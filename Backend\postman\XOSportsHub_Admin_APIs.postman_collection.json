{"info": {"name": "XOSportsHub Admin APIs", "description": "Comprehensive collection of admin panel APIs for XOSportsHub platform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:5000/api", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "response": []}]}, {"name": "Dashboard", "item": [{"name": "Get Dashboard Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/dashboard/stats", "host": ["{{base_url}}"], "path": ["admin", "dashboard", "stats"]}}, "response": []}, {"name": "Get Recent Activity", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/dashboard/activity?limit=20", "host": ["{{base_url}}"], "path": ["admin", "dashboard", "activity"], "query": [{"key": "limit", "value": "20"}]}}, "response": []}, {"name": "Get Pending Approvals", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/dashboard/pending-approvals", "host": ["{{base_url}}"], "path": ["admin", "dashboard", "pending-approvals"]}}, "response": []}, {"name": "Get Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/dashboard/analytics?period=6m", "host": ["{{base_url}}"], "path": ["admin", "dashboard", "analytics"], "query": [{"key": "period", "value": "6m", "description": "7d, 30d, 6m, 1y"}]}}, "response": []}, {"name": "Get Top Performers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/dashboard/top-performers", "host": ["{{base_url}}"], "path": ["admin", "dashboard", "top-performers"]}}, "response": []}, {"name": "Get System Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/dashboard/system-health", "host": ["{{base_url}}"], "path": ["admin", "dashboard", "system-health"]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/users?page=1&limit=10&search=&role=&status=&sortBy=createdAt&sortOrder=desc", "host": ["{{base_url}}"], "path": ["admin", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": ""}, {"key": "role", "value": "", "description": "buyer, seller, admin"}, {"key": "status", "value": "", "description": "active, inactive, suspended"}, {"key": "sortBy", "value": "createdAt"}, {"key": "sortOrder", "value": "desc"}]}}, "response": []}, {"name": "Get User Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/users/stats", "host": ["{{base_url}}"], "path": ["admin", "users", "stats"]}}, "response": []}, {"name": "Export Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/users/export?format=json&role=&status=", "host": ["{{base_url}}"], "path": ["admin", "users", "export"], "query": [{"key": "format", "value": "json", "description": "json, csv"}, {"key": "role", "value": ""}, {"key": "status", "value": ""}]}}, "response": []}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"email\": \"<EMAIL>\",\n  \"mobile\": \"+1234567890\",\n  \"role\": \"buyer\"\n}"}, "url": {"raw": "{{base_url}}/admin/users", "host": ["{{base_url}}"], "path": ["admin", "users"]}}, "response": []}, {"name": "Get User by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}"]}}, "response": []}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON>\",\n  \"status\": \"active\",\n  \"isVerified\": true\n}"}, "url": {"raw": "{{base_url}}/admin/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}"]}}, "response": []}, {"name": "Delete User", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}"]}}, "response": []}, {"name": "Bulk Update Users", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userIds\": [\"user_id_1\", \"user_id_2\"],\n  \"action\": \"activate\",\n  \"data\": {}\n}"}, "url": {"raw": "{{base_url}}/admin/users/bulk-update", "host": ["{{base_url}}"], "path": ["admin", "users", "bulk-update"]}}, "response": []}, {"name": "Bulk Delete Users", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userIds\": [\"user_id_1\", \"user_id_2\"]\n}"}, "url": {"raw": "{{base_url}}/admin/users/bulk-delete", "host": ["{{base_url}}"], "path": ["admin", "users", "bulk-delete"]}}, "response": []}, {"name": "Toggle User Status", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}/toggle-status", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}", "toggle-status"]}}, "response": []}, {"name": "Verify User", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}/verify", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}", "verify"]}}, "response": []}, {"name": "Suspend User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Violation of terms of service\"\n}"}, "url": {"raw": "{{base_url}}/admin/users/{{user_id}}/suspend", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}", "suspend"]}}, "response": []}, {"name": "Unsuspend User", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}/unsuspend", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}", "unsuspend"]}}, "response": []}, {"name": "Get User Activity History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}/activity?page=1&limit=20", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}", "activity"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}, "response": []}]}, {"name": "Content Management", "item": [{"name": "Get All Content", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/content?page=1&limit=10&search=&status=&contentType=&sport=&sortBy=createdAt&sortOrder=desc", "host": ["{{base_url}}"], "path": ["admin", "content"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": ""}, {"key": "status", "value": "", "description": "Draft, Under Review, Published, Rejected, Archived"}, {"key": "contentType", "value": ""}, {"key": "sport", "value": ""}, {"key": "sortBy", "value": "createdAt"}, {"key": "sortOrder", "value": "desc"}]}}, "response": []}, {"name": "Get Content Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/content/stats", "host": ["{{base_url}}"], "path": ["admin", "content", "stats"]}}, "response": []}, {"name": "Export Content", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/content/export?format=json&status=&contentType=&sport=", "host": ["{{base_url}}"], "path": ["admin", "content", "export"], "query": [{"key": "format", "value": "json"}, {"key": "status", "value": ""}, {"key": "contentType", "value": ""}, {"key": "sport", "value": ""}]}}, "response": []}, {"name": "Get Content by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/content/{{content_id}}", "host": ["{{base_url}}"], "path": ["admin", "content", "{{content_id}}"]}}, "response": []}, {"name": "Approve Content", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"approvalNotes\": \"Content meets all quality standards\"\n}"}, "url": {"raw": "{{base_url}}/admin/content/{{content_id}}/approve", "host": ["{{base_url}}"], "path": ["admin", "content", "{{content_id}}", "approve"]}}, "response": []}, {"name": "Reject Content", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Poor quality\",\n  \"rejectionNotes\": \"Content does not meet minimum quality standards\"\n}"}, "url": {"raw": "{{base_url}}/admin/content/{{content_id}}/reject", "host": ["{{base_url}}"], "path": ["admin", "content", "{{content_id}}", "reject"]}}, "response": []}, {"name": "Delete Content", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/admin/content/{{content_id}}", "host": ["{{base_url}}"], "path": ["admin", "content", "{{content_id}}"]}}, "response": []}, {"name": "Bulk Approve Content", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contentIds\": [\"content_id_1\", \"content_id_2\"]\n}"}, "url": {"raw": "{{base_url}}/admin/content/bulk-approve", "host": ["{{base_url}}"], "path": ["admin", "content", "bulk-approve"]}}, "response": []}, {"name": "Bulk Reject Content", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contentIds\": [\"content_id_1\", \"content_id_2\"],\n  \"reason\": \"Quality issues\"\n}"}, "url": {"raw": "{{base_url}}/admin/content/bulk-reject", "host": ["{{base_url}}"], "path": ["admin", "content", "bulk-reject"]}}, "response": []}, {"name": "Bulk Delete Content", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contentIds\": [\"content_id_1\", \"content_id_2\"]\n}"}, "url": {"raw": "{{base_url}}/admin/content/bulk-delete", "host": ["{{base_url}}"], "path": ["admin", "content", "bulk-delete"]}}, "response": []}, {"name": "Update Content Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"Published\"\n}"}, "url": {"raw": "{{base_url}}/admin/content/{{content_id}}/status", "host": ["{{base_url}}"], "path": ["admin", "content", "{{content_id}}", "status"]}}, "response": []}, {"name": "Moderate Content", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"flag\",\n  \"reason\": \"Inappropriate content\"\n}"}, "url": {"raw": "{{base_url}}/admin/content/{{content_id}}/moderate", "host": ["{{base_url}}"], "path": ["admin", "content", "{{content_id}}", "moderate"]}}, "response": []}, {"name": "Feature Content", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/admin/content/{{content_id}}/feature", "host": ["{{base_url}}"], "path": ["admin", "content", "{{content_id}}", "feature"]}}, "response": []}, {"name": "Unfeature Content", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/admin/content/{{content_id}}/unfeature", "host": ["{{base_url}}"], "path": ["admin", "content", "{{content_id}}", "unfeature"]}}, "response": []}, {"name": "Get Content Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/content/{{content_id}}/reviews?page=1&limit=10", "host": ["{{base_url}}"], "path": ["admin", "content", "{{content_id}}", "reviews"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}]}]}