import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectContent,
  selectSelectedContent,
  selectUI,
  setSelectedContent,
  showContentDetailModal,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchContent,
  deleteContent,
  approveContent,
  rejectContent,
  updateContentStatus,
} from "../../redux/slices/adminDashboardThunks";
import AdminLayout from "../../components/admin/AdminLayout";
import ContentDetailModal from "../../components/admin/ContentDetailModal";
import "../../styles/AdminContentManagement.css";
import { IMAGE_BASE_URL } from "../../utils/constants";

// Icons
import {
  FaVideo,
  FaSearch,
  FaFilter,
  FaEye,
  FaEdit,
  FaTrash,
  FaTog<PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON>,
  FaGavel,
  Fa<PERSON>lock,
  <PERSON>a<PERSON>lag,
  FaDownload
} from "react-icons/fa";
import { MdAdd } from "react-icons/md";

const AdminContentManagement = () => {
  const dispatch = useDispatch();
  const content = useSelector(selectContent);
  const selectedContent = useSelector(selectSelectedContent);
  const ui = useSelector(selectUI);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");

  // Fetch content on component mount
  useEffect(() => {
    dispatch(fetchContent());
  }, [dispatch]);

  // Get unique categories from content
  const uniqueCategories = React.useMemo(() => {
    const categories = new Set((content.data || []).map(item => item?.category || 'Uncategorized'));
    return Array.from(categories).filter(Boolean);
  }, [content.data]);

  // Filter content based on search and filters
  const filteredContent = (content.data || []).filter(item => {
    // Safely access properties with fallbacks
    const title = item?.title || '';
    const seller = item?.seller?.fullName || item?.seller?.firstName + ' ' + item?.seller?.lastName || 'Unknown';
    const category = item?.category || '';
    const status = item?.status || '';

    const matchesSearch =
      title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      seller.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = categoryFilter === "all" || category === categoryFilter;
    const matchesStatus = statusFilter === "all" || status === statusFilter;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Handle select all
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      dispatch(setSelectedContent(filteredContent.map(item => item.id)));
    } else {
      dispatch(setSelectedContent([]));
    }
  };

  // Handle individual select
  const handleSelectContent = (contentId) => {
    const newSelection = selectedContent.includes(contentId)
      ? selectedContent.filter(id => id !== contentId)
      : [...selectedContent, contentId];
    dispatch(setSelectedContent(newSelection));
  };

  // Handle content actions
  const handleContentAction = async (contentItem, action) => {
    switch (action) {
      case 'view':
        // Create a complete version of the content item with all properties from API response
        const cleanContentItem = {
          // Basic properties
          id: contentItem.id || contentItem._id,
          _id: contentItem._id,
          title: contentItem.title || 'Untitled',
          description: contentItem.description || '',
          sport: contentItem.sport || '',
          contentType: contentItem.contentType || '',
          category: contentItem.category || 'Uncategorized',
          difficulty: contentItem.difficulty || '',
          coachName: contentItem.coachName || '',
          aboutCoach: contentItem.aboutCoach || '',
          strategicContent: contentItem.strategicContent || '',
          price: contentItem.price || 0,
          status: contentItem.status || 'draft',
          saleType: contentItem.saleType || 'Fixed',

          // File and media properties
          fileUrl: contentItem.fileUrl || '',
          previewUrl: contentItem.previewUrl || '',
          previewStatus: contentItem.previewStatus || '',
          thumbnailUrl: contentItem.thumbnailUrl || '',
          duration: contentItem.duration,
          fileSize: contentItem.fileSize || 0,
          videoLength: contentItem.videoLength,

          // Content metadata
          tags: contentItem.tags || [],
          language: contentItem.language || 'English',
          prerequisites: contentItem.prerequisites || [],
          learningObjectives: contentItem.learningObjectives || [],
          equipment: contentItem.equipment || [],

          // Seller information
          seller: {
            _id: contentItem.seller?._id || '',
            fullName: contentItem.seller?.fullName || '',
            firstName: contentItem.seller?.firstName || '',
            lastName: contentItem.seller?.lastName || '',
            email: contentItem.seller?.email || '',
            id: contentItem.seller?.id || contentItem.seller?._id || ''
          },

          // Status and visibility
          visibility: contentItem.visibility || 'Public',
          isActive: contentItem.isActive,
          allowCustomRequests: contentItem.allowCustomRequests || false,
          customRequestPrice: contentItem.customRequestPrice,
          isCustomContent: contentItem.isCustomContent || false,

          // Sales and auction data
          isSold: contentItem.isSold || false,
          auctionStatus: contentItem.auctionStatus || '',
          salesCount: contentItem.salesCount || 0,
          totalRevenue: contentItem.totalRevenue || 0,
          averageRating: contentItem.averageRating || 0,
          reviewCount: contentItem.reviewCount || 0,

          // Auction details object
          auctionDetails: {
            basePrice: contentItem.auctionDetails?.basePrice || null,
            minimumBidIncrement: contentItem.auctionDetails?.minimumBidIncrement || null,
            auctionStartDate: contentItem.auctionDetails?.auctionStartDate || null,
            auctionEndDate: contentItem.auctionDetails?.auctionEndDate || null,
            allowOfferBeforeAuctionStart: contentItem.auctionDetails?.allowOfferBeforeAuctionStart || false,
            endTime: contentItem.auctionDetails?.endTime || null
          },

          // Auction winning details
          winningBidId: contentItem.winningBidId || null,
          winningOfferId: contentItem.winningOfferId || null,
          auctionEndedAt: contentItem.auctionEndedAt || null,
          soldAt: contentItem.soldAt || null,

          // Timestamps
          uploadDate: contentItem.uploadDate || contentItem.createdAt || new Date(),
          createdAt: contentItem.createdAt || new Date(),
          publishedDate: contentItem.publishedDate || null,
          lastUpdated: contentItem.lastUpdated || null,

          // Status history
          statusHistory: contentItem.statusHistory || [],

          // Additional properties
          __v: contentItem.__v || 0,

          // Legacy properties for compatibility
          thumbnail: contentItem.thumbnailUrl || contentItem.thumbnail || null,
          views: contentItem.views || 0,
          isAuction: contentItem.saleType === 'Auction',
          auctionStartDate: contentItem.auctionDetails?.auctionStartDate || null,
          auctionEndDate: contentItem.auctionDetails?.auctionEndDate || null,
          startingBid: contentItem.auctionDetails?.basePrice || 0,
          currentBid: contentItem.currentBid || 0,
          minBidIncrement: contentItem.auctionDetails?.minimumBidIncrement || 0,
          highestBidder: contentItem.highestBidder ? {
            fullName: contentItem.highestBidder.fullName || '',
            firstName: contentItem.highestBidder.firstName || '',
            lastName: contentItem.highestBidder.lastName || ''
          } : null
        };
        dispatch(showContentDetailModal({ ...cleanContentItem, viewOnly: true }));
        console.log('Modal should be visible now');
        break;
      case 'edit':
        dispatch(showContentDetailModal(contentItem));
        break;
      case 'published':
        if (window.confirm(`Publish "${contentItem.title}"?`)) {
          try {
            await dispatch(approveContent({
              id: contentItem.id,
              approvalNotes: 'Individual approval'
            })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'content_approval',
              description: `Content published: ${contentItem.title}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`Content "${contentItem.title}" has been published!`);
          } catch (error) {
            console.error('Failed to publish content:', error);
            alert('Failed to publish content. Please try again.');
          }
        }
        break;
      case 'draft':
        const reason = prompt(`Reason for setting "${contentItem.title}" to draft:`);
        if (reason) {
          try {
            await dispatch(rejectContent({
              id: contentItem.id,
              reason,
              rejectionNotes: reason
            })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'content_rejection',
              description: `Content set to draft: ${contentItem.title} - Reason: ${reason}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`Content "${contentItem.title}" has been set to draft.`);
          } catch (error) {
            console.error('Failed to set content to draft:', error);
            alert('Failed to set content to draft. Please try again.');
          }
        }
        break;
      case 'delete':
        if (window.confirm(`Delete "${contentItem.title}"? This action cannot be undone.`)) {
          try {
            await dispatch(deleteContent(contentItem.id)).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'content_deletion',
              description: `Content deleted: ${contentItem.title}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`Content "${contentItem.title}" has been deleted!`);
          } catch (error) {
            console.error('Failed to delete content:', error);
            alert('Failed to delete content. Please try again.');
          }
        }
        break;
      default:
        break;
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action) => {
    if (selectedContent.length === 0) {
      alert('Please select content first');
      return;
    }

    switch (action) {
      case 'Published':
        if (window.confirm(`Publish ${selectedContent.length} selected items?`)) {
          try {
            await Promise.all(selectedContent.map(id =>
              dispatch(approveContent({ id, approvalNotes: 'Bulk approval' })).unwrap()
            ));
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_content_approval',
              description: `Bulk published ${selectedContent.length} content items`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`${selectedContent.length} items published`);
            dispatch(setSelectedContent([]));
          } catch (error) {
            console.error('Failed to publish content:', error);
            alert('Failed to publish some content items. Please try again.');
          }
        }
        break;
      case 'draft':
        const reason = prompt(`Reason for setting ${selectedContent.length} items to draft:`);
        if (reason) {
          try {
            await Promise.all(selectedContent.map(id =>
              dispatch(rejectContent({ id, reason, rejectionNotes: reason })).unwrap()
            ));
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_content_rejection',
              description: `Bulk set to draft ${selectedContent.length} content items - Reason: ${reason}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`${selectedContent.length} items set to draft`);
            dispatch(setSelectedContent([]));
          } catch (error) {
            console.error('Failed to set content to draft:', error);
            alert('Failed to set some content items to draft. Please try again.');
          }
        }
        break;
      case 'delete':
        if (window.confirm(`Delete ${selectedContent.length} selected items? This action cannot be undone.`)) {
          try {
            await Promise.all(selectedContent.map(id =>
              dispatch(deleteContent(id)).unwrap()
            ));
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_content_deletion',
              description: `Bulk deleted ${selectedContent.length} content items`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`${selectedContent.length} items deleted`);
            dispatch(setSelectedContent([]));
          } catch (error) {
            console.error('Failed to delete content:', error);
            alert('Failed to delete some content items. Please try again.');
          }
        }
        break;
      default:
        break;
    }
  };

  const handleStatusToggle = async (contentItem, e) => {
    e.stopPropagation(); // Prevent row click event
    try {
      const newStatus = contentItem.status === 'Published' ? 'Draft' : 'Published';
      await dispatch(updateContentStatus({
        id: contentItem.id,
        status: newStatus,
        reason: `Status changed to ${newStatus} by admin`
      })).unwrap();

      dispatch(addActivity({
        id: Date.now(),
        type: 'content_status_update',
        description: `Content status updated: ${contentItem.title} - ${newStatus}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));
    } catch (error) {
      console.error('Failed to update content status:', error);
      alert('Failed to update content status. Please try again.');
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (item) => {
    // if (item.isSold) return <span className="status-badge status-sold">Sold</span>;

    return (
      <div className="status-controls">
        <span className={`status-badge status-${item.status.toLowerCase()}`}>
          {item.status}
        </span>

        <button
          className="btn-action toggle"
          onClick={(e) => handleStatusToggle(item, e)}
          disabled={item.isSold}
          title={item.status === 'Published' ? 'Unpublish Content' : 'Publish Content'}
        >
          {item.status === 'Published' ? <FaToggleOn /> : <FaToggleOff />}
        </button>

      </div>
    );
  };

  // Get category badge class
  const getCategoryBadge = (category) => {
    switch (category) {
      case 'Training Videos':
        return 'category-badge training';
      case 'Courses':
        return 'category-badge courses';
      case 'E-books':
        return 'category-badge ebooks';
      case 'Live Sessions':
        return 'category-badge live';
      default:
        return 'category-badge';
    }
  };

  return (
    <AdminLayout>
      <div className="AdminContentManagement">
        {/* Header Actions */}
        <div className="AdminContentManagement__header">
          <div className="header-left">
            <div className="search-container">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search content by title or seller..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="AdminContentManagement__filters">
          <div className="filter-group">
            <FaFilter className="filter-icon" />
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Categories</option>
              {uniqueCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Status</option>
              <option value="Published">Published</option>
              <option value="Draft">Draft</option>
            </select>
          </div>

          {selectedContent.length > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">
                {selectedContent.length} selected
              </span>
              <button
                className="btn btn-success"
                onClick={() => handleBulkAction('Published')}
              >
                <FaCheck />
                Approve
              </button>
              <button
                className="btn btn-warning"
                onClick={() => handleBulkAction('draft')}
              >
                <FaTimes />
                Reject
              </button>
              <button
                className="btn btn-danger"
                onClick={() => handleBulkAction('delete')}
              >
                Delete
              </button>
            </div>
          )}
        </div>

        {/* Content Table */}
        <div className="AdminContentManagement__table">
          <div className="table-container">
            <table className="content-table">
              <thead>
                <tr>
                  <th>
                    <input
                      type="checkbox"
                      onChange={handleSelectAll}
                      checked={selectedContent.length === filteredContent.length && filteredContent.length > 0}
                    />
                  </th>
                  <th>Content</th>
                  <th>Seller</th>
                  <th>Category</th>
                  <th>Price</th>
                  <th>Status</th>
                  <th>Upload Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredContent.map((item) => (
                  <tr key={item?.id || Math.random()}>
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedContent.includes(item?.id)}
                        onChange={() => handleSelectContent(item?.id)}
                      />
                    </td>
                    <td>
                      <div className="content-info">
                        <div className="content-thumbnail">
                          {item?.thumbnailUrl ? (
                            <img src={IMAGE_BASE_URL + item.thumbnailUrl} alt={item?.title || 'Content'} />
                          ) : (
                            <FaVideo />
                          )}
                        </div>
                        <div className="content-details">
                          <span className="content-title">{item?.title || 'Untitled'}</span>
                        </div>
                      </div>
                    </td>
                    <td>
                      {item?.seller?.fullName ||
                        `${item?.seller?.firstName || ''} ${item?.seller?.lastName || ''}`.trim() ||
                        'Unknown'}
                    </td>
                    <td>
                      <span className={getCategoryBadge(item?.category)}>
                        {item?.category || 'Uncategorized'}
                      </span>
                    </td>
                    <td>{formatCurrency(item?.price || 0)}</td>
                    <td>
                      {getStatusBadge(item)}
                    </td>
                    <td>{formatDate(item?.uploadDate || new Date())}</td>
                    <td>
                      <div className="table-actions">
                        <button
                          className="btn-action view"
                          title="View Content"
                          onClick={() => handleContentAction(item, 'view')}
                        >
                          <FaEye />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredContent.length === 0 && (
            <div className="no-results">
              <FaVideo className="no-results-icon" />
              <h3>No content found</h3>
              <p>Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="AdminContentManagement__pagination">
          <div className="pagination-info">
            Showing {filteredContent.length} of {content.data?.length || 0} content items
          </div>
          <div className="pagination-controls">
            <button className="btn btn-outline" disabled>Previous</button>
            <span className="page-number active">1</span>
            <button className="btn btn-outline" disabled>Next</button>
          </div>
        </div>
      </div>
      <ContentDetailModal />
    </AdminLayout>
  );
};

export default AdminContentManagement;
