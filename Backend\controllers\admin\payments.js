const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const Payment = require('../../models/Payment');
const Order = require('../../models/Order');
const User = require('../../models/User');

// @desc    Get all payments with filtering, sorting, and pagination
// @route   GET /api/admin/payments
// @access  Private/Admin
exports.getAllPayments = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      method = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom = '',
      dateTo = '',
      minAmount = '',
      maxAmount = '',
      buyerId = '',
      sellerId = ''
    } = req.query;

    // Build query
    let query = {};

    // Search functionality
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      const users = await User.find({
        $or: [
          { firstName: searchRegex },
          { lastName: searchRegex },
          { email: searchRegex }
        ]
      }).select('_id');

      const userIds = users.map(user => user._id);

      query.$or = [
        { buyer: { $in: userIds } },
        { seller: { $in: userIds } },
        { stripePaymentIntentId: searchRegex }
      ];
    }

    // Filter by status
    if (status) {
      query.status = status;
    }

    // Filter by payment method
    if (method) {
      query.paymentMethod = method;
    }

    // Filter by buyer
    if (buyerId) {
      query.buyer = buyerId;
    }

    // Filter by seller
    if (sellerId) {
      query.seller = sellerId;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) {
        query.createdAt.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        query.createdAt.$lte = new Date(dateTo);
      }
    }

    // Amount range filter
    if (minAmount || maxAmount) {
      query.amount = {};
      if (minAmount) {
        query.amount.$gte = parseFloat(minAmount);
      }
      if (maxAmount) {
        query.amount.$lte = parseFloat(maxAmount);
      }
    }

    // Sorting
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const payments = await Payment.find(query)
      .populate('buyer', 'firstName lastName email profileImage')
      .populate('seller', 'firstName lastName email profileImage')
      .populate('orderId', 'orderType totalAmount status')
      .sort(sortOptions)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    // Get total count for pagination
    const total = await Payment.countDocuments(query);

    res.status(200).json({
      success: true,
      data: payments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get payment by ID
// @route   GET /api/admin/payments/:id
// @access  Private/Admin
exports.getPaymentById = async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id)
      .populate('buyer', 'firstName lastName email profileImage mobile phone')
      .populate('seller', 'firstName lastName email profileImage mobile phone')
      .populate('orderId', 'orderType totalAmount status content bidId customRequestId');

    if (!payment) {
      return next(new ErrorResponse('Payment not found', 404));
    }

    res.status(200).json({
      success: true,
      data: payment
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update payment status
// @route   PUT /api/admin/payments/:id/status
// @access  Private/Admin
exports.updatePaymentStatus = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { status, notes } = req.body;

    const payment = await Payment.findById(req.params.id);
    if (!payment) {
      return next(new ErrorResponse('Payment not found', 404));
    }

    const oldStatus = payment.status;
    payment.status = status;

    // Add status change to payment history if notes provided
    if (notes) {
      if (!payment.statusHistory) {
        payment.statusHistory = [];
      }
      payment.statusHistory.push({
        status: status,
        notes: notes,
        changedBy: req.user.id,
        changedAt: new Date()
      });
    }

    await payment.save();

    res.status(200).json({
      success: true,
      data: payment,
      message: `Payment status updated from ${oldStatus} to ${status}`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get payment statistics
// @route   GET /api/admin/payments/stats
// @access  Private/Admin
exports.getPaymentStats = async (req, res, next) => {
  try {
    const totalPayments = await Payment.countDocuments();
    const pendingPayments = await Payment.countDocuments({ status: 'Pending' });
    const completedPayments = await Payment.countDocuments({ status: 'Completed' });
    const failedPayments = await Payment.countDocuments({ status: 'Failed' });
    const refundedPayments = await Payment.countDocuments({ status: 'Refunded' });

    // Revenue stats
    const revenueStats = await Payment.aggregate([
      { $match: { status: 'Completed' } },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          totalPlatformFees: { $sum: '$platformFee' },
          totalSellerEarnings: { $sum: '$sellerEarnings' },
          averagePaymentAmount: { $avg: '$amount' }
        }
      }
    ]);

    // Payment method stats
    const methodStats = await Payment.aggregate([
      {
        $group: {
          _id: '$paymentMethod',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Monthly stats for current year
    const currentYear = new Date().getFullYear();
    const monthlyStats = await Payment.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(`${currentYear}-01-01`),
            $lte: new Date(`${currentYear}-12-31`)
          },
          status: 'Completed'
        }
      },
      {
        $group: {
          _id: { $month: '$createdAt' },
          count: { $sum: 1 },
          revenue: { $sum: '$amount' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Failed payment reasons
    const failureReasons = await Payment.aggregate([
      { $match: { status: 'Failed' } },
      {
        $group: {
          _id: '$failureReason',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.status(200).json({
      success: true,
      data: {
        overview: {
          total: totalPayments,
          pending: pendingPayments,
          completed: completedPayments,
          failed: failedPayments,
          refunded: refundedPayments
        },
        revenue: revenueStats[0] || {
          totalRevenue: 0,
          totalPlatformFees: 0,
          totalSellerEarnings: 0,
          averagePaymentAmount: 0
        },
        paymentMethods: methodStats,
        monthlyStats,
        failureReasons
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Process refund for payment
// @route   POST /api/admin/payments/:id/refund
// @access  Private/Admin
exports.processRefund = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { amount, reason } = req.body;
    const payment = await Payment.findById(req.params.id);

    if (!payment) {
      return next(new ErrorResponse('Payment not found', 404));
    }

    if (payment.status !== 'Completed') {
      return next(new ErrorResponse('Cannot refund payment that is not completed', 400));
    }

    if (amount > payment.amount) {
      return next(new ErrorResponse('Refund amount cannot exceed payment amount', 400));
    }

    // Update payment status
    payment.status = 'Refunded';
    payment.refundAmount = amount;
    payment.refundReason = reason;
    payment.refundedAt = new Date();
    payment.refundedBy = req.user.id;

    await payment.save();

    res.status(200).json({
      success: true,
      data: payment,
      message: `Refund of $${amount} processed successfully`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk update payments
// @route   POST /api/admin/payments/bulk-update
// @access  Private/Admin
exports.bulkUpdatePayments = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { paymentIds, status } = req.body;

    const result = await Payment.updateMany(
      { _id: { $in: paymentIds } },
      { status: status }
    );

    res.status(200).json({
      success: true,
      data: {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount
      },
      message: `${result.modifiedCount} payments updated to ${status}`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export payments data
// @route   GET /api/admin/payments/export
// @access  Private/Admin
exports.exportPayments = async (req, res, next) => {
  try {
    const {
      format = 'csv',
      status = '',
      dateFrom = '',
      dateTo = ''
    } = req.query;

    // Build query for export
    let query = {};
    if (status) query.status = status;
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
      if (dateTo) query.createdAt.$lte = new Date(dateTo);
    }

    const payments = await Payment.find(query)
      .populate('buyer', 'firstName lastName email')
      .populate('seller', 'firstName lastName email')
      .populate('orderId', 'orderType')
      .sort('-createdAt');

    // Format data for export
    const exportData = payments.map(payment => ({
      paymentId: payment._id,
      orderId: payment.orderId?._id || 'N/A',
      orderType: payment.orderId?.orderType || 'N/A',
      buyerName: payment.buyer ? `${payment.buyer.firstName} ${payment.buyer.lastName}` : 'N/A',
      buyerEmail: payment.buyer?.email || 'N/A',
      sellerName: payment.seller ? `${payment.seller.firstName} ${payment.seller.lastName}` : 'N/A',
      sellerEmail: payment.seller?.email || 'N/A',
      amount: payment.amount,
      platformFee: payment.platformFee,
      sellerEarnings: payment.sellerEarnings,
      paymentMethod: payment.paymentMethod,
      status: payment.status,
      stripePaymentIntentId: payment.stripePaymentIntentId,
      createdAt: payment.createdAt,
      completedAt: payment.completedAt
    }));

    if (format === 'json') {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=payments.json');
      return res.json(exportData);
    }

    // Default CSV format
    const csv = [
      // CSV headers
      'Payment ID,Order ID,Order Type,Buyer Name,Buyer Email,Seller Name,Seller Email,Amount,Platform Fee,Seller Earnings,Payment Method,Status,Stripe Payment Intent ID,Created At,Completed At',
      // CSV data
      ...exportData.map(payment =>
        `${payment.paymentId},${payment.orderId},${payment.orderType},"${payment.buyerName}",${payment.buyerEmail},"${payment.sellerName}",${payment.sellerEmail},${payment.amount},${payment.platformFee},${payment.sellerEarnings},${payment.paymentMethod},${payment.status},${payment.stripePaymentIntentId},${payment.createdAt},${payment.completedAt || ''}`
      )
    ].join('\n');

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=payments.csv');
    res.send(csv);
  } catch (err) {
    next(err);
  }
};

// @desc    Get payment analytics
// @route   GET /api/admin/payments/analytics
// @access  Private/Admin
exports.getPaymentAnalytics = async (req, res, next) => {
  try {
    const { period = '30d', groupBy = 'day' } = req.query;

    // Calculate date range
    let startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate = new Date('2020-01-01'); // All time
    }

    // Group by format
    let groupFormat;
    switch (groupBy) {
      case 'day':
        groupFormat = { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } };
        break;
      case 'week':
        groupFormat = { $dateToString: { format: "%Y-W%U", date: "$createdAt" } };
        break;
      case 'month':
        groupFormat = { $dateToString: { format: "%Y-%m", date: "$createdAt" } };
        break;
      default:
        groupFormat = { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } };
    }

    const analytics = await Payment.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: groupFormat,
          totalPayments: { $sum: 1 },
          totalRevenue: { $sum: '$amount' },
          completedPayments: {
            $sum: { $cond: [{ $eq: ['$status', 'Completed'] }, 1, 0] }
          },
          failedPayments: {
            $sum: { $cond: [{ $eq: ['$status', 'Failed'] }, 1, 0] }
          },
          averagePaymentAmount: { $avg: '$amount' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get failed payments
// @route   GET /api/admin/payments/failed
// @access  Private/Admin
exports.getFailedPayments = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    const failedPayments = await Payment.find({ status: 'Failed' })
      .populate('buyer', 'firstName lastName email')
      .populate('seller', 'firstName lastName email')
      .populate('orderId', 'orderType totalAmount')
      .sort('-createdAt')
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Payment.countDocuments({ status: 'Failed' });

    res.status(200).json({
      success: true,
      data: failedPayments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Retry failed payment
// @route   POST /api/admin/payments/:id/retry
// @access  Private/Admin
exports.retryPayment = async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id);

    if (!payment) {
      return next(new ErrorResponse('Payment not found', 404));
    }

    if (payment.status !== 'Failed') {
      return next(new ErrorResponse('Can only retry failed payments', 400));
    }

    // Reset payment status to pending for retry
    payment.status = 'Pending';
    payment.retryCount = (payment.retryCount || 0) + 1;
    payment.lastRetryAt = new Date();

    await payment.save();

    res.status(200).json({
      success: true,
      data: payment,
      message: 'Payment retry initiated'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Flag payment
// @route   PUT /api/admin/payments/:id/flag
// @access  Private/Admin
exports.flagPayment = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reason } = req.body;
    const payment = await Payment.findById(req.params.id);

    if (!payment) {
      return next(new ErrorResponse('Payment not found', 404));
    }

    payment.isFlagged = true;
    payment.flagReason = reason;
    payment.flaggedBy = req.user.id;
    payment.flaggedAt = new Date();

    await payment.save();

    res.status(200).json({
      success: true,
      data: payment,
      message: 'Payment flagged successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Unflag payment
// @route   PUT /api/admin/payments/:id/unflag
// @access  Private/Admin
exports.unflagPayment = async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id);

    if (!payment) {
      return next(new ErrorResponse('Payment not found', 404));
    }

    payment.isFlagged = false;
    payment.flagReason = undefined;
    payment.flaggedBy = undefined;
    payment.flaggedAt = undefined;
    payment.unflaggedBy = req.user.id;
    payment.unflaggedAt = new Date();

    await payment.save();

    res.status(200).json({
      success: true,
      data: payment,
      message: 'Payment unflagged successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get payment disputes
// @route   GET /api/admin/payments/disputes
// @access  Private/Admin
exports.getPaymentDisputes = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    const disputes = await Payment.find({
      $or: [
        { isDisputed: true },
        { isFlagged: true }
      ]
    })
      .populate('buyer', 'firstName lastName email')
      .populate('seller', 'firstName lastName email')
      .populate('orderId', 'orderType totalAmount')
      .sort('-createdAt')
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Payment.countDocuments({
      $or: [
        { isDisputed: true },
        { isFlagged: true }
      ]
    });

    res.status(200).json({
      success: true,
      data: disputes,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Resolve payment dispute
// @route   POST /api/admin/payments/:id/resolve-dispute
// @access  Private/Admin
exports.resolveDispute = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { resolution, refundAmount } = req.body;
    const payment = await Payment.findById(req.params.id);

    if (!payment) {
      return next(new ErrorResponse('Payment not found', 404));
    }

    payment.isDisputed = false;
    payment.disputeResolution = resolution;
    payment.disputeResolvedBy = req.user.id;
    payment.disputeResolvedAt = new Date();

    if (refundAmount && refundAmount > 0) {
      payment.status = 'Refunded';
      payment.refundAmount = refundAmount;
      payment.refundReason = 'Dispute resolution';
      payment.refundedAt = new Date();
      payment.refundedBy = req.user.id;
    }

    await payment.save();

    res.status(200).json({
      success: true,
      data: payment,
      message: 'Payment dispute resolved successfully'
    });
  } catch (err) {
    next(err);
  }
};
