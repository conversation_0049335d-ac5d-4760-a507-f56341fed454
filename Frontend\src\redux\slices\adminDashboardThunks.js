import { createAsyncThunk } from '@reduxjs/toolkit';
import adminDashboardService from '../../services/admin/adminDashboardService';
import adminUserService from '../../services/admin/adminUserService';
import adminContentService from '../../services/admin/adminContentService';
import adminBidService from '../../services/admin/adminBidService';
import adminOfferService from '../../services/admin/adminOfferService';

// Dashboard Thunks
export const fetchDashboardStats = createAsyncThunk(
  'adminDashboard/fetchDashboardStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await adminDashboardService.getDashboardStats();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch dashboard stats');
    }
  }
);

export const fetchRecentActivity = createAsyncThunk(
  'adminDashboard/fetchRecentActivity',
  async (limit = 20, { rejectWithValue }) => {
    try {
      const response = await adminDashboardService.getRecentActivity(limit);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch recent activity');
    }
  }
);

export const fetchPendingApprovals = createAsyncThunk(
  'adminDashboard/fetchPendingApprovals',
  async (_, { rejectWithValue }) => {
    try {
      const response = await adminDashboardService.getPendingApprovals();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch pending approvals');
    }
  }
);

export const fetchAnalytics = createAsyncThunk(
  'adminDashboard/fetchAnalytics',
  async (period = '6m', { rejectWithValue }) => {
    try {
      const response = await adminDashboardService.getAnalytics(period);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch analytics');
    }
  }
);

export const fetchTopPerformers = createAsyncThunk(
  'adminDashboard/fetchTopPerformers',
  async (_, { rejectWithValue }) => {
    try {
      const response = await adminDashboardService.getTopPerformers();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch top performers');
    }
  }
);

export const fetchSystemHealth = createAsyncThunk(
  'adminDashboard/fetchSystemHealth',
  async (_, { rejectWithValue }) => {
    try {
      const response = await adminDashboardService.getSystemHealth();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch system health');
    }
  }
);

// User Management Thunks
export const fetchUsers = createAsyncThunk(
  'adminDashboard/fetchUsers',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await adminUserService.getAllUsers(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch users');
    }
  }
);

export const fetchUserById = createAsyncThunk(
  'adminDashboard/fetchUserById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await adminUserService.getUserById(id);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch user');
    }
  }
);

export const createUser = createAsyncThunk(
  'adminDashboard/createUser',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await adminUserService.createUser(userData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to create user');
    }
  }
);

export const updateUser = createAsyncThunk(
  'adminDashboard/updateUser',
  async ({ id, userData }, { rejectWithValue }) => {
    try {
      const response = await adminUserService.updateUser(id, userData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to update user');
    }
  }
);

export const deleteUser = createAsyncThunk(
  'adminDashboard/deleteUser',
  async (id, { rejectWithValue }) => {
    try {
      await adminUserService.deleteUser(id);
      return id;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to delete user');
    }
  }
);

export const bulkUpdateUsers = createAsyncThunk(
  'adminDashboard/bulkUpdateUsers',
  async ({ userIds, action, data }, { rejectWithValue }) => {
    try {
      const response = await adminUserService.bulkUpdateUsers(userIds, action, data);
      return { userIds, action, data, result: response };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to bulk update users');
    }
  }
);

export const bulkDeleteUsers = createAsyncThunk(
  'adminDashboard/bulkDeleteUsers',
  async (userIds, { rejectWithValue }) => {
    try {
      const response = await adminUserService.bulkDeleteUsers(userIds);
      return { userIds, result: response };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to bulk delete users');
    }
  }
);

export const toggleUserStatus = createAsyncThunk(
  'adminDashboard/toggleUserStatus',
  async (id, { rejectWithValue }) => {
    try {
      const response = await adminUserService.toggleUserStatus(id);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to toggle user status');
    }
  }
);

export const verifyUser = createAsyncThunk(
  'adminDashboard/verifyUser',
  async (id, { rejectWithValue }) => {
    try {
      const response = await adminUserService.verifyUser(id);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to verify user');
    }
  }
);

export const suspendUser = createAsyncThunk(
  'adminDashboard/suspendUser',
  async ({ id, reason }, { rejectWithValue }) => {
    try {
      const response = await adminUserService.suspendUser(id, reason);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to suspend user');
    }
  }
);

export const unsuspendUser = createAsyncThunk(
  'adminDashboard/unsuspendUser',
  async (id, { rejectWithValue }) => {
    try {
      const response = await adminUserService.unsuspendUser(id);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to unsuspend user');
    }
  }
);

export const fetchUserActivityHistory = createAsyncThunk(
  'adminDashboard/fetchUserActivityHistory',
  async ({ id, params }, { rejectWithValue }) => {
    try {
      const response = await adminUserService.getUserActivityHistory(id, params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch user activity');
    }
  }
);

export const fetchUserStats = createAsyncThunk(
  'adminDashboard/fetchUserStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await adminUserService.getUserStats();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch user stats');
    }
  }
);

export const exportUsers = createAsyncThunk(
  'adminDashboard/exportUsers',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await adminUserService.exportUsers(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to export users');
    }
  }
);

// Content Management Thunks
export const fetchContent = createAsyncThunk(
  'adminDashboard/fetchContent',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await adminContentService.getAllContent(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch content');
    }
  }
);

export const fetchContentById = createAsyncThunk(
  'adminDashboard/fetchContentById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await adminContentService.getContentById(id);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch content');
    }
  }
);

export const updateContent = createAsyncThunk(
  'adminDashboard/updateContent',
  async ({ id, contentData }, { rejectWithValue }) => {
    try {
      const response = await adminContentService.updateContent(id, contentData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to update content');
    }
  }
);

export const approveContent = createAsyncThunk(
  'adminDashboard/approveContent',
  async ({ id, approvalNotes }, { rejectWithValue }) => {
    try {
      const response = await adminContentService.approveContent(id, approvalNotes);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to approve content');
    }
  }
);

export const rejectContent = createAsyncThunk(
  'adminDashboard/rejectContent',
  async ({ id, reason, rejectionNotes }, { rejectWithValue }) => {
    try {
      const response = await adminContentService.rejectContent(id, reason, rejectionNotes);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to reject content');
    }
  }
);

export const deleteContent = createAsyncThunk(
  'adminDashboard/deleteContent',
  async (id, { rejectWithValue }) => {
    try {
      await adminContentService.deleteContent(id);
      return id;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to delete content');
    }
  }
);

export const bulkApproveContent = createAsyncThunk(
  'adminDashboard/bulkApproveContent',
  async (contentIds, { rejectWithValue }) => {
    try {
      const response = await adminContentService.bulkApproveContent(contentIds);
      return { contentIds, result: response };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to bulk approve content');
    }
  }
);

export const bulkRejectContent = createAsyncThunk(
  'adminDashboard/bulkRejectContent',
  async ({ contentIds, reason }, { rejectWithValue }) => {
    try {
      const response = await adminContentService.bulkRejectContent(contentIds, reason);
      return { contentIds, reason, result: response };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to bulk reject content');
    }
  }
);

export const bulkDeleteContent = createAsyncThunk(
  'adminDashboard/bulkDeleteContent',
  async (contentIds, { rejectWithValue }) => {
    try {
      const response = await adminContentService.bulkDeleteContent(contentIds);
      return { contentIds, result: response };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to bulk delete content');
    }
  }
);

export const updateContentStatus = createAsyncThunk(
  'adminDashboard/updateContentStatus',
  async ({ id, status }, { rejectWithValue }) => {
    try {
      const response = await adminContentService.updateContentStatus(id, status);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to update content status');
    }
  }
);

export const moderateContent = createAsyncThunk(
  'adminDashboard/moderateContent',
  async ({ id, action, reason }, { rejectWithValue }) => {
    try {
      const response = await adminContentService.moderateContent(id, action, reason);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to moderate content');
    }
  }
);

export const featureContent = createAsyncThunk(
  'adminDashboard/featureContent',
  async (id, { rejectWithValue }) => {
    try {
      const response = await adminContentService.featureContent(id);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to feature content');
    }
  }
);

export const unfeatureContent = createAsyncThunk(
  'adminDashboard/unfeatureContent',
  async (id, { rejectWithValue }) => {
    try {
      const response = await adminContentService.unfeatureContent(id);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to unfeature content');
    }
  }
);

export const fetchContentReviews = createAsyncThunk(
  'adminDashboard/fetchContentReviews',
  async ({ id, params }, { rejectWithValue }) => {
    try {
      const response = await adminContentService.getContentReviews(id, params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch content reviews');
    }
  }
);

export const fetchContentStats = createAsyncThunk(
  'adminDashboard/fetchContentStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await adminContentService.getContentStats();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch content stats');
    }
  }
);

export const exportContent = createAsyncThunk(
  'adminDashboard/exportContent',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await adminContentService.exportContent(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to export content');
    }
  }
);

// Bid Management Thunks
export const fetchBids = createAsyncThunk(
  'adminDashboard/fetchBids',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await adminBidService.getAllBids(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch bids');
    }
  }
);

export const fetchBidById = createAsyncThunk(
  'adminDashboard/fetchBidById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await adminBidService.getBidById(id);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch bid');
    }
  }
);

export const approveBid = createAsyncThunk(
  'adminDashboard/approveBid',
  async ({ id, approvalNotes }, { rejectWithValue }) => {
    try {
      const response = await adminBidService.approveBid(id, approvalNotes);
      return { id, data: response.data };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to approve bid');
    }
  }
);

export const rejectBid = createAsyncThunk(
  'adminDashboard/rejectBid',
  async ({ id, reason, rejectionNotes }, { rejectWithValue }) => {
    try {
      const response = await adminBidService.rejectBid(id, reason, rejectionNotes);
      return { id, data: response.data };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to reject bid');
    }
  }
);

export const deleteBid = createAsyncThunk(
  'adminDashboard/deleteBid',
  async (id, { rejectWithValue }) => {
    try {
      await adminBidService.deleteBid(id);
      return id;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to delete bid');
    }
  }
);

export const updateBid = createAsyncThunk(
  'adminDashboard/updateBid',
  async ({ id, status }, { rejectWithValue }) => {
    try {
      const response = await adminBidService.updateBidStatus(id, status);
      return { id, data: response.data };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to update bid');
    }
  }
);

export const bulkApproveBids = createAsyncThunk(
  'adminDashboard/bulkApproveBids',
  async (bidIds, { rejectWithValue }) => {
    try {
      const response = await adminBidService.bulkApproveBids(bidIds);
      return { bidIds, result: response };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to bulk approve bids');
    }
  }
);

export const bulkRejectBids = createAsyncThunk(
  'adminDashboard/bulkRejectBids',
  async ({ bidIds, reason }, { rejectWithValue }) => {
    try {
      const response = await adminBidService.bulkRejectBids(bidIds, reason);
      return { bidIds, result: response };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to bulk reject bids');
    }
  }
);

export const bulkDeleteBids = createAsyncThunk(
  'adminDashboard/bulkDeleteBids',
  async (bidIds, { rejectWithValue }) => {
    try {
      const response = await adminBidService.bulkDeleteBids(bidIds);
      return { bidIds, result: response };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to bulk delete bids');
    }
  }
);

export const getBidStats = createAsyncThunk(
  'adminDashboard/getBidStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await adminBidService.getBidStats();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch bid stats');
    }
  }
);

// Offer Management Thunks
export const fetchOffers = createAsyncThunk(
  'adminDashboard/fetchOffers',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await adminOfferService.getAllOffers(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch offers');
    }
  }
);

export const fetchOfferById = createAsyncThunk(
  'adminDashboard/fetchOfferById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await adminOfferService.getOfferById(id);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch offer details');
    }
  }
);

export const approveOffer = createAsyncThunk(
  'adminDashboard/approveOffer',
  async ({ id, approvalNotes }, { rejectWithValue }) => {
    try {
      const response = await adminOfferService.approveOffer(id, approvalNotes);
      return { id, data: response.data };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to approve offer');
    }
  }
);

export const rejectOffer = createAsyncThunk(
  'adminDashboard/rejectOffer',
  async ({ id, reason, rejectionNotes }, { rejectWithValue }) => {
    try {
      const response = await adminOfferService.rejectOffer(id, reason, rejectionNotes);
      return { id, data: response.data };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to reject offer');
    }
  }
);

export const deleteOffer = createAsyncThunk(
  'adminDashboard/deleteOffer',
  async (id, { rejectWithValue }) => {
    try {
      await adminOfferService.deleteOffer(id);
      return id;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to delete offer');
    }
  }
);

export const bulkApproveOffers = createAsyncThunk(
  'adminDashboard/bulkApproveOffers',
  async ({ offerIds, approvalNotes }, { rejectWithValue }) => {
    try {
      const response = await adminOfferService.bulkApproveOffers(offerIds, approvalNotes);
      return { offerIds, data: response.data };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to approve offers');
    }
  }
);

export const bulkRejectOffers = createAsyncThunk(
  'adminDashboard/bulkRejectOffers',
  async ({ offerIds, reason, rejectionNotes }, { rejectWithValue }) => {
    try {
      const response = await adminOfferService.bulkRejectOffers({ offerIds, reason, rejectionNotes });
      return { offerIds, data: response.data };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to reject offers');
    }
  }
);

export const bulkDeleteOffers = createAsyncThunk(
  'adminDashboard/bulkDeleteOffers',
  async (offerIds, { rejectWithValue }) => {
    try {
      await adminOfferService.bulkDeleteOffers(offerIds);
      return { offerIds };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to delete offers');
    }
  }
);

export const getOfferStats = createAsyncThunk(
  'adminDashboard/getOfferStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await adminOfferService.getOfferStats();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch offer statistics');
    }
  }
);
