const express = require('express');
const { check } = require('express-validator');
const {
  getRevenueReport,
  getUserReport,
  getContentReport,
  getSalesReport,
  getPerformanceReport,
  getCustomReport,
  exportReport,
  getReportTemplates,
  saveReportTemplate,
  deleteReportTemplate,
  scheduleReport,
  getScheduledReports,
  cancelScheduledReport
} = require('../../controllers/admin/reports');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin report routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Revenue reports
router.get('/revenue', [
  check('startDate', 'Start date is required').optional().isISO8601(),
  check('endDate', 'End date is required').optional().isISO8601(),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month', 'year']),
], getRevenueReport);

// User reports
router.get('/users', [
  check('startDate', 'Start date is required').optional().isISO8601(),
  check('endDate', 'End date is required').optional().isISO8601(),
  check('userType', 'User type must be valid').optional().isIn(['buyer', 'seller', 'all']),
], getUserReport);

// Content reports
router.get('/content', [
  check('startDate', 'Start date is required').optional().isISO8601(),
  check('endDate', 'End date is required').optional().isISO8601(),
  check('contentType', 'Content type must be valid').optional(),
  check('sport', 'Sport must be valid').optional(),
], getContentReport);

// Sales reports
router.get('/sales', [
  check('startDate', 'Start date is required').optional().isISO8601(),
  check('endDate', 'End date is required').optional().isISO8601(),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month', 'year']),
], getSalesReport);

// Performance reports
router.get('/performance', [
  check('startDate', 'Start date is required').optional().isISO8601(),
  check('endDate', 'End date is required').optional().isISO8601(),
  check('metric', 'Metric must be valid').optional().isIn(['revenue', 'users', 'content', 'orders']),
], getPerformanceReport);

// Custom reports
router.post('/custom', [
  check('reportType', 'Report type is required').not().isEmpty(),
  check('parameters', 'Parameters must be an object').isObject(),
], getCustomReport);

// Export reports
router.post('/export', [
  check('reportType', 'Report type is required').not().isEmpty(),
  check('format', 'Format must be valid').isIn(['csv', 'excel', 'pdf']),
  check('parameters', 'Parameters must be an object').isObject(),
], exportReport);

// Report templates
router.get('/templates', getReportTemplates);

router.post('/templates', [
  check('name', 'Template name is required').not().isEmpty(),
  check('reportType', 'Report type is required').not().isEmpty(),
  check('parameters', 'Parameters must be an object').isObject(),
], saveReportTemplate);

router.delete('/templates/:id', deleteReportTemplate);

// Scheduled reports
router.get('/scheduled', getScheduledReports);

router.post('/schedule', [
  check('name', 'Schedule name is required').not().isEmpty(),
  check('reportType', 'Report type is required').not().isEmpty(),
  check('schedule', 'Schedule is required').not().isEmpty(),
  check('recipients', 'Recipients array is required').isArray(),
  check('parameters', 'Parameters must be an object').isObject(),
], scheduleReport);

router.delete('/scheduled/:id', cancelScheduledReport);

module.exports = router;
