const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const User = require('../../models/User');
const Content = require('../../models/Content');
const Order = require('../../models/Order');
const Payment = require('../../models/Payment');
const Bid = require('../../models/Bid');

// @desc    Get dashboard analytics overview
// @route   GET /api/admin/analytics/dashboard
// @access  Private/Admin
exports.getDashboardAnalytics = async (req, res, next) => {
  try {
    const { period = '30d' } = req.query;

    // Calculate date range
    const now = new Date();
    let startDate;

    switch (period) {
      case '7d':
        startDate = new Date(now.setDate(now.getDate() - 7));
        break;
      case '30d':
        startDate = new Date(now.setDate(now.getDate() - 30));
        break;
      case '90d':
        startDate = new Date(now.setDate(now.getDate() - 90));
        break;
      case '1y':
        startDate = new Date(now.setFullYear(now.getFullYear() - 1));
        break;
      case 'all':
        startDate = new Date('2020-01-01'); // Platform start date
        break;
      default:
        startDate = new Date(now.setDate(now.getDate() - 30));
    }

    // Key metrics
    const totalUsers = await User.countDocuments({
      createdAt: { $gte: startDate }
    });

    const totalRevenue = await Payment.aggregate([
      {
        $match: {
          status: 'Completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);

    const totalOrders = await Order.countDocuments({
      createdAt: { $gte: startDate }
    });

    const totalContent = await Content.countDocuments({
      createdAt: { $gte: startDate }
    });

    // Growth rates (compared to previous period)
    const previousPeriodStart = new Date(startDate);
    previousPeriodStart.setTime(previousPeriodStart.getTime() - (now.getTime() - startDate.getTime()));

    const previousUsers = await User.countDocuments({
      createdAt: { $gte: previousPeriodStart, $lt: startDate }
    });

    const previousRevenue = await Payment.aggregate([
      {
        $match: {
          status: 'Completed',
          createdAt: { $gte: previousPeriodStart, $lt: startDate }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);

    const previousOrders = await Order.countDocuments({
      createdAt: { $gte: previousPeriodStart, $lt: startDate }
    });

    const previousContent = await Content.countDocuments({
      createdAt: { $gte: previousPeriodStart, $lt: startDate }
    });

    // Calculate growth rates
    const userGrowth = previousUsers > 0 ? ((totalUsers - previousUsers) / previousUsers) * 100 : 0;
    const revenueGrowth = previousRevenue[0]?.total > 0
      ? ((totalRevenue[0]?.total || 0) - previousRevenue[0].total) / previousRevenue[0].total * 100
      : 0;
    const orderGrowth = previousOrders > 0 ? ((totalOrders - previousOrders) / previousOrders) * 100 : 0;
    const contentGrowth = previousContent > 0 ? ((totalContent - previousContent) / previousContent) * 100 : 0;

    res.status(200).json({
      success: true,
      data: {
        period,
        metrics: {
          users: {
            total: totalUsers,
            growth: Math.round(userGrowth * 100) / 100
          },
          revenue: {
            total: totalRevenue[0]?.total || 0,
            growth: Math.round(revenueGrowth * 100) / 100
          },
          orders: {
            total: totalOrders,
            growth: Math.round(orderGrowth * 100) / 100
          },
          content: {
            total: totalContent,
            growth: Math.round(contentGrowth * 100) / 100
          }
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get user analytics
// @route   GET /api/admin/analytics/users
// @access  Private/Admin
exports.getUserAnalytics = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      userType = 'all',
      groupBy = 'day'
    } = req.query;

    let userQuery = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    if (userType !== 'all') {
      userQuery.role = userType;
    }

    // Group format based on groupBy parameter
    let groupFormat;
    switch (groupBy) {
      case 'day':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'week':
        groupFormat = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        };
        break;
      case 'month':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      default:
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    // User registrations over time
    const userRegistrations = await User.aggregate([
      { $match: userQuery },
      {
        $group: {
          _id: groupFormat,
          count: { $sum: 1 },
          buyers: { $sum: { $cond: [{ $eq: ['$role', 'buyer'] }, 1, 0] } },
          sellers: { $sum: { $cond: [{ $eq: ['$role', 'seller'] }, 1, 0] } }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // User activity metrics
    const activeUsers = await Order.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: null,
          uniqueBuyers: { $addToSet: '$buyer' },
          uniqueSellers: { $addToSet: '$seller' }
        }
      },
      {
        $project: {
          activeBuyers: { $size: '$uniqueBuyers' },
          activeSellers: { $size: '$uniqueSellers' }
        }
      }
    ]);

    // User retention (users who made multiple purchases)
    const userRetention = await Order.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$buyer',
          orderCount: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: null,
          totalUsers: { $sum: 1 },
          repeatUsers: { $sum: { $cond: [{ $gt: ['$orderCount', 1] }, 1, 0] } }
        }
      },
      {
        $project: {
          retentionRate: {
            $multiply: [
              { $divide: ['$repeatUsers', '$totalUsers'] },
              100
            ]
          }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        userRegistrations,
        activeUsers: activeUsers[0] || { activeBuyers: 0, activeSellers: 0 },
        retentionRate: userRetention[0]?.retentionRate || 0,
        period: { startDate, endDate, userType, groupBy }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get content analytics
// @route   GET /api/admin/analytics/content
// @access  Private/Admin
exports.getContentAnalytics = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      contentType = '',
      sport = '',
      groupBy = 'day'
    } = req.query;

    let contentQuery = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    if (contentType) contentQuery.contentType = contentType;
    if (sport) contentQuery.sport = sport;

    // Group format
    let groupFormat;
    switch (groupBy) {
      case 'day':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'week':
        groupFormat = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        };
        break;
      case 'month':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      default:
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    // Content uploads over time
    const contentUploads = await Content.aggregate([
      { $match: contentQuery },
      {
        $group: {
          _id: groupFormat,
          count: { $sum: 1 },
          published: { $sum: { $cond: [{ $eq: ['$status', 'Published'] }, 1, 0] } },
          pending: { $sum: { $cond: [{ $eq: ['$status', 'Under Review'] }, 1, 0] } }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Content performance metrics
    const contentPerformance = await Order.aggregate([
      {
        $lookup: {
          from: 'contents',
          localField: 'content',
          foreignField: '_id',
          as: 'contentInfo'
        }
      },
      { $unwind: '$contentInfo' },
      {
        $match: {
          'contentInfo.createdAt': {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$contentInfo.contentType',
          totalSales: { $sum: 1 },
          totalRevenue: { $sum: '$amount' },
          averagePrice: { $avg: '$amount' }
        }
      },
      { $sort: { totalRevenue: -1 } }
    ]);

    // Content approval rates
    const approvalRates = await Content.aggregate([
      { $match: contentQuery },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          published: { $sum: { $cond: [{ $eq: ['$status', 'Published'] }, 1, 0] } },
          rejected: { $sum: { $cond: [{ $eq: ['$status', 'Rejected'] }, 1, 0] } },
          pending: { $sum: { $cond: [{ $eq: ['$status', 'Under Review'] }, 1, 0] } }
        }
      },
      {
        $project: {
          approvalRate: {
            $multiply: [
              { $divide: ['$published', '$total'] },
              100
            ]
          },
          rejectionRate: {
            $multiply: [
              { $divide: ['$rejected', '$total'] },
              100
            ]
          }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        contentUploads,
        contentPerformance,
        approvalRates: approvalRates[0] || { approvalRate: 0, rejectionRate: 0 },
        period: { startDate, endDate, contentType, sport, groupBy }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get revenue analytics
// @route   GET /api/admin/analytics/revenue
// @access  Private/Admin
exports.getRevenueAnalytics = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      groupBy = 'day',
      currency = 'USD'
    } = req.query;

    // Group format
    let groupFormat;
    switch (groupBy) {
      case 'day':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'week':
        groupFormat = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        };
        break;
      case 'month':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      default:
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    // Revenue over time
    const revenueData = await Payment.aggregate([
      {
        $match: {
          status: 'Completed',
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: groupFormat,
          totalRevenue: { $sum: '$amount' },
          platformFees: { $sum: '$platformFee' },
          sellerEarnings: { $sum: '$sellerEarnings' },
          transactionCount: { $sum: 1 },
          averageTransaction: { $avg: '$amount' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Revenue by payment method
    const revenueByMethod = await Payment.aggregate([
      {
        $match: {
          status: 'Completed',
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$paymentMethod',
          revenue: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { revenue: -1 } }
    ]);

    // Revenue trends
    const revenueTrends = await Payment.aggregate([
      {
        $match: {
          status: 'Completed',
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          totalTransactions: { $sum: 1 },
          averageTransaction: { $avg: '$amount' },
          maxTransaction: { $max: '$amount' },
          minTransaction: { $min: '$amount' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        revenueData,
        revenueByMethod,
        trends: revenueTrends[0] || {},
        period: { startDate, endDate, groupBy, currency }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get sales analytics
// @route   GET /api/admin/analytics/sales
// @access  Private/Admin
exports.getSalesAnalytics = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      groupBy = 'day',
      saleType = 'all'
    } = req.query;

    let orderQuery = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    if (saleType !== 'all') {
      orderQuery.orderType = saleType;
    }

    // Group format
    let groupFormat;
    switch (groupBy) {
      case 'day':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'week':
        groupFormat = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        };
        break;
      case 'month':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      default:
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    // Sales over time
    const salesData = await Order.aggregate([
      { $match: orderQuery },
      {
        $group: {
          _id: groupFormat,
          totalSales: { $sum: 1 },
          totalRevenue: { $sum: '$amount' },
          averageOrderValue: { $avg: '$amount' },
          completedSales: { $sum: { $cond: [{ $eq: ['$status', 'Completed'] }, 1, 0] } }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Sales by type
    const salesByType = await Order.aggregate([
      { $match: orderQuery },
      {
        $group: {
          _id: '$orderType',
          count: { $sum: 1 },
          revenue: { $sum: '$amount' }
        }
      },
      { $sort: { revenue: -1 } }
    ]);

    // Conversion funnel
    const conversionFunnel = await Order.aggregate([
      { $match: orderQuery },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        salesData,
        salesByType,
        conversionFunnel,
        period: { startDate, endDate, groupBy, saleType }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get traffic analytics
// @route   GET /api/admin/analytics/traffic
// @access  Private/Admin
exports.getTrafficAnalytics = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      source = ''
    } = req.query;

    // Mock traffic data - In a real application, you would integrate with analytics services
    const trafficData = {
      totalVisits: 15420,
      uniqueVisitors: 8930,
      pageViews: 45670,
      bounceRate: 32.5,
      averageSessionDuration: 245, // seconds
      topPages: [
        { page: '/marketplace', views: 8920, uniqueViews: 5430 },
        { page: '/content/training-videos', views: 6780, uniqueViews: 4210 },
        { page: '/sellers', views: 4560, uniqueViews: 3120 },
        { page: '/auctions', views: 3890, uniqueViews: 2890 },
        { page: '/profile', views: 2340, uniqueViews: 1890 }
      ],
      trafficSources: [
        { source: 'Direct', visits: 6890, percentage: 44.7 },
        { source: 'Google', visits: 4320, percentage: 28.0 },
        { source: 'Social Media', visits: 2180, percentage: 14.1 },
        { source: 'Referral', visits: 1560, percentage: 10.1 },
        { source: 'Email', visits: 470, percentage: 3.1 }
      ],
      deviceBreakdown: [
        { device: 'Desktop', visits: 8920, percentage: 57.8 },
        { device: 'Mobile', visits: 5430, percentage: 35.2 },
        { device: 'Tablet', visits: 1070, percentage: 7.0 }
      ]
    };

    res.status(200).json({
      success: true,
      data: {
        ...trafficData,
        period: { startDate, endDate, source }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get conversion analytics
// @route   GET /api/admin/analytics/conversion
// @access  Private/Admin
exports.getConversionAnalytics = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      funnelType = 'purchase'
    } = req.query;

    let conversionData = {};

    switch (funnelType) {
      case 'registration':
        // Registration funnel: Visitor -> Signup -> Email Verification -> Profile Complete
        conversionData = {
          visitors: 10000, // Mock data
          signups: 1200,
          emailVerified: 980,
          profileCompleted: 850,
          conversionRate: 8.5
        };
        break;

      case 'purchase':
        // Purchase funnel: Visitor -> Product View -> Add to Cart -> Checkout -> Purchase
        const productViews = await Content.countDocuments({
          createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) }
        });

        const orders = await Order.countDocuments({
          createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) }
        });

        const completedOrders = await Order.countDocuments({
          status: 'Completed',
          createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) }
        });

        conversionData = {
          productViews: productViews * 10, // Estimated views
          addedToCart: Math.floor(productViews * 0.15), // 15% add to cart
          checkoutStarted: Math.floor(productViews * 0.08), // 8% start checkout
          ordersPlaced: orders,
          ordersCompleted: completedOrders,
          conversionRate: productViews > 0 ? (completedOrders / (productViews * 10)) * 100 : 0
        };
        break;

      case 'seller_onboarding':
        // Seller onboarding funnel
        const sellerSignups = await User.countDocuments({
          role: 'seller',
          createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) }
        });

        const verifiedSellers = await User.countDocuments({
          role: 'seller',
          isVerified: true,
          createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) }
        });

        const activeSellers = await Content.aggregate([
          {
            $match: {
              createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) }
            }
          },
          {
            $group: {
              _id: '$seller'
            }
          },
          {
            $count: 'activeSellers'
          }
        ]);

        conversionData = {
          sellerSignups,
          verifiedSellers,
          activeSellers: activeSellers[0]?.activeSellers || 0,
          conversionRate: sellerSignups > 0 ? (activeSellers[0]?.activeSellers || 0) / sellerSignups * 100 : 0
        };
        break;

      default:
        return next(new ErrorResponse('Invalid funnel type', 400));
    }

    res.status(200).json({
      success: true,
      data: {
        funnelType,
        ...conversionData,
        period: { startDate, endDate }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get real-time analytics
// @route   GET /api/admin/analytics/realtime
// @access  Private/Admin
exports.getRealTimeAnalytics = async (req, res, next) => {
  try {
    const now = new Date();
    const lastHour = new Date(now.getTime() - 60 * 60 * 1000);
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Real-time metrics (last hour)
    const realtimeMetrics = {
      activeUsers: Math.floor(Math.random() * 150) + 50, // Mock active users
      currentSessions: Math.floor(Math.random() * 80) + 20,
      pageViewsLastHour: Math.floor(Math.random() * 500) + 200,
      newOrdersLastHour: await Order.countDocuments({
        createdAt: { $gte: lastHour }
      }),
      newUsersLastHour: await User.countDocuments({
        createdAt: { $gte: lastHour }
      }),
      revenueLastHour: await Payment.aggregate([
        {
          $match: {
            status: 'Completed',
            createdAt: { $gte: lastHour }
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$amount' }
          }
        }
      ])
    };

    // 24-hour trends
    const last24HoursTrends = {
      orders: await Order.countDocuments({
        createdAt: { $gte: last24Hours }
      }),
      users: await User.countDocuments({
        createdAt: { $gte: last24Hours }
      }),
      revenue: await Payment.aggregate([
        {
          $match: {
            status: 'Completed',
            createdAt: { $gte: last24Hours }
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$amount' }
          }
        }
      ])
    };

    res.status(200).json({
      success: true,
      data: {
        realtime: {
          ...realtimeMetrics,
          revenueLastHour: realtimeMetrics.revenueLastHour[0]?.total || 0
        },
        last24Hours: {
          ...last24HoursTrends,
          revenue: last24HoursTrends.revenue[0]?.total || 0
        },
        timestamp: now
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get sales analytics
// @route   GET /api/admin/analytics/sales
// @access  Private/Admin
exports.getSalesAnalytics = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      groupBy = 'day',
      saleType = 'all'
    } = req.query;

    let salesQuery = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    if (saleType !== 'all') {
      salesQuery.saleType = saleType;
    }

    // Group format
    let groupFormat;
    switch (groupBy) {
      case 'day':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'week':
        groupFormat = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        };
        break;
      case 'month':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      default:
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    // Sales data over time
    const salesData = await Order.aggregate([
      { $match: salesQuery },
      {
        $group: {
          _id: groupFormat,
          totalSales: { $sum: 1 },
          totalRevenue: { $sum: '$amount' },
          averageOrderValue: { $avg: '$amount' },
          directSales: { $sum: { $cond: [{ $eq: ['$saleType', 'direct'] }, 1, 0] } },
          auctionSales: { $sum: { $cond: [{ $eq: ['$saleType', 'auction'] }, 1, 0] } }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Top selling content
    const topContent = await Order.aggregate([
      { $match: salesQuery },
      {
        $group: {
          _id: '$content',
          salesCount: { $sum: 1 },
          totalRevenue: { $sum: '$amount' }
        }
      },
      {
        $lookup: {
          from: 'contents',
          localField: '_id',
          foreignField: '_id',
          as: 'contentInfo'
        }
      },
      { $unwind: '$contentInfo' },
      {
        $project: {
          title: '$contentInfo.title',
          sport: '$contentInfo.sport',
          contentType: '$contentInfo.contentType',
          salesCount: 1,
          totalRevenue: 1
        }
      },
      { $sort: { salesCount: -1 } },
      { $limit: 10 }
    ]);

    res.status(200).json({
      success: true,
      data: {
        salesData,
        topContent,
        period: { startDate, endDate, groupBy, saleType }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get traffic analytics
// @route   GET /api/admin/analytics/traffic
// @access  Private/Admin
exports.getTrafficAnalytics = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      source = ''
    } = req.query;

    // Mock traffic data - In a real implementation, this would come from analytics tools
    const trafficData = {
      totalVisitors: 15420,
      uniqueVisitors: 12350,
      pageViews: 45680,
      bounceRate: 35.2,
      averageSessionDuration: 245, // seconds
      topPages: [
        { page: '/marketplace', views: 8500, uniqueViews: 6200 },
        { page: '/content/football', views: 5200, uniqueViews: 4100 },
        { page: '/content/basketball', views: 3800, uniqueViews: 3200 },
        { page: '/sellers', views: 2900, uniqueViews: 2400 },
        { page: '/about', views: 1800, uniqueViews: 1600 }
      ],
      trafficSources: [
        { source: 'Direct', visitors: 5200, percentage: 42.1 },
        { source: 'Google', visitors: 3100, percentage: 25.1 },
        { source: 'Social Media', visitors: 2400, percentage: 19.4 },
        { source: 'Referral', visitors: 1200, percentage: 9.7 },
        { source: 'Email', visitors: 450, percentage: 3.6 }
      ],
      deviceBreakdown: [
        { device: 'Desktop', visitors: 7400, percentage: 59.9 },
        { device: 'Mobile', visitors: 4200, percentage: 34.0 },
        { device: 'Tablet', visitors: 750, percentage: 6.1 }
      ]
    };

    res.status(200).json({
      success: true,
      data: {
        ...trafficData,
        period: { startDate, endDate, source }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get conversion analytics
// @route   GET /api/admin/analytics/conversion
// @access  Private/Admin
exports.getConversionAnalytics = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      funnelType = 'purchase'
    } = req.query;

    // Mock conversion data
    const conversionData = {
      purchase: {
        steps: [
          { step: 'Visit', users: 10000, conversionRate: 100 },
          { step: 'Browse Content', users: 7500, conversionRate: 75 },
          { step: 'View Details', users: 4200, conversionRate: 42 },
          { step: 'Add to Cart', users: 1800, conversionRate: 18 },
          { step: 'Checkout', users: 950, conversionRate: 9.5 },
          { step: 'Purchase', users: 720, conversionRate: 7.2 }
        ],
        overallConversion: 7.2,
        dropoffPoints: [
          { from: 'Visit', to: 'Browse Content', dropoff: 25 },
          { from: 'Browse Content', to: 'View Details', dropoff: 44 },
          { from: 'View Details', to: 'Add to Cart', dropoff: 57 }
        ]
      },
      registration: {
        steps: [
          { step: 'Landing Page', users: 5000, conversionRate: 100 },
          { step: 'Sign Up Form', users: 2800, conversionRate: 56 },
          { step: 'Email Verification', users: 2100, conversionRate: 42 },
          { step: 'Profile Setup', users: 1850, conversionRate: 37 },
          { step: 'Complete Registration', users: 1650, conversionRate: 33 }
        ],
        overallConversion: 33,
        dropoffPoints: [
          { from: 'Landing Page', to: 'Sign Up Form', dropoff: 44 },
          { from: 'Sign Up Form', to: 'Email Verification', dropoff: 25 },
          { from: 'Email Verification', to: 'Profile Setup', dropoff: 12 }
        ]
      }
    };

    res.status(200).json({
      success: true,
      data: {
        funnel: conversionData[funnelType] || conversionData.purchase,
        period: { startDate, endDate, funnelType }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get custom analytics
// @route   POST /api/admin/analytics/custom
// @access  Private/Admin
exports.getCustomAnalytics = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const {
      metrics,
      dimensions,
      filters = {},
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date()
    } = req.body;

    // Mock custom analytics data
    const customData = {
      metrics: metrics.map(metric => ({
        name: metric,
        value: Math.floor(Math.random() * 10000),
        change: Math.floor(Math.random() * 200) - 100 // -100 to +100
      })),
      dimensions: dimensions.map(dimension => ({
        name: dimension,
        values: Array.from({ length: 5 }, (_, i) => ({
          label: `${dimension} ${i + 1}`,
          value: Math.floor(Math.random() * 1000)
        }))
      })),
      filters: filters,
      period: { startDate, endDate }
    };

    res.status(200).json({
      success: true,
      data: customData
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get comparative analytics
// @route   POST /api/admin/analytics/compare
// @access  Private/Admin
exports.getComparativeAnalytics = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const {
      metric,
      periods,
      groupBy = 'day'
    } = req.body;

    // Mock comparative data
    const comparativeData = {
      metric,
      groupBy,
      periods: periods.map((period, index) => ({
        label: period.label || `Period ${index + 1}`,
        startDate: period.startDate,
        endDate: period.endDate,
        data: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000),
          value: Math.floor(Math.random() * 1000) + index * 100
        })),
        total: Math.floor(Math.random() * 30000) + index * 5000,
        average: Math.floor(Math.random() * 1000) + index * 100
      }))
    };

    res.status(200).json({
      success: true,
      data: comparativeData
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export analytics data
// @route   POST /api/admin/analytics/export
// @access  Private/Admin
exports.exportAnalytics = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const {
      analyticsType,
      format,
      parameters
    } = req.body;

    // Mock export data
    const exportData = {
      exportId: Date.now().toString(),
      analyticsType,
      format,
      parameters,
      status: 'completed',
      downloadUrl: `/api/admin/analytics/download/${Date.now()}`,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      fileSize: Math.floor(Math.random() * 5000) + 1000 // KB
    };

    res.status(200).json({
      success: true,
      data: exportData,
      message: 'Analytics export has been generated successfully'
    });
  } catch (err) {
    next(err);
  }
};
