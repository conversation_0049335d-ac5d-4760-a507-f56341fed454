const express = require('express');
const { check } = require('express-validator');
const {
  getAllOrders,
  getOrderById,
  updateOrderStatus,
  deleteOrder,
  bulkUpdateOrders,
  bulkDeleteOrders,
  getOrderStats,
  exportOrders,
  processRefund,
  getOrderAnalytics,
  getOrderTimeline,
  flagOrder,
  unflagOrder
} = require('../../controllers/admin/orders');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin order routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get all orders with filtering, sorting, and pagination
router.get('/', getAllOrders);

// Get order statistics
router.get('/stats', getOrderStats);

// Get order analytics
router.get('/analytics', [
  check('period', 'Period must be valid').optional().isIn(['7d', '30d', '90d', '1y', 'all']),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month']),
], getOrderAnalytics);

// Export orders data
router.get('/export', exportOrders);

// Bulk operations
router.post('/bulk-update', [
  check('orderIds', 'Order IDs array is required').isArray(),
  check('status', 'Status is required').isIn(['Pending', 'Processing', 'Completed', 'Cancelled', 'Refunded']),
], bulkUpdateOrders);

router.post('/bulk-delete', [
  check('orderIds', 'Order IDs array is required').isArray(),
], bulkDeleteOrders);

// Individual order operations
router.get('/:id', getOrderById);

router.put('/:id/status', [
  check('status', 'Status is required').isIn(['Pending', 'Processing', 'Completed', 'Cancelled', 'Refunded']),
  check('notes', 'Notes must be a string').optional().isString(),
], updateOrderStatus);

router.post('/:id/refund', [
  check('amount', 'Refund amount is required').isFloat({ min: 0.01 }),
  check('reason', 'Refund reason is required').not().isEmpty(),
], processRefund);

router.get('/:id/timeline', getOrderTimeline);

router.put('/:id/flag', [
  check('reason', 'Flag reason is required').not().isEmpty(),
], flagOrder);

router.put('/:id/unflag', unflagOrder);

router.delete('/:id', deleteOrder);

module.exports = router;
