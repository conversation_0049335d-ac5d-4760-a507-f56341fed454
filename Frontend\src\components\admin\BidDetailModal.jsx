import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectCurrentBidDetail, selectUI, hideBidDetailModal } from '../../redux/slices/adminDashboardSlice';
import { FaTimes } from 'react-icons/fa';
import '../../styles/BidDetailModal.css';

const BidDetailModal = () => {
    const dispatch = useDispatch();
    const currentBid = useSelector(selectCurrentBidDetail);
    const ui = useSelector(selectUI);

    if (!currentBid || !ui.showBidDetailModal) return null;

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const handleClose = () => {
        dispatch(hideBidDetailModal());
    };

    return (
        <div className="modal-overlay">
            <div className="bid-detail-modal">
                <div className="modal-header">
                    <h2>Bid Details</h2>
                    <button className="close-button" onClick={handleClose}>
                        <FaTimes />
                    </button>
                </div>

                <div className="modal-content">
                    <div className="bid-info-section">
                        <h3>Bid Information</h3>
                        <div className="info-grid">
                            <div className="info-item">
                                <label>Bid ID:</label>
                                <span>{currentBid.bidId || `#${currentBid._id?.slice(-6)}`}</span>
                            </div>
                            <div className="info-item">
                                <label>Status:</label>
                                <span className={`status-badge ${currentBid.status.toLowerCase()}`}>
                                    {currentBid.status}
                                </span>
                            </div>
                            <div className="info-item">
                                <label>Bid Amount:</label>
                                <span>{formatCurrency(currentBid.amount)}</span>
                            </div>
                            <div className="info-item">
                                <label>Bid Date:</label>
                                <span>{formatDate(currentBid.createdAt)}</span>
                            </div>
                            {currentBid.maxAutoBidAmount && (
                                <div className="info-item">
                                    <label>Max Auto-Bid Amount:</label>
                                    <span>{formatCurrency(currentBid.maxAutoBidAmount)}</span>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="content-info-section">
                        <h3>Content Information</h3>
                        <div className="info-grid">
                            <div className="info-item">
                                <label>Title:</label>
                                <span>{currentBid.content?.title}</span>
                            </div>
                            <div className="info-item">
                                <label>Type:</label>
                                <span>{currentBid.content?.contentType}</span>
                            </div>
                            <div className="info-item">
                                <label>Base Price:</label>
                                <span>{formatCurrency(currentBid.content?.price || 0)}</span>
                            </div>
                        </div>
                    </div>

                    <div className="bidder-info-section">
                        <h3>Bidder Information</h3>
                        <div className="info-grid">
                            <div className="info-item">
                                <label>Name:</label>
                                <span>
                                    {currentBid.bidder
                                        ? `${currentBid.bidder.firstName} ${currentBid.bidder.lastName}`
                                        : currentBid.bidderName}
                                </span>
                            </div>
                            <div className="info-item">
                                <label>Email:</label>
                                <span>{currentBid.bidder?.email || 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default BidDetailModal; 