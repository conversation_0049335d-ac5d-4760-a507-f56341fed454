import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/bids`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Get all bids with filtering and pagination
export const getAllBids = async (params = {}) => {
  try {
    const response = await api.get('/', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get bid by ID
export const getBidById = async (id) => {
  try {
    const response = await api.get(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Approve bid
export const approveBid = async (id, approvalNotes = '') => {
  try {
    const response = await api.put(`/${id}/approve`, { approvalNotes });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Reject bid
export const rejectBid = async (id, reason, rejectionNotes = '') => {
  try {
    const response = await api.put(`/${id}/reject`, { reason, rejectionNotes });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Delete bid
export const deleteBid = async (id) => {
  try {
    const response = await api.delete(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk approve bids
export const bulkApproveBids = async (bidIds) => {
  try {
    const response = await api.post('/bulk-approve', { bidIds });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk reject bids
export const bulkRejectBids = async (bidIds, reason) => {
  try {
    const response = await api.post('/bulk-reject', { bidIds, reason });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk delete bids
export const bulkDeleteBids = async (bidIds) => {
  try {
    const response = await api.post('/bulk-delete', { bidIds });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update bid status
export const updateBidStatus = async (id, status) => {
  try {
    const response = await api.put(`/${id}/status`, { status });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Cancel bid
export const cancelBid = async (id, reason) => {
  try {
    const response = await api.put(`/${id}/cancel`, { reason });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Flag bid
export const flagBid = async (id, reason) => {
  try {
    const response = await api.put(`/${id}/flag`, { reason });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Unflag bid
export const unflagBid = async (id) => {
  try {
    const response = await api.put(`/${id}/unflag`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get bid statistics
export const getBidStats = async () => {
  try {
    const response = await api.get('/stats');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get auction statistics
export const getAuctionStats = async () => {
  try {
    const response = await api.get('/auction-stats');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Export bids
export const exportBids = async (params = {}) => {
  try {
    const response = await api.get('/export', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bid management actions
export const bidActions = {
  approve: (id, notes) => approveBid(id, notes),
  reject: (id, reason, notes) => rejectBid(id, reason, notes),
  cancel: (id, reason) => cancelBid(id, reason),
  flag: (id, reason) => flagBid(id, reason),
  unflag: (id) => unflagBid(id),
};

// Bid status actions
export const bidStatusActions = {
  activate: (id) => updateBidStatus(id, 'Active'),
  expire: (id) => updateBidStatus(id, 'Expired'),
  win: (id) => updateBidStatus(id, 'Won'),
  cancel: (id) => updateBidStatus(id, 'Cancelled'),
};

export default {
  getAllBids,
  getBidById,
  approveBid,
  rejectBid,
  deleteBid,
  bulkApproveBids,
  bulkRejectBids,
  bulkDeleteBids,
  updateBidStatus,
  cancelBid,
  flagBid,
  unflagBid,
  getBidStats,
  getAuctionStats,
  exportBids,
  bidActions,
  bidStatusActions
};
