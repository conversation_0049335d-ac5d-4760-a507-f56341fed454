import React, { useEffect, useState, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import {
  motion,
  useScroll,
  useTransform,
  useInView,
  useAnimation,
} from "framer-motion";
import "../../styles/Home.css";
import "../../styles/HomeAnimations.css";

import heromainimg from "../../assets/images/herosideimg.svg";
import ourmissionimage from "../../assets/images/ourmissionimage.svg";
import verticallineimg from "../../assets/images/verticallineimg.svg";
// Components
import SportsCard from "../../components/common/SportsCard";
import StrategyCard from "../../components/common/StrategyCard";
// Services
import { getStoredUser } from "../../services/authService";
import { getAllContent } from "../../services/contentService";

// Icons
import { TbLockDollar } from "react-icons/tb";
import { TbPencilDollar } from "react-icons/tb";
import { GoShieldCheck } from "react-icons/go";
import { LiaHandHoldingUsdSolid } from "react-icons/lia";
import { BsDatabaseLock } from "react-icons/bs";
import { GiTeacher } from "react-icons/gi";
import { GrTransaction } from "react-icons/gr";
import { FaRegHandshake } from "react-icons/fa";
import { MdSecurity } from "react-icons/md";

// Data
import { sportsData } from "../../data/sportsData";

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 60 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] },
  },
};

const fadeInLeft = {
  hidden: { opacity: 0, x: -60 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] },
  },
};

const fadeInRight = {
  hidden: { opacity: 0, x: 60 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] },
  },
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: { duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] },
  },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1,
    },
  },
};

const Home = () => {
  const navigate = useNavigate();
  const [topRatedStrategies, setTopRatedStrategies] = useState([]);
  const [loading, setLoading] = useState(true);

  // Refs for scroll animations
  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"],
  });

  // Parallax transforms
  const heroImageY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const heroImageScale = useTransform(scrollYProgress, [0, 1], [1, 1.2]);

  // Get only the first 4 sports for the fixed grid layout
  const featuredSports = sportsData.slice(0, 4);

  useEffect(() => {
    const fetchTopRatedStrategies = async () => {
      try {
        const response = await getAllContent({
          sortBy: "rating",
          limit: 8,
        });
        setTopRatedStrategies(response.data.slice(0, 8));
        setLoading(false);
      } catch (error) {
        console.error("Error fetching top rated strategies:", error);
        setLoading(false);
      }
    };

    fetchTopRatedStrategies();
  }, []);

  // Handler for "Start Trading Strategies Today" button with role-based redirection
  const handleStartTradingClick = (e) => {
    e.preventDefault();

    // Get user data from localStorage
    const user = getStoredUser();

    if (!user) {
      // If no user is authenticated, redirect to auth page
      navigate("/auth");
      return;
    }

    // Get the active role (for non-admin users, use activeRole; for admin, use role)
    const activeRole =
      user.role === "admin" ? user.role : user.activeRole || user.role;

    // Redirect based on active role
    if (activeRole === "buyer") {
      navigate("/content");
    } else if (activeRole === "seller") {
      navigate("/seller/my-sports-strategies");
    } else {
      // Fallback to auth page for any other case
      navigate("/auth");
    }
  };

  return (
    <div className="home-section">
      {/* Hero Section */}
      <motion.section
        ref={heroRef}
        className="hero-section p-section"
        initial="hidden"
        animate="visible"
        variants={staggerContainer}
      >
        <div className="hero-container max-container">
          <motion.div className="hero-content" variants={staggerContainer}>
            <motion.h1
              className="hero-title mb-10"
              variants={{
                hidden: { opacity: 0, y: 50 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: {
                    duration: 1,
                    ease: [0.25, 0.46, 0.45, 0.94],
                    staggerChildren: 0.1,
                    delayChildren: 0.2,
                  },
                },
              }}
            >
              {"Digital Sports Playbook Marketplace"
                .split(" ")
                .map((word, index) => (
                  <motion.span
                    key={index}
                    variants={{
                      hidden: { opacity: 0, y: 50 },
                      visible: {
                        opacity: 1,
                        y: 0,
                        transition: { duration: 0.6 },
                      },
                    }}
                    style={{ display: "inline-block", marginRight: "0.3em" }}
                  >
                    {word}
                  </motion.span>
                ))}
            </motion.h1>

            <motion.p
              className="hero-tagline mb-20"
              variants={fadeInLeft}
              transition={{ delay: 0.8 }}
            >
              "Elevate Your Game - A Digital Exchange of Sports Strategies"
            </motion.p>

            <motion.div
              className="hero-description mb-30"
              variants={fadeInUp}
              transition={{ delay: 1.0 }}
            >
              <p>Discover, Buy, and Sell Winning Sports Strategies.</p>
              <p>
                Join a secure, innovative marketplace dedicated to premium
                sports tactics, playbooks, videos, and more.
              </p>
            </motion.div>

            <motion.div
              variants={scaleIn}
              transition={{ delay: 1.2 }}
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.2 },
              }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                to="/auth"
                className="btn btn-primary"
                onClick={handleStartTradingClick}
              >
                Start Trading Strategies Today
              </Link>
            </motion.div>
          </motion.div>

          <motion.div
            className="hero-background"
            style={{
              y: heroImageY,
              scale: heroImageScale,
            }}
            initial={{ opacity: 0, x: 100 }}
            animate={{
              opacity: 1,
              x: 0,
              transition: {
                duration: 1.2,
                delay: 0.5,
                ease: [0.25, 0.46, 0.45, 0.94],
              },
            }}
          >
            <motion.img
              src={heromainimg}
              alt="Sports Equipment"
              whileHover={{
                scale: 1.1,
                rotate: 2,
                transition: { duration: 0.3 },
              }}
            />
          </motion.div>
        </div>
      </motion.section>

      {/* Sports Section */}
      <motion.section
        className="sports-section p-section"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={staggerContainer}
      >
        <div className="sports-container max-container">
          <motion.div className="sports-header mb-30" variants={fadeInUp}>
            <motion.h2
              className="sports-title"
              variants={{
                hidden: { opacity: 0, y: 30 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] },
                },
              }}
            >
              Sports
            </motion.h2>
          </motion.div>

          <motion.div
            className="sports-cards-wrapper"
            variants={staggerContainer}
          >
            <motion.div
              className="sports-cards-container"
              variants={{
                hidden: { opacity: 0 },
                visible: {
                  opacity: 1,
                  transition: {
                    staggerChildren: 0.15,
                    delayChildren: 0.2,
                  },
                },
              }}
            >
              {featuredSports.map((sport) => (
                <motion.div
                  key={sport.id}
                  variants={{
                    hidden: {
                      opacity: 0,
                      y: 50,
                      scale: 0.9,
                    },
                    visible: {
                      opacity: 1,
                      y: 0,
                      scale: 1,
                      transition: {
                        duration: 0.6,
                        ease: [0.25, 0.46, 0.45, 0.94],
                      },
                    },
                  }}
                  whileHover={{
                    y: -10,
                    scale: 1.05,
                    transition: { duration: 0.3 },
                  }}
                >
                  <SportsCard image={sport.image} name={sport.name} />
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* Featured Strategic Content Section */}
      <motion.section
        className="featured-section p-section"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={staggerContainer}
      >
        <div className="featured-container max-container">
          <motion.div
            className="featured-header mb-30"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.3,
                  delayChildren: 0.1,
                },
              },
            }}
          >
            <motion.h2 className="featured-title" variants={fadeInLeft}>
              Top Rated Sports Strategies
            </motion.h2>
            <motion.div variants={fadeInRight}>
              <Link to="/content" className="featured-view-all">
                Learn More Contents
              </Link>
            </motion.div>
          </motion.div>

          <motion.div
            className="featured-grid"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.1,
                  delayChildren: 0.3,
                },
              },
            }}
          >
            {loading ? (
              <motion.div variants={fadeInUp} className="loading-text">
                Loading top rated strategies...
              </motion.div>
            ) : (
              topRatedStrategies.map((strategy, index) => (
                <motion.div
                  key={strategy._id}
                  variants={{
                    hidden: {
                      opacity: 0,
                      y: 60,
                      rotateX: 15,
                    },
                    visible: {
                      opacity: 1,
                      y: 0,
                      rotateX: 0,
                      transition: {
                        duration: 0.8,
                        ease: [0.25, 0.46, 0.45, 0.94],
                        delay: index * 0.1,
                      },
                    },
                  }}
                  whileHover={{
                    y: -8,
                    rotateY: 5,
                    scale: 1.02,
                    transition: { duration: 0.3 },
                  }}
                >
                  <StrategyCard
                    id={strategy._id}
                    image={`${import.meta.env.VITE_IMAGE_BASE_URL}${
                      strategy.thumbnailUrl || strategy.fileUrl
                    }`}
                    title={strategy.title}
                    coach={strategy.coachName}
                    price={
                      strategy.saleType === "Auction" &&
                      strategy.auctionDetails?.basePrice
                        ? strategy.auctionDetails.basePrice
                        : strategy.price
                    }
                    hasVideo={strategy.contentType === "Video"}
                    saleType={strategy.saleType}
                    auctionDetails={strategy.auctionDetails}
                  />
                </motion.div>
              ))
            )}
          </motion.div>
        </div>
      </motion.section>

      {/* Our Mission Section */}
      <motion.section
        className="mission-section p-section"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={staggerContainer}
      >
        <div className="mission-container max-container">
          <motion.div
            className="mission-image"
            variants={{
              hidden: { opacity: 0, x: -80, scale: 0.9 },
              visible: {
                opacity: 1,
                x: 0,
                scale: 1,
                transition: {
                  duration: 1,
                  ease: [0.25, 0.46, 0.45, 0.94],
                },
              },
            }}
            whileHover={{
              scale: 1.05,
              transition: { duration: 0.3 },
            }}
          >
            <motion.img
              src={ourmissionimage}
              alt="Coaching Session"
              whileHover={{
                scale: 1.1,
                transition: { duration: 0.3 },
              }}
            />
          </motion.div>

          <motion.div
            className="mission-content"
            variants={{
              hidden: { opacity: 0, x: 80 },
              visible: {
                opacity: 1,
                x: 0,
                transition: {
                  duration: 1,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  staggerChildren: 0.2,
                  delayChildren: 0.3,
                },
              },
            }}
          >
            <motion.h2
              className="mission-title mb-20"
              variants={{
                hidden: { opacity: 0, y: 30 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.8 },
                },
              }}
            >
              Our Mission
            </motion.h2>

            <motion.p
              className="mission-description mb-30"
              variants={{
                hidden: { opacity: 0, y: 30 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.8 },
                },
              }}
            >
              Build a digital marketplace where sports professionals, coaches,
              and enthusiasts can exchange sports strategies—videos, PDFs,
              playbooks, and custom requests.
            </motion.p>

            <motion.div
              variants={{
                hidden: { opacity: 0, y: 30 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.8 },
                },
              }}
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.2 },
              }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                to="/info"
                className="btn-outline"
                onClick={handleStartTradingClick}
              >
                Start Trading Strategies Today
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* What We Offer + Why Join Our Marketplace Section */}
      <motion.section
        className="offer-join-section p-section"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={staggerContainer}
      >
        <div className="offer-join-container max-container">
          <motion.div
            className="offer-column"
            variants={{
              hidden: { opacity: 0, x: -60 },
              visible: {
                opacity: 1,
                x: 0,
                transition: {
                  duration: 0.8,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  staggerChildren: 0.15,
                  delayChildren: 0.2,
                },
              },
            }}
          >
            <motion.h2
              className="offer-title mb-20"
              variants={{
                hidden: { opacity: 0, y: 30 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.8 },
                },
              }}
            >
              What We Offer
            </motion.h2>

            <motion.ul className="offer-list" variants={staggerContainer}>
              {[
                {
                  icon: TbLockDollar,
                  text: "Fixed-Price Listings And Bidding Options For Exclusive Sports Digital Content",
                },
                {
                  icon: TbPencilDollar,
                  text: "Buyer Requests For Tailored Playbook, Opponent Scouting Reports, Meal Plans Or Wellness Guides, etc.",
                },
                { icon: GoShieldCheck, text: "Secure, Cloud-Based Hosting" },
                {
                  icon: LiaHandHoldingUsdSolid,
                  text: "Transparent Commission-Based Payments For Creators And The Platform",
                },
                {
                  icon: BsDatabaseLock,
                  text: "Focus On Seller Credibility, Data Security, And High-Quality Content",
                },
              ].map((item, index) => (
                <motion.li
                  key={index}
                  className="offer-item mb-20"
                  variants={{
                    hidden: { opacity: 0, x: -30, y: 20 },
                    visible: {
                      opacity: 1,
                      x: 0,
                      y: 0,
                      transition: {
                        duration: 0.6,
                        ease: [0.25, 0.46, 0.45, 0.94],
                      },
                    },
                  }}
                  whileHover={{
                    x: 10,
                    transition: { duration: 0.3 },
                  }}
                >
                  <motion.div
                    className="icon-container"
                    whileHover={{
                      scale: 1.2,
                      rotate: 5,
                      transition: { duration: 0.3 },
                    }}
                  >
                    <item.icon className="offer-icon" />
                  </motion.div>
                  <div className="offer-text">
                    <h3>{item.text}</h3>
                  </div>
                </motion.li>
              ))}
            </motion.ul>
          </motion.div>

          <motion.div
            className="vertical-line"
            variants={{
              hidden: { opacity: 0, scaleY: 0 },
              visible: {
                opacity: 1,
                scaleY: 1,
                transition: {
                  duration: 1.2,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  delay: 0.5,
                },
              },
            }}
          >
            <motion.img
              src={verticallineimg}
              alt="verticallineimg"
              style={{ originY: 0 }}
            />
          </motion.div>

          <motion.div
            className="join-column"
            variants={{
              hidden: { opacity: 0, x: 60 },
              visible: {
                opacity: 1,
                x: 0,
                transition: {
                  duration: 0.8,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  staggerChildren: 0.15,
                  delayChildren: 0.4,
                },
              },
            }}
          >
            <motion.h2
              className="join-title mb-20"
              variants={{
                hidden: { opacity: 0, y: 30 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.8 },
                },
              }}
            >
              Why Join Our Marketplace?
            </motion.h2>

            <motion.ul className="join-list" variants={staggerContainer}>
              {[
                {
                  icon: GiTeacher,
                  title: "Access Expert Strategies",
                  desc: "Explore And Purchase Strategies Curated By Top Sports Minds",
                },
                {
                  icon: GrTransaction,
                  title: "Flexible Transactions",
                  desc: "Choose Between Fixed Prices Or Competitive Bidding, With Options For Custom Content Requests",
                },
                {
                  icon: MdSecurity,
                  title: "Secure & Protected",
                  desc: "Scalable Cloud Hosting And Verified Sellers Ensure Safety And Trust",
                },
                {
                  icon: FaRegHandshake,
                  title: "Fair & Transparent",
                  desc: "Clear Fee Structures And Secure Payment Processing Support A Trustworthy Environment",
                },
              ].map((item, index) => (
                <motion.li
                  key={index}
                  className="join-item mb-20"
                  variants={{
                    hidden: { opacity: 0, x: 30, y: 20 },
                    visible: {
                      opacity: 1,
                      x: 0,
                      y: 0,
                      transition: {
                        duration: 0.6,
                        ease: [0.25, 0.46, 0.45, 0.94],
                      },
                    },
                  }}
                  whileHover={{
                    x: -10,
                    transition: { duration: 0.3 },
                  }}
                >
                  <motion.div
                    className="icon-container"
                    whileHover={{
                      scale: 1.2,
                      rotate: -5,
                      transition: { duration: 0.3 },
                    }}
                  >
                    <item.icon className="join-icon" />
                  </motion.div>
                  <div className="join-text">
                    <h3>{item.title}</h3>
                    <p>{item.desc}</p>
                  </div>
                </motion.li>
              ))}
            </motion.ul>
          </motion.div>
        </div>
      </motion.section>

      {/* Ready to Elevate Your Game? Section */}
      <motion.section
        className="cta-section p-section"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={staggerContainer}
      >
        <motion.div
          className="cta-container max-container"
          variants={{
            hidden: { opacity: 0 },
            visible: {
              opacity: 1,
              transition: {
                staggerChildren: 0.3,
                delayChildren: 0.2,
              },
            },
          }}
        >
          <motion.h2
            className="cta-title mb-10"
            variants={{
              hidden: { opacity: 0, y: 50, scale: 0.9 },
              visible: {
                opacity: 1,
                y: 0,
                scale: 1,
                transition: {
                  duration: 0.8,
                  ease: [0.25, 0.46, 0.45, 0.94],
                },
              },
            }}
          >
            Ready to Elevate Your Game?
          </motion.h2>

          <motion.p
            className="cta-description mb-25"
            variants={{
              hidden: { opacity: 0, y: 30 },
              visible: {
                opacity: 1,
                y: 0,
                transition: {
                  duration: 0.8,
                  ease: [0.25, 0.46, 0.45, 0.94],
                },
              },
            }}
          >
            Join our platform now and start trading or requesting winning sports
            strategies!
          </motion.p>

          <motion.div
            variants={{
              hidden: { opacity: 0, y: 30, scale: 0.8 },
              visible: {
                opacity: 1,
                y: 0,
                scale: 1,
                transition: {
                  duration: 0.8,
                  ease: [0.25, 0.46, 0.45, 0.94],
                },
              },
            }}
            whileHover={{
              scale: 1.1,
              y: -5,
              transition: { duration: 0.3 },
            }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              to="/auth"
              className="btn btn-primary mt-20"
              onClick={handleStartTradingClick}
            >
              Join The Strategy Exchange
            </Link>
          </motion.div>
        </motion.div>
      </motion.section>
    </div>
  );
};

export default Home;
