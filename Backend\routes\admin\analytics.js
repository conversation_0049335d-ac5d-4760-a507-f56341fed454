const express = require('express');
const { check } = require('express-validator');
const {
  getDashboardAnalytics,
  getUserAnalytics,
  getContentAnalytics,
  getRevenueAnalytics,
  getSalesAnalytics,
  getTrafficAnalytics,
  getConversionAnalytics,
  getCustomAnalytics,
  getRealTimeAnalytics,
  getComparativeAnalytics,
  exportAnalytics
} = require('../../controllers/admin/analytics');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin analytics routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Dashboard analytics (overview)
router.get('/dashboard', [
  check('period', 'Period must be valid').optional().isIn(['7d', '30d', '90d', '1y', 'all']),
], getDashboardAnalytics);

// User analytics
router.get('/users', [
  check('startDate', 'Start date must be valid').optional().isISO8601(),
  check('endDate', 'End date must be valid').optional().isISO8601(),
  check('userType', 'User type must be valid').optional().isIn(['buyer', 'seller', 'all']),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month']),
], getUserAnalytics);

// Content analytics
router.get('/content', [
  check('startDate', 'Start date must be valid').optional().isISO8601(),
  check('endDate', 'End date must be valid').optional().isISO8601(),
  check('contentType', 'Content type must be valid').optional(),
  check('sport', 'Sport must be valid').optional(),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month']),
], getContentAnalytics);

// Revenue analytics
router.get('/revenue', [
  check('startDate', 'Start date must be valid').optional().isISO8601(),
  check('endDate', 'End date must be valid').optional().isISO8601(),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month']),
  check('currency', 'Currency must be valid').optional().isIn(['USD', 'EUR', 'GBP']),
], getRevenueAnalytics);

// Sales analytics
router.get('/sales', [
  check('startDate', 'Start date must be valid').optional().isISO8601(),
  check('endDate', 'End date must be valid').optional().isISO8601(),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month']),
  check('saleType', 'Sale type must be valid').optional().isIn(['direct', 'auction', 'all']),
], getSalesAnalytics);

// Traffic analytics
router.get('/traffic', [
  check('startDate', 'Start date must be valid').optional().isISO8601(),
  check('endDate', 'End date must be valid').optional().isISO8601(),
  check('source', 'Traffic source must be valid').optional(),
], getTrafficAnalytics);

// Conversion analytics
router.get('/conversion', [
  check('startDate', 'Start date must be valid').optional().isISO8601(),
  check('endDate', 'End date must be valid').optional().isISO8601(),
  check('funnelType', 'Funnel type must be valid').optional().isIn(['registration', 'purchase', 'seller_onboarding']),
], getConversionAnalytics);

// Real-time analytics
router.get('/realtime', getRealTimeAnalytics);

// Custom analytics
router.post('/custom', [
  check('metrics', 'Metrics array is required').isArray(),
  check('dimensions', 'Dimensions array is required').isArray(),
  check('filters', 'Filters must be an object').optional().isObject(),
  check('startDate', 'Start date must be valid').optional().isISO8601(),
  check('endDate', 'End date must be valid').optional().isISO8601(),
], getCustomAnalytics);

// Comparative analytics
router.post('/compare', [
  check('metric', 'Metric is required').not().isEmpty(),
  check('periods', 'Periods array is required').isArray(),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month']),
], getComparativeAnalytics);

// Export analytics
router.post('/export', [
  check('analyticsType', 'Analytics type is required').not().isEmpty(),
  check('format', 'Format must be valid').isIn(['csv', 'excel', 'json']),
  check('parameters', 'Parameters must be an object').isObject(),
], exportAnalytics);

module.exports = router;
