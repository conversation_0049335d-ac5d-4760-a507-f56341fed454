const User = require('../models/User');

/**
 * Creates a default admin user if it doesn't exist
 */
async function createDefaultAdmin() {
  try {
    // Check if admin user already exists
    const adminExists = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminExists) {
      // Create default admin user
      const adminUser = await User.create({
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        mobile: '+15551234567',
        role: 'admin',
        isVerified: true,
        status: 1
      });

      console.log('✅ Default admin user created successfully');
    } else {
      console.log('ℹ️ Default admin user already exists');
    }
  } catch (error) {
    console.error('❌ Error creating default admin user:', error.message);
  }
}

module.exports = createDefaultAdmin; 