import React from "react";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  selectActiveTab,
  setActiveTab,
} from "../../redux/slices/adminDashboardSlice";
import { logout } from "../../redux/slices/authSlice";
import "../../styles/AdminSidebar.css";

// Icons
import { MdDashboard, MdPeople, MdVideoLibrary, MdRateReview } from "react-icons/md";
import { FaChartBar, FaFileAlt, FaCog, FaGavel, FaHandshake, FaClipboardList } from "react-icons/fa";
import { IoLogOut } from "react-icons/io5";

const AdminSidebar = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const activeTab = useSelector(selectActiveTab);

  // Handle tab click
  const handleTabClick = (tab) => {
    dispatch(setActiveTab(tab));

    // Navigate to the corresponding route
    switch (tab) {
      case "dashboard":
        navigate("/admin/dashboard");
        break;
      case "users":
        navigate("/admin/users");
        break;
      case "content":
        navigate("/admin/content");
        break;
      case "bids":
        navigate("/admin/bids");
        break;
      case "offers":
        navigate("/admin/offers");
        break;
      case "requests":
        navigate("/admin/requests");
        break;
      case "reviews":
        navigate("/admin/reviews");
        break;
      case "reports":
        navigate("/admin/reports");
        break;
      case "cms":
        navigate("/admin/cms");
        break;
      case "settings":
        navigate("/admin/settings");
        break;
      default:
        navigate("/admin/dashboard");
    }
  };

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate("/");
  };

  return (
    <div className="AdminSidebar">
      <div className="AdminSidebar__container">
        {/* Logo Section */}
        <div className="AdminSidebar__logo">
          <h3>XOSportsHub</h3>
          <span>Admin Panel</span>
        </div>

        {/* Navigation Menu */}
        <ul className="AdminSidebar__menu">
          <li
            className={`AdminSidebar__item ${activeTab === "dashboard" ? "active" : ""
              }`}
            onClick={() => handleTabClick("dashboard")}
            data-tooltip="Dashboard Overview"
          >
            <MdDashboard className="AdminSidebar__icon" />
            <span>Dashboard Overview</span>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "users" ? "active" : ""
              }`}
            onClick={() => handleTabClick("users")}
            data-tooltip="User Management"
          >
            <MdPeople className="AdminSidebar__icon" />
            <span>User Management</span>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "content" ? "active" : ""
              }`}
            onClick={() => handleTabClick("content")}
            data-tooltip="Content Management"
          >
            <MdVideoLibrary className="AdminSidebar__icon" />
            <span>Content Management</span>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "bids" ? "active" : ""
              }`}
            onClick={() => handleTabClick("bids")}
            data-tooltip="Bid Management"
          >
            <FaGavel className="AdminSidebar__icon" />
            <span>Bid Management</span>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "offers" ? "active" : ""
              }`}
            onClick={() => handleTabClick("offers")}
            data-tooltip="Offer Management"
          >
            <FaHandshake className="AdminSidebar__icon" />
            <span>Offer Management</span>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "requests" ? "active" : ""
              }`}
            onClick={() => handleTabClick("requests")}
            data-tooltip="Request Management"
          >
            <FaClipboardList className="AdminSidebar__icon" />
            <span>Request Management</span>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "reviews" ? "active" : ""
              }`}
            onClick={() => handleTabClick("reviews")}
            data-tooltip="Review Management"
          >
            <MdRateReview className="AdminSidebar__icon" />
            <span>Review Management</span>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "reports" ? "active" : ""
              }`}
            onClick={() => handleTabClick("reports")}
            data-tooltip="Reports & Analytics"
          >
            <FaChartBar className="AdminSidebar__icon" />
            <span>Reports & Analytics</span>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "cms" ? "active" : ""
              }`}
            onClick={() => handleTabClick("cms")}
            data-tooltip="CMS Pages"
          >
            <FaFileAlt className="AdminSidebar__icon" />
            <span>CMS Pages</span>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "settings" ? "active" : ""
              }`}
            onClick={() => handleTabClick("settings")}
            data-tooltip="Settings"
          >
            <FaCog className="AdminSidebar__icon" />
            <span>Settings</span>
          </li>
        </ul>

        {/* Logout Section */}
        <div className="AdminSidebar__logout">
          <div
            className="AdminSidebar__item logout-item"
            onClick={handleLogout}
            data-tooltip="Logout"
          >
            <IoLogOut className="AdminSidebar__icon" />
            <span>Logout</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSidebar;
