const express = require('express');
const { check } = require('express-validator');
const {
  getAllCMSPages,
  getCMSPageById,
  createCMSPage,
  updateCMSPage,
  deleteCMSPage,
  bulkDeleteCMSPages,
  publishCMSPage,
  unpublishCMSPage,
  getCMSStats,
  exportCMSPages,
  duplicateCMSPage,
  getCMSPageBySlug,
  updateCMSPageStatus
} = require('../../controllers/admin/cms');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin CMS routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get all CMS pages with filtering, sorting, and pagination
router.get('/', getAllCMSPages);

// Get CMS statistics
router.get('/stats', getCMSStats);

// Export CMS pages data
router.get('/export', exportCMSPages);

// Create new CMS page
router.post('/', [
  check('title', 'Title is required').not().isEmpty(),
  check('slug', 'Slug is required').not().isEmpty(),
  check('content', 'Content is required').not().isEmpty(),
  check('status', 'Status is required').isIn(['draft', 'published', 'archived']),
], createCMSPage);

// Bulk operations
router.post('/bulk-delete', [
  check('pageIds', 'Page IDs array is required').isArray(),
], bulkDeleteCMSPages);

// Get CMS page by slug (for preview)
router.get('/slug/:slug', getCMSPageBySlug);

// Individual CMS page operations
router.get('/:id', getCMSPageById);

router.put('/:id', [
  check('title', 'Title is required').not().isEmpty(),
  check('slug', 'Slug is required').not().isEmpty(),
  check('content', 'Content is required').not().isEmpty(),
], updateCMSPage);

router.delete('/:id', deleteCMSPage);

// CMS page status management
router.put('/:id/status', [
  check('status', 'Status is required').isIn(['draft', 'published', 'archived']),
], updateCMSPageStatus);

router.put('/:id/publish', publishCMSPage);
router.put('/:id/unpublish', unpublishCMSPage);

// Duplicate CMS page
router.post('/:id/duplicate', [
  check('title', 'New title is required').not().isEmpty(),
  check('slug', 'New slug is required').not().isEmpty(),
], duplicateCMSPage);

module.exports = router;
