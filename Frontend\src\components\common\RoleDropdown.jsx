import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toggleRole } from '../../redux/slices/authSlice';
import { needsSellerOnboarding } from '../../utils/sellerUtils';
import { FaChevronDown, FaUser, FaStore } from 'react-icons/fa';
import '../../styles/RoleDropdown.css';

const RoleDropdown = ({ isMobile = false }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user, isLoading } = useSelector((state) => state.auth);
  const [isToggling, setIsToggling] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Don't render if user is admin or not authenticated - check this AFTER all hooks
  if (!user || user.role === 'admin') {
    return null;
  }

  const currentActiveRole = user.activeRole || user.role;
  const isBuyerMode = currentActiveRole === 'buyer';

  const handleToggle = async (targetRole) => {
    if (isToggling || isLoading || targetRole === currentActiveRole) return;
    
    setIsToggling(true);
    setIsOpen(false);
    
    try {
      const result = await dispatch(toggleRole()).unwrap();
      
      // Determine the appropriate redirect path based on the new role
      let redirectPath;
      if (result.data.activeRole === 'buyer') {
        redirectPath = '/content';
      } else {
        redirectPath = needsSellerOnboarding(result.data) ? '/seller-onboarding' : '/seller/dashboard';
      }

      // Use replace to avoid adding to history stack
      navigate(redirectPath, { replace: true });
    } catch {
      // Silently handle error and redirect to home
      navigate('/', { replace: true });
    } finally {
      setIsToggling(false);
    }
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className={`role-dropdown ${isMobile ? 'role-dropdown--mobile' : 'role-dropdown--desktop'}`} ref={dropdownRef}>
      <div 
        className={`role-dropdown__trigger ${isToggling ? 'role-dropdown__trigger--loading' : ''}`}
        onClick={toggleDropdown}
      >
        <div className="role-dropdown__current">
          {isBuyerMode ? (
            <>
              <FaUser className="role-dropdown__icon" />
              <span>Buyer Mode</span>
            </>
          ) : (
            <>
              <FaStore className="role-dropdown__icon" />
              <span>Seller Mode</span>
            </>
          )}
        </div>
        <FaChevronDown className={`role-dropdown__arrow ${isOpen ? 'role-dropdown__arrow--open' : ''}`} />
      </div>

      {isOpen && (
        <div className="role-dropdown__menu">
          <div 
            className={`role-dropdown__option ${isBuyerMode ? 'role-dropdown__option--active' : ''}`}
            onClick={() => handleToggle('buyer')}
          >
            <FaUser className="role-dropdown__icon" />
            <span>Buyer Mode</span>
          </div>
          <div 
            className={`role-dropdown__option ${!isBuyerMode ? 'role-dropdown__option--active' : ''}`}
            onClick={() => handleToggle('seller')}
          >
            <FaStore className="role-dropdown__icon" />
            <span>Seller Mode</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoleDropdown;
