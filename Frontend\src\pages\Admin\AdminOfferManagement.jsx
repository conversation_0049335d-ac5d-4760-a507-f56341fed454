import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectOffers,
  selectSelectedOffers,
  selectLoading,
  selectOfferFilters,
  selectOffersPagination,
  setSelectedOffers,
  showOfferDetailModal,
  setOfferFilters,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchOffers,
  approveOffer,
  rejectOffer,
  deleteOffer,
  bulkApproveOffers,
  bulkRejectOffers,
  bulkDeleteOffers,
  getOfferStats,
} from "../../redux/slices/adminDashboardThunks";
import AdminLayout from "../../components/admin/AdminLayout";
import "../../styles/AdminOfferManagement.css";
import { showSuccess, showError } from "../../utils/toast";

// Icons
import { FaSearch, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaEdit, <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaTimes } from "react-icons/fa";

const AdminOfferManagement = () => {
  const dispatch = useDispatch();
  const offers = useSelector(selectOffers);
  const selectedOffers = useSelector(selectSelectedOffers);
  const loading = useSelector(selectLoading);
  const filters = useSelector(selectOfferFilters);
  const pagination = useSelector(selectOffersPagination);

  // Use API data if available, otherwise fall back to empty array
  const displayOffers = offers?.data || [];

  const [searchTerm, setSearchTerm] = useState(filters.search || "");
  const [statusFilter, setStatusFilter] = useState(filters.status || "all");

  // Handle page change
  const handlePageChange = (newPage) => {
    dispatch(fetchOffers({
      page: newPage,
      limit: pagination?.limit || 10,
      search: searchTerm,
      status: statusFilter !== 'all' ? statusFilter : '',
      sortBy: 'createdAt',
      sortOrder: 'desc'
    }));
  };

  // Initial fetch on component mount
  useEffect(() => {
    dispatch(fetchOffers({
      page: 1,
      limit: 10,
      search: '',
      status: '',
      sortBy: 'createdAt',
      sortOrder: 'desc'
    }));
    dispatch(getOfferStats());
  }, [dispatch]);

  // Handle search and filter changes
  useEffect(() => {
    const timer = setTimeout(() => {
      dispatch(setOfferFilters({
        search: searchTerm,
        status: statusFilter
      }));
      dispatch(fetchOffers({
        page: 1,
        limit: 10,
        search: searchTerm,
        status: statusFilter !== 'all' ? statusFilter : '',
        sortBy: 'createdAt',
        sortOrder: 'desc'
      }));
    }, 500);

    return () => clearTimeout(timer);
  }, [dispatch, searchTerm, statusFilter]);

  // Handle select all
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      dispatch(setSelectedOffers(displayOffers.map(offer => offer._id)));
    } else {
      dispatch(setSelectedOffers([]));
    }
  };

  // Handle individual select
  const handleSelectOffer = (offerId) => {
    const newSelection = selectedOffers.includes(offerId)
      ? selectedOffers.filter(id => id !== offerId)
      : [...selectedOffers, offerId];
    dispatch(setSelectedOffers(newSelection));
  };

  // Handle offer actions
  const handleOfferAction = async (offerItem, action) => {
    const offerId = offerItem._id;
    const displayOfferId = offerItem.displayId || offerId;
    const contentTitle = offerItem.content?.title || 'Unknown Content';
    let reason;

    switch (action) {
      case 'view':
        dispatch(showOfferDetailModal(offerItem));
        break;
      case 'approve':
        if (window.confirm(`Approve offer "${displayOfferId}"?`)) {
          try {
            await dispatch(approveOffer({ id: offerId, approvalNotes: '' })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'offer_approval',
              description: `Offer approved: ${displayOfferId} for ${contentTitle}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            showSuccess(`Offer "${displayOfferId}" has been approved!`);
          } catch (error) {
            showError(`Failed to approve offer: ${error}`);
          }
        }
        break;
      case 'reject':
        reason = prompt(`Reason for rejecting offer "${displayOfferId}":`);
        if (reason) {
          try {
            await dispatch(rejectOffer({ id: offerId, reason, rejectionNotes: '' })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'offer_rejection',
              description: `Offer rejected: ${displayOfferId} - Reason: ${reason}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            showSuccess(`Offer "${displayOfferId}" has been rejected.`);
          } catch (error) {
            showError(`Failed to reject offer: ${error}`);
          }
        }
        break;
      case 'delete':
        if (window.confirm(`Delete offer "${displayOfferId}"? This action cannot be undone.`)) {
          try {
            await dispatch(deleteOffer(offerId)).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'offer_deletion',
              description: `Offer deleted: ${displayOfferId}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            showSuccess(`Offer "${displayOfferId}" has been deleted.`);
          } catch (error) {
            showError(`Failed to delete offer: ${error}`);
          }
        }
        break;
      default:
        break;
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action) => {
    if (selectedOffers.length === 0) {
      showError('Please select offers first');
      return;
    }

    let reason;

    switch (action) {
      case 'approve':
        if (window.confirm(`Approve ${selectedOffers.length} selected offers?`)) {
          try {
            await dispatch(bulkApproveOffers({ offerIds: selectedOffers, approvalNotes: '' })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_offer_approval',
              description: `Bulk approved ${selectedOffers.length} offers`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            showSuccess(`${selectedOffers.length} offers approved`);
            dispatch(setSelectedOffers([]));
          } catch (error) {
            showError(`Failed to approve offers: ${error}`);
          }
        }
        break;
      case 'reject':
        reason = prompt(`Reason for rejecting ${selectedOffers.length} offers:`);
        if (reason) {
          try {
            await dispatch(bulkRejectOffers({ offerIds: selectedOffers, reason })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_offer_rejection',
              description: `Bulk rejected ${selectedOffers.length} offers - Reason: ${reason}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            showSuccess(`${selectedOffers.length} offers rejected`);
            dispatch(setSelectedOffers([]));
          } catch (error) {
            showError(`Failed to reject offers: ${error}`);
          }
        }
        break;
      case 'delete':
        if (window.confirm(`Delete ${selectedOffers.length} selected offers? This action cannot be undone.`)) {
          try {
            await dispatch(bulkDeleteOffers(selectedOffers)).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_offer_deletion',
              description: `Bulk deleted ${selectedOffers.length} offers`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            showSuccess(`${selectedOffers.length} offers deleted`);
            dispatch(setSelectedOffers([]));
          } catch (error) {
            showError(`Failed to delete offers: ${error}`);
          }
        }
        break;
      default:
        break;
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <AdminLayout>
      <div className="admin-offer-management">
        <h2>Offer Management</h2>
        
        {/* Search and Filter Section */}
        <div className="controls-section">
          <div className="search-box">
            <FaSearch />
            <input
              type="text"
              placeholder="Search offers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="filter-box">
            <FaFilter />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="Pending">Pending</option>
              <option value="Accepted">Accepted</option>
              <option value="Rejected">Rejected</option>
              <option value="Expired">Expired</option>
            </select>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedOffers.length > 0 && (
          <div className="bulk-actions">
            <span>{selectedOffers.length} selected</span>
            <button onClick={() => handleBulkAction('approve')} className="approve">
              <FaCheck /> Approve Selected
            </button>
            <button onClick={() => handleBulkAction('reject')} className="reject">
              <FaTimes /> Reject Selected
            </button>
            <button onClick={() => handleBulkAction('delete')} className="delete">
              <FaTrash /> Delete Selected
            </button>
          </div>
        )}

        {/* Offers Table */}
        <div className="table-container">
          <table>
            <thead>
              <tr>
                <th>
                  <input
                    type="checkbox"
                    checked={selectedOffers.length === displayOffers.length && displayOffers.length > 0}
                    onChange={handleSelectAll}
                  />
                </th>
                <th>Content</th>
                <th>Buyer</th>
                <th>Seller</th>
                <th>Amount</th>
                <th>Status</th>
                <th>Message</th>
                <th>Created</th>
                <th>Expires</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {loading.offers ? (
                <tr>
                  <td colSpan="10" className="loading">Loading offers...</td>
                </tr>
              ) : displayOffers.length === 0 ? (
                <tr>
                  <td colSpan="10" className="no-data">No offers found</td>
                </tr>
              ) : (
                displayOffers.map((offer) => (
                  <tr key={offer._id}>
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedOffers.includes(offer._id)}
                        onChange={() => handleSelectOffer(offer._id)}
                      />
                    </td>
                    <td>
                      <div className="content-cell">
                        <span className="title">{offer.content?.title || 'Unknown'}</span>
                        <span className="details">
                          {offer.content?.sport || 'Unknown'} | {offer.content?.contentType || 'Unknown'}
                        </span>
                      </div>
                    </td>
                    <td>
                      <div className="user-cell">
                        <img 
                          src={offer.buyer?.profileImage || 'default-profile.jpg'} 
                          alt={offer.buyer?.fullName || 'Unknown User'}
                          className="profile-image"
                        />
                        <div className="user-info">
                          <span className="name">{offer.buyer?.fullName || 'Unknown User'}</span>
                          <span className="email">{offer.buyer?.email || 'No email'}</span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div className="user-cell">
                        <img 
                          src={offer.seller?.profileImage || 'default-profile.jpg'} 
                          alt={offer.seller?.fullName || 'Unknown User'}
                          className="profile-image"
                        />
                        <div className="user-info">
                          <span className="name">{offer.seller?.fullName || 'Unknown User'}</span>
                          <span className="email">{offer.seller?.email || 'No email'}</span>
                        </div>
                      </div>
                    </td>
                    <td>{formatCurrency(offer.amount || 0)}</td>
                    <td>
                      <span className={`status-badge ${(offer.status || 'unknown').toLowerCase()}`}>
                        {offer.status || 'Unknown'}
                      </span>
                    </td>
                    <td>
                      <div className="message-cell" title={offer.message || 'No message'}>
                        {(offer.message || 'No message').length > 30 
                          ? `${(offer.message || 'No message').substring(0, 30)}...` 
                          : (offer.message || 'No message')}
                      </div>
                    </td>
                    <td>{formatDate(offer.createdAt)}</td>
                    <td>{formatDate(offer.expiresAt)}</td>
                    <td className="actions">
                      <button
                        onClick={() => handleOfferAction(offer, 'view')}
                        className="view"
                        title="View Details"
                      >
                        <FaEye />
                      </button>
                      {offer.status === 'Pending' && (
                        <>
                          <button
                            onClick={() => handleOfferAction(offer, 'approve')}
                            className="approve"
                            title="Approve Offer"
                          >
                            <FaCheck />
                          </button>
                          <button
                            onClick={() => handleOfferAction(offer, 'reject')}
                            className="reject"
                            title="Reject Offer"
                          >
                            <FaTimes />
                          </button>
                        </>
                      )}
                      <button
                        onClick={() => handleOfferAction(offer, 'delete')}
                        className="delete"
                        title="Delete Offer"
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {offers?.pagination && offers.pagination.pages > 1 && (
          <div className="pagination">
            <button
              disabled={offers.pagination.page === 1}
              onClick={() => handlePageChange(offers.pagination.page - 1)}
            >
              Previous
            </button>
            <span>
              Page {offers.pagination.page} of {offers.pagination.pages}
            </span>
            <button
              disabled={offers.pagination.page === offers.pagination.pages}
              onClick={() => handlePageChange(offers.pagination.page + 1)}
            >
              Next
            </button>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminOfferManagement;