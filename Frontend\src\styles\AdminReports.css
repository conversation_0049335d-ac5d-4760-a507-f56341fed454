/* AdminReports Component Styles */
.AdminReports {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Header */
.AdminReports__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--heading6);
  margin-bottom: var(--basefont);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.period-selector {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.calendar-icon {
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.period-select {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  cursor: pointer;
}

.period-select:focus {
  outline: none;
  border-color: var(--btn-color);
}

.header-right {
  display: flex;
  gap: var(--smallfont);
}

.btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.btn.btn-primary:hover {
  background-color: #d32f2f;
}

.btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.btn.btn-outline:hover {
  background-color: var(--bg-gray);
}

/* Metrics Cards */
.AdminReports__metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--heading6);
  margin-bottom: var(--heading6);
}

.metric-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: var(--heading6);
  box-shadow: var(--box-shadow-light);
  border-left: 4px solid;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.metric-card.revenue {
  border-left-color: var(--btn-color);
}

.metric-card.users {
  border-left-color: #3b82f6;
}

.metric-card.content {
  border-left-color: #10b981;
}

.metric-card.monthly {
  border-left-color: #f59e0b;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  font-size: var(--heading5);
  color: var(--white);
}

.metric-card.revenue .metric-icon {
  background-color: var(--btn-color);
}

.metric-card.users .metric-icon {
  background-color: #3b82f6;
}

.metric-card.content .metric-icon {
  background-color: #10b981;
}

.metric-card.monthly .metric-icon {
  background-color: #f59e0b;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: var(--smallfont);
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
}

.metric-trend.positive {
  background-color: #dcfce7;
  color: #166534;
}

.metric-trend.negative {
  background-color: #fef2f2;
  color: #991b1b;
}

.metric-content {
  text-align: left;
}

.metric-number {
  font-size: var(--heading3);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 4px;
}

.metric-label {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 2px;
}

.metric-sublabel {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Charts Section */
.AdminReports__charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--heading6);
  margin-bottom: var(--heading6);
}

.chart-container {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.chart-header {
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
}

.chart-header h3 {
  margin: 0 0 4px 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.chart-header p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.chart-placeholder {
  padding: var(--heading6);
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-mock {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 80%;
  gap: var(--smallfont);
}

.chart-bar {
  flex: 1;
  background-color: var(--btn-color);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  position: relative;
  min-height: 20px;
  transition: all 0.3s ease;
  display: flex;
  align-items: end;
  justify-content: center;
}

.chart-bar.users-bar {
  background-color: #3b82f6;
}

.chart-bar:hover {
  opacity: 0.8;
}

.bar-value {
  position: absolute;
  top: -25px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  color: var(--secondary-color);
  white-space: nowrap;
}

.chart-labels {
  display: flex;
  justify-content: space-around;
  margin-top: var(--smallfont);
  height: 20%;
}

.chart-label {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  text-align: center;
}

/* Distribution Charts */
.AdminReports__distributions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--heading6);
}

.distribution-container {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.distribution-header {
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
}

.distribution-header h3 {
  margin: 0 0 4px 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.distribution-header p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.distribution-chart {
  padding: var(--heading6);
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.distribution-bar {
  flex: 1;
  height: 20px;
  background-color: var(--bg-gray);
  border-radius: 10px;
  overflow: hidden;
}

.distribution-fill {
  height: 100%;
  background-color: var(--btn-color);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.distribution-fill.revenue-fill {
  background-color: #10b981;
}

.distribution-info {
  display: flex;
  flex-direction: column;
  min-width: 120px;
}

.distribution-label {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 600;
}

.distribution-value {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .AdminReports__charts,
  .AdminReports__distributions {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .AdminReports__header {
    flex-direction: column;
    align-items: stretch;
  }

  .header-right {
    justify-content: center;
  }

  .AdminReports__metrics {
    grid-template-columns: 1fr 1fr;
  }

  .metric-card {
    padding: var(--basefont);
  }

  .metric-icon {
    width: 40px;
    height: 40px;
    font-size: var(--heading6);
  }

  .metric-number {
    font-size: var(--heading4);
  }

  .chart-placeholder {
    height: 250px;
  }

  .distribution-info {
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .AdminReports__metrics {
    grid-template-columns: 1fr;
  }

  .metric-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .chart-placeholder {
    height: 200px;
    padding: var(--basefont);
  }

  .bar-value {
    display: none;
  }

  .distribution-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }

  .distribution-info {
    min-width: auto;
    flex-direction: row;
    justify-content: space-between;
  }
}
