const express = require('express');
const { check } = require('express-validator');
const {
  getAllReviews,
  getReviewById,
  updateReviewStatus,
  deleteReview,
  bulkUpdateReviews,
  bulkDeleteReviews,
  getReviewStats,
  exportReviews,
  getReviewAnalytics,
  flagReview,
  unflagReview,
  moderateReview,
  getReviewTimeline
} = require('../../controllers/admin/reviews');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin review routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get all reviews with filtering, sorting, and pagination
router.get('/', getAllReviews);

// Get review statistics
router.get('/stats', getReviewStats);

// Get review analytics
router.get('/analytics', [
  check('period', 'Period must be valid').optional().isIn(['7d', '30d', '90d', '1y', 'all']),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month']),
], getReviewAnalytics);

// Export reviews data
router.get('/export', exportReviews);

// Bulk operations
router.post('/bulk-update', [
  check('reviewIds', 'Review IDs array is required').isArray(),
  check('status', 'Status is required').isIn(['approved', 'pending', 'rejected']),
], bulkUpdateReviews);

router.post('/bulk-delete', [
  check('reviewIds', 'Review IDs array is required').isArray(),
], bulkDeleteReviews);

// Individual review operations
router.get('/:id', getReviewById);

router.put('/:id/status', [
  check('status', 'Status is required').isIn(['approved', 'pending', 'rejected']),
  check('notes', 'Notes must be a string').optional().isString(),
], updateReviewStatus);

router.get('/:id/timeline', getReviewTimeline);

router.put('/:id/flag', [
  check('reason', 'Flag reason is required').not().isEmpty(),
], flagReview);

router.put('/:id/unflag', unflagReview);

router.put('/:id/moderate', [
  check('action', 'Moderation action is required').isIn(['approve', 'reject', 'flag']),
  check('reason', 'Reason is required for rejection or flagging').optional().not().isEmpty(),
], moderateReview);

router.delete('/:id', deleteReview);

module.exports = router;
