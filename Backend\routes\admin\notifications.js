const express = require('express');
const { check } = require('express-validator');
const {
  getAllNotifications,
  getNotificationById,
  createNotification,
  updateNotification,
  deleteNotification,
  bulkCreateNotifications,
  bulkDeleteNotifications,
  getNotificationStats,
  exportNotifications,
  getNotificationAnalytics,
  createBroadcastNotification,
  getNotificationTemplates,
  createNotificationTemplate,
  updateNotificationTemplate,
  deleteNotificationTemplate
} = require('../../controllers/admin/notifications');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin notification routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get all notifications with filtering, sorting, and pagination
router.get('/', getAllNotifications);

// Get notification statistics
router.get('/stats', getNotificationStats);

// Get notification analytics
router.get('/analytics', [
  check('period', 'Period must be valid').optional().isIn(['7d', '30d', '90d', '1y', 'all']),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month']),
], getNotificationAnalytics);

// Get notification templates
router.get('/templates', getNotificationTemplates);

// Export notifications data
router.get('/export', exportNotifications);

// Create single notification
router.post('/', [
  check('user', 'User ID is required').not().isEmpty(),
  check('title', 'Title is required').not().isEmpty(),
  check('message', 'Message is required').not().isEmpty(),
  check('type', 'Type is required').isIn([
    'order',
    'bid',
    'custom_request',
    'payment',
    'account',
    'system',
    'promotion',
    'announcement'
  ])
], createNotification);

// Create broadcast notification
router.post('/broadcast', [
  check('title', 'Title is required').not().isEmpty(),
  check('message', 'Message is required').not().isEmpty(),
  check('type', 'Type is required').isIn([
    'system',
    'promotion',
    'announcement'
  ]),
  check('targetAudience', 'Target audience is required').isIn([
    'all',
    'buyers',
    'sellers',
    'verified',
    'unverified'
  ])
], createBroadcastNotification);

// Bulk operations
router.post('/bulk-create', [
  check('notifications', 'Notifications array is required').isArray(),
], bulkCreateNotifications);

router.post('/bulk-delete', [
  check('notificationIds', 'Notification IDs array is required').isArray(),
], bulkDeleteNotifications);

// Notification templates
router.post('/templates', [
  check('name', 'Template name is required').not().isEmpty(),
  check('title', 'Template title is required').not().isEmpty(),
  check('message', 'Template message is required').not().isEmpty(),
  check('type', 'Template type is required').not().isEmpty(),
], createNotificationTemplate);

router.put('/templates/:id', [
  check('name', 'Template name is required').optional().not().isEmpty(),
  check('title', 'Template title is required').optional().not().isEmpty(),
  check('message', 'Template message is required').optional().not().isEmpty(),
], updateNotificationTemplate);

router.delete('/templates/:id', deleteNotificationTemplate);

// Individual notification operations
router.get('/:id', getNotificationById);

router.put('/:id', [
  check('title', 'Title is required').optional().not().isEmpty(),
  check('message', 'Message is required').optional().not().isEmpty(),
], updateNotification);

router.delete('/:id', deleteNotification);

module.exports = router;
