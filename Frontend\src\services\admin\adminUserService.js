import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/users`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Get all users with filtering and pagination
export const getAllUsers = async (params = {}) => {
  try {
    const response = await api.get('/', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get user by ID
export const getUserById = async (id) => {
  try {
    const response = await api.get(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Create new user
export const createUser = async (userData) => {
  try {
    const response = await api.post('/', userData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update user
export const updateUser = async (id, userData) => {
  try {
    const response = await api.put(`/${id}`, userData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Delete user
export const deleteUser = async (id) => {
  try {
    const response = await api.delete(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk update users
export const bulkUpdateUsers = async (userIds, action, data = {}) => {
  try {
    const response = await api.post('/bulk-update', {
      userIds,
      action,
      data
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk delete users
export const bulkDeleteUsers = async (userIds) => {
  try {
    const response = await api.post('/bulk-delete', { userIds });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Toggle user status
export const toggleUserStatus = async (id) => {
  try {
    const response = await api.put(`/${id}/toggle-status`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Verify user
export const verifyUser = async (id) => {
  try {
    const response = await api.put(`/${id}/verify`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Suspend user
export const suspendUser = async (id, reason) => {
  try {
    const response = await api.put(`/${id}/suspend`, { reason });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Unsuspend user
export const unsuspendUser = async (id) => {
  try {
    const response = await api.put(`/${id}/unsuspend`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get user activity history
export const getUserActivityHistory = async (id, params = {}) => {
  try {
    const response = await api.get(`/${id}/activity`, { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get user statistics
export const getUserStats = async () => {
  try {
    const response = await api.get('/stats');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Export users
export const exportUsers = async (params = {}) => {
  try {
    const response = await api.get('/export', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// User management actions
export const userActions = {
  activate: (userIds) => bulkUpdateUsers(userIds, 'activate'),
  deactivate: (userIds) => bulkUpdateUsers(userIds, 'deactivate'),
  verify: (userIds) => bulkUpdateUsers(userIds, 'verify'),
  unverify: (userIds) => bulkUpdateUsers(userIds, 'unverify'),
  updateRole: (userIds, role) => bulkUpdateUsers(userIds, 'update_role', { role }),
};

export default {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  bulkUpdateUsers,
  bulkDeleteUsers,
  toggleUserStatus,
  verifyUser,
  suspendUser,
  unsuspendUser,
  getUserActivityHistory,
  getUserStats,
  exportUsers,
  userActions
};
