const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const Notification = require('../../models/Notification');
const User = require('../../models/User');

// In-memory storage for notification templates (in production, use database)
let notificationTemplates = [
  {
    id: 1,
    name: 'Welcome New User',
    title: 'Welcome to XOSportsHub!',
    message: 'Thank you for joining our platform. Start exploring amazing sports content!',
    type: 'account',
    variables: ['firstName', 'lastName']
  },
  {
    id: 2,
    name: 'Order Confirmation',
    title: 'Order Confirmed',
    message: 'Your order for {{contentTitle}} has been confirmed. Order ID: {{orderId}}',
    type: 'order',
    variables: ['contentTitle', 'orderId']
  },
  {
    id: 3,
    name: 'Payment Success',
    title: 'Payment Successful',
    message: 'Your payment of ${{amount}} has been processed successfully.',
    type: 'payment',
    variables: ['amount']
  }
];

// @desc    Get all notifications with filtering, sorting, and pagination
// @route   GET /api/admin/notifications
// @access  Private/Admin
exports.getAllNotifications = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      type = '',
      isRead = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom = '',
      dateTo = '',
      userId = ''
    } = req.query;

    // Build query
    let query = {};

    // Search functionality
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      const users = await User.find({
        $or: [
          { firstName: searchRegex },
          { lastName: searchRegex },
          { email: searchRegex }
        ]
      }).select('_id');

      const userIds = users.map(user => user._id);

      query.$or = [
        { user: { $in: userIds } },
        { title: searchRegex },
        { message: searchRegex }
      ];
    }

    // Filter by type
    if (type) {
      query.type = type;
    }

    // Filter by read status
    if (isRead !== '') {
      query.isRead = isRead === 'true';
    }

    // Filter by user
    if (userId) {
      query.user = userId;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) {
        query.createdAt.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        query.createdAt.$lte = new Date(dateTo);
      }
    }

    // Sorting
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const notifications = await Notification.find(query)
      .populate('user', 'firstName lastName email profileImage')
      .sort(sortOptions)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    // Get total count for pagination
    const total = await Notification.countDocuments(query);

    res.status(200).json({
      success: true,
      data: notifications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get notification by ID
// @route   GET /api/admin/notifications/:id
// @access  Private/Admin
exports.getNotificationById = async (req, res, next) => {
  try {
    const notification = await Notification.findById(req.params.id)
      .populate('user', 'firstName lastName email profileImage mobile phone');

    if (!notification) {
      return next(new ErrorResponse('Notification not found', 404));
    }

    res.status(200).json({
      success: true,
      data: notification
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create notification
// @route   POST /api/admin/notifications
// @access  Private/Admin
exports.createNotification = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { user, title, message, type, data } = req.body;

    const notification = await Notification.create({
      user,
      title,
      message,
      type,
      data,
      createdBy: req.user.id
    });

    await notification.populate('user', 'firstName lastName email');

    res.status(201).json({
      success: true,
      data: notification,
      message: 'Notification created successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update notification
// @route   PUT /api/admin/notifications/:id
// @access  Private/Admin
exports.updateNotification = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const notification = await Notification.findById(req.params.id);
    if (!notification) {
      return next(new ErrorResponse('Notification not found', 404));
    }

    const { title, message, type, data } = req.body;

    if (title) notification.title = title;
    if (message) notification.message = message;
    if (type) notification.type = type;
    if (data) notification.data = data;

    await notification.save();

    res.status(200).json({
      success: true,
      data: notification,
      message: 'Notification updated successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete notification
// @route   DELETE /api/admin/notifications/:id
// @access  Private/Admin
exports.deleteNotification = async (req, res, next) => {
  try {
    const notification = await Notification.findById(req.params.id);
    if (!notification) {
      return next(new ErrorResponse('Notification not found', 404));
    }

    await notification.deleteOne();

    res.status(200).json({
      success: true,
      message: 'Notification deleted successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create broadcast notification
// @route   POST /api/admin/notifications/broadcast
// @access  Private/Admin
exports.createBroadcastNotification = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { title, message, type, targetAudience, data } = req.body;

    // Build user query based on target audience
    let userQuery = {};
    switch (targetAudience) {
      case 'buyers':
        userQuery = { $or: [{ role: 'buyer' }, { activeRole: 'buyer' }] };
        break;
      case 'sellers':
        userQuery = { $or: [{ role: 'seller' }, { activeRole: 'seller' }] };
        break;
      case 'verified':
        userQuery = { isVerified: true };
        break;
      case 'unverified':
        userQuery = { isVerified: false };
        break;
      default:
        userQuery = {}; // All users
    }

    // Get target users
    const users = await User.find(userQuery).select('_id');

    // Create notifications for all target users
    const notifications = users.map(user => ({
      user: user._id,
      title,
      message,
      type,
      data,
      createdBy: req.user.id
    }));

    const createdNotifications = await Notification.insertMany(notifications);

    res.status(201).json({
      success: true,
      data: {
        count: createdNotifications.length,
        targetAudience,
        notifications: createdNotifications.slice(0, 5) // Return first 5 as sample
      },
      message: `Broadcast notification sent to ${createdNotifications.length} users`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get notification statistics
// @route   GET /api/admin/notifications/stats
// @access  Private/Admin
exports.getNotificationStats = async (req, res, next) => {
  try {
    const totalNotifications = await Notification.countDocuments();
    const unreadNotifications = await Notification.countDocuments({ isRead: false });
    const readNotifications = await Notification.countDocuments({ isRead: true });

    // Type distribution
    const typeDistribution = await Notification.aggregate([
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Monthly stats for current year
    const currentYear = new Date().getFullYear();
    const monthlyStats = await Notification.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(`${currentYear}-01-01`),
            $lte: new Date(`${currentYear}-12-31`)
          }
        }
      },
      {
        $group: {
          _id: { $month: '$createdAt' },
          count: { $sum: 1 },
          read: {
            $sum: { $cond: [{ $eq: ['$isRead', true] }, 1, 0] }
          }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalNotifications,
        unreadNotifications,
        readNotifications,
        typeDistribution,
        monthlyStats
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get notification analytics
// @route   GET /api/admin/notifications/analytics
// @access  Private/Admin
exports.getNotificationAnalytics = async (req, res, next) => {
  try {
    const { period = '30d', groupBy = 'day' } = req.query;

    // Calculate date range based on period
    const endDate = new Date();
    let startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Build group stage based on groupBy parameter
    let groupStage = {};
    switch (groupBy) {
      case 'day':
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'week':
        groupStage = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        };
        break;
      case 'month':
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      default:
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    const analytics = await Notification.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: groupStage,
          totalNotifications: { $sum: 1 },
          readNotifications: {
            $sum: { $cond: [{ $eq: ['$isRead', true] }, 1, 0] }
          },
          unreadNotifications: {
            $sum: { $cond: [{ $eq: ['$isRead', false] }, 1, 0] }
          },
          typeDistribution: {
            $push: '$type'
          }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export notifications data
// @route   GET /api/admin/notifications/export
// @access  Private/Admin
exports.exportNotifications = async (req, res, next) => {
  try {
    const notifications = await Notification.find()
      .populate('user', 'firstName lastName email')
      .populate('createdBy', 'firstName lastName email');

    // Transform data for export
    const exportData = notifications.map(notification => ({
      ID: notification._id,
      'Title': notification.title,
      'Message': notification.message,
      'Type': notification.type,
      'Is Read': notification.isRead,
      'User Name': notification.user ? `${notification.user.firstName} ${notification.user.lastName}` : 'N/A',
      'User Email': notification.user ? notification.user.email : 'N/A',
      'Created By': notification.createdBy ? `${notification.createdBy.firstName} ${notification.createdBy.lastName}` : 'System',
      'Created At': notification.createdAt,
      'Updated At': notification.updatedAt
    }));

    res.status(200).json({
      success: true,
      data: exportData
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk create notifications
// @route   POST /api/admin/notifications/bulk-create
// @access  Private/Admin
exports.bulkCreateNotifications = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { notifications } = req.body;

    // Add createdBy to each notification
    const notificationsWithCreator = notifications.map(notification => ({
      ...notification,
      createdBy: req.user.id
    }));

    const result = await Notification.insertMany(notificationsWithCreator);

    res.status(201).json({
      success: true,
      message: `${result.length} notifications created successfully`,
      data: result
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk delete notifications
// @route   POST /api/admin/notifications/bulk-delete
// @access  Private/Admin
exports.bulkDeleteNotifications = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { notificationIds } = req.body;

    const result = await Notification.deleteMany({ _id: { $in: notificationIds } });

    res.status(200).json({
      success: true,
      message: `${result.deletedCount} notifications deleted successfully`,
      data: result
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get notification templates
// @route   GET /api/admin/notifications/templates
// @access  Private/Admin
exports.getNotificationTemplates = async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      data: notificationTemplates
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create notification template
// @route   POST /api/admin/notifications/templates
// @access  Private/Admin
exports.createNotificationTemplate = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { name, title, message, type, variables } = req.body;

    // Generate new template ID
    const newId = notificationTemplates.length > 0
      ? Math.max(...notificationTemplates.map(t => t.id)) + 1
      : 1;

    const newTemplate = {
      id: newId,
      name,
      title,
      message,
      type,
      variables: variables || [],
      createdAt: new Date(),
      createdBy: req.user.id
    };

    notificationTemplates.push(newTemplate);

    res.status(201).json({
      success: true,
      message: 'Template created successfully',
      data: newTemplate
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update notification template
// @route   PUT /api/admin/notifications/templates/:id
// @access  Private/Admin
exports.updateNotificationTemplate = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const templateId = parseInt(req.params.id);
    const templateIndex = notificationTemplates.findIndex(t => t.id === templateId);

    if (templateIndex === -1) {
      return next(new ErrorResponse('Template not found', 404));
    }

    const { name, title, message, type, variables } = req.body;
    const updatedTemplate = {
      ...notificationTemplates[templateIndex],
      name: name || notificationTemplates[templateIndex].name,
      title: title || notificationTemplates[templateIndex].title,
      message: message || notificationTemplates[templateIndex].message,
      type: type || notificationTemplates[templateIndex].type,
      variables: variables || notificationTemplates[templateIndex].variables,
      updatedAt: new Date(),
      updatedBy: req.user.id
    };

    notificationTemplates[templateIndex] = updatedTemplate;

    res.status(200).json({
      success: true,
      message: 'Template updated successfully',
      data: updatedTemplate
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete notification template
// @route   DELETE /api/admin/notifications/templates/:id
// @access  Private/Admin
exports.deleteNotificationTemplate = async (req, res, next) => {
  try {
    const templateId = parseInt(req.params.id);
    const templateIndex = notificationTemplates.findIndex(t => t.id === templateId);

    if (templateIndex === -1) {
      return next(new ErrorResponse('Template not found', 404));
    }

    notificationTemplates.splice(templateIndex, 1);

    res.status(200).json({
      success: true,
      message: 'Template deleted successfully'
    });
  } catch (err) {
    next(err);
  }
};
