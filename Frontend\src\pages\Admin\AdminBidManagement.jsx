import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectBids,
  selectSelectedBids,
  selectUI,
  selectLoading,
  selectErrors,
  selectBidFilters,
  selectBidsPagination,
  setSelectedBids,
  showBidDetailModal,
  setBidFilters,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchBids,
  approveBid as approveBidThunk,
  rejectBid as rejectBidThunk,
  deleteBid as deleteBidThunk,
  updateBid as updateBidThunk,
  bulkApproveBids,
  bulkRejectBids,
  bulkDeleteBids,
} from "../../redux/slices/adminDashboardThunks";
import AdminLayout from "../../components/admin/AdminLayout";
import BidDetailModal from "../../components/admin/BidDetailModal";
import "../../styles/AdminBidManagement.css";

// Icons
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "react-icons/fa";
import { MdAdd } from "react-icons/md";

const AdminBidManagement = () => {
  const dispatch = useDispatch();
  const bids = useSelector(selectBids);
  const selectedBids = useSelector(selectSelectedBids);
  const ui = useSelector(selectUI);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);
  const filters = useSelector(selectBidFilters);
  const pagination = useSelector(selectBidsPagination);

  // Use API data if available, otherwise fall back to static data
  const displayBids = bids.data.length > 0 ? bids.data : [];

  const [searchTerm, setSearchTerm] = useState(filters.search || "");
  const [statusFilter, setStatusFilter] = useState(filters.status || "all");

  // Initial fetch on component mount only
  useEffect(() => {
    dispatch(fetchBids({
      page: 1,
      limit: 10,
      search: '',
      status: '',
      sortBy: 'createdAt',
      sortOrder: 'desc'
    }));
  }, [dispatch]);

  // Update filters when search or status changes (debounced)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm !== filters.search || statusFilter !== (filters.status || 'all')) {
        const newFilters = {
          search: searchTerm,
          status: statusFilter === 'all' ? '' : statusFilter
        };

        // Update filters and fetch new data
        dispatch(setBidFilters(newFilters));
        dispatch(fetchBids({
          page: 1, // Reset to first page when filtering
          limit: pagination.limit,
          search: newFilters.search,
          status: newFilters.status,
          sortBy: filters.sortBy,
          sortOrder: filters.sortOrder
        }));
      }
    }, 10); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchTerm, statusFilter, dispatch, filters.search, filters.status, filters.sortBy, filters.sortOrder, pagination.limit]);

  // Handle pagination changes
  useEffect(() => {
    if (pagination.current > 1) { // Only fetch if not on first page (first page is handled by initial fetch or filter change)
      dispatch(fetchBids({
        page: pagination.current,
        limit: pagination.limit,
        search: filters.search,
        status: filters.status,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder
      }));
    }
  }, [pagination.current]);

  // Filter bids based on search and filters (client-side filtering for static data)
  const filteredBids = displayBids.filter(bid => {
    const matchesSearch = !searchTerm ||
      (bid.bidId && bid.bidId.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (bid.content?.title && bid.content.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (bid.bidder?.firstName && `${bid.bidder.firstName} ${bid.bidder.lastName}`.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (bid.bidderName && bid.bidderName.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === "all" || bid.status === statusFilter;

    return matchesSearch && matchesStatus;
  });


  // Handle bid actions
  const handleBidAction = async (bidItem, action) => {
    const bidId = bidItem._id || bidItem.id;
    const displayBidId = bidItem.bidId || `#${bidId?.slice(-6)}`;
    const contentTitle = bidItem.content?.title || bidItem.contentTitle;

    switch (action) {
      case 'view':
      case 'edit':
        dispatch(showBidDetailModal(bidItem));
        break;
      case 'approve':
        if (window.confirm(`Approve bid "${displayBidId}"?`)) {
          try {
            await dispatch(approveBidThunk({ id: bidId, approvalNotes: '' })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bid_approval',
              description: `Bid approved: ${displayBidId} for ${contentTitle}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`Bid "${displayBidId}" has been approved!`);
          } catch (error) {
            alert(`Failed to approve bid: ${error}`);
          }
        }
        break;
      case 'reject':
        const reason = prompt(`Reason for rejecting bid "${displayBidId}":`);
        if (reason) {
          try {
            await dispatch(rejectBidThunk({ id: bidId, reason, rejectionNotes: '' })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bid_rejection',
              description: `Bid rejected: ${displayBidId} - Reason: ${reason}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`Bid "${displayBidId}" has been rejected.`);
          } catch (error) {
            alert(`Failed to reject bid: ${error}`);
          }
        }
        break;
      case 'delete':
        if (window.confirm(`Delete bid "${displayBidId}"? This action cannot be undone.`)) {
          try {
            await dispatch(deleteBidThunk(bidId)).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bid_deletion',
              description: `Bid deleted: ${displayBidId}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`Bid "${displayBidId}" has been deleted!`);
          } catch (error) {
            alert(`Failed to delete bid: ${error}`);
          }
        }
        break;
      default:
        break;
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action) => {
    if (selectedBids.length === 0) {
      alert('Please select bids first');
      return;
    }

    switch (action) {
      case 'approve':
        if (window.confirm(`Approve ${selectedBids.length} selected bids?`)) {
          try {
            await dispatch(bulkApproveBids(selectedBids)).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_bid_approval',
              description: `Bulk approved ${selectedBids.length} bids`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`${selectedBids.length} bids approved`);
            dispatch(setSelectedBids([]));
          } catch (error) {
            alert(`Failed to approve bids: ${error}`);
          }
        }
        break;
      case 'reject':
        const reason = prompt(`Reason for rejecting ${selectedBids.length} bids:`);
        if (reason) {
          try {
            await dispatch(bulkRejectBids({ bidIds: selectedBids, reason })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_bid_rejection',
              description: `Bulk rejected ${selectedBids.length} bids - Reason: ${reason}`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`${selectedBids.length} bids rejected`);
            dispatch(setSelectedBids([]));
          } catch (error) {
            alert(`Failed to reject bids: ${error}`);
          }
        }
        break;
      case 'delete':
        if (window.confirm(`Delete ${selectedBids.length} selected bids? This action cannot be undone.`)) {
          try {
            await dispatch(bulkDeleteBids(selectedBids)).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_bid_deletion',
              description: `Bulk deleted ${selectedBids.length} bids`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            alert(`${selectedBids.length} bids deleted`);
            dispatch(setSelectedBids([]));
          } catch (error) {
            alert(`Failed to delete bids: ${error}`);
          }
        }
        break;
      default:
        break;
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge class
  const getStatusBadge = (status) => {
    switch (status) {
      case 'Active':
        return 'status-badge active';
      case 'Won':
        return 'status-badge won';
      case 'Lost':
        return 'status-badge lost';
      case 'Outbid':
        return 'status-badge outbid';
      case 'Cancelled':
        return 'status-badge cancelled';
      default:
        return 'status-badge';
    }
  };

  return (
    <AdminLayout>
      <div className="AdminBidManagement">
        {/* Header Actions */}
        <div className="AdminBidManagement__header">
          <div className="header-left">
            <div className="search-container">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search bids by ID, content, or bidder..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="AdminBidManagement__filters">
          <div className="filter-group">
            <FaFilter className="filter-icon" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Status</option>
              <option value="Active">Active</option>
              <option value="Won">Won</option>
              <option value="Lost">Lost</option>
              <option value="Outbid">Outbid</option>
              <option value="Cancelled">Cancelled</option>
            </select>
          </div>

          {selectedBids.length > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">
                {selectedBids.length} selected
              </span>
              <button
                className="btn btn-success"
                onClick={() => handleBulkAction('approve')}
              >
                <FaCheck />
                Approve
              </button>
              <button
                className="btn btn-warning"
                onClick={() => handleBulkAction('reject')}
              >
                <FaTimes />
                Reject
              </button>
              <button
                className="btn btn-danger"
                onClick={() => handleBulkAction('delete')}
              >
                Delete
              </button>
            </div>
          )}
        </div>

        {/* Bids Table */}
        <div className="AdminBidManagement__table">
          <div className="table-container">
            <table className="bids-table">
              <thead>
                <tr>
                  <th>Bid ID</th>
                  <th>Content Title</th>
                  <th>Bidder Name</th>
                  <th>Bid Amount</th>
                  <th>Status</th>
                  <th>Bid Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading.bids ? (
                  <tr>
                    <td colSpan="7" className="loading-row">
                      <div className="loading-spinner"></div>
                      <span>Loading bids...</span>
                    </td>
                  </tr>
                ) : errors.bids ? (
                  <tr>
                    <td colSpan="7" className="error-row">
                      <span>Error loading bids: {errors.bids}</span>
                      <button
                        onClick={() => dispatch(fetchBids())}
                        className="btn btn-outline"
                      >
                        Retry
                      </button>
                    </td>
                  </tr>
                ) : filteredBids.map((bid) => {
                  const bidId = bid._id || bid.id;
                  const displayBidId = bid.bidId || `#${bidId?.slice(-6)}`;
                  const contentTitle = bid.content?.title || bid.contentTitle;
                  const bidderName = bid.bidder ? `${bid.bidder.firstName} ${bid.bidder.lastName}` : bid.bidderName;
                  const bidderEmail = bid.bidder?.email || bid.bidderEmail;
                  const bidAmount = bid.amount || bid.bidAmount;
                  const bidDate = bid.createdAt || bid.bidDate;

                  return (
                    <tr key={bidId}>
                      <td>
                        <div className="bid-id">
                          <span className="bid-id-text">{displayBidId}</span>
                          {bid.isAutoBid && <span className="auto-bid-badge">AUTO</span>}
                          {bid.isHighestBid && <span className="highest-bid-badge">HIGHEST</span>}
                        </div>
                      </td>
                      <td>
                        <div className="content-info">
                          <div className="content-thumbnail">
                            <FaGavel />
                          </div>
                          <div className="content-details">
                            <span className="content-title">{contentTitle}</span>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div className="bidder-info">
                          <span className="bidder-name">{bidderName}</span>
                          <span className="bidder-email">{bidderEmail}</span>
                        </div>
                      </td>
                      <td>
                        <div className="bid-amount">
                          <span className="amount">{formatCurrency(bidAmount)}</span>
                          {bid.maxAutoBidAmount && (
                            <span className="max-auto-bid">
                              Max: {formatCurrency(bid.maxAutoBidAmount)}
                            </span>
                          )}
                        </div>
                      </td>
                      <td>
                        <span className={getStatusBadge(bid.status)}>
                          {bid.status}
                        </span>
                      </td>
                      <td>{formatDate(bidDate)}</td>
                      <td>
                        <div className="table-actions">
                          <button
                            className="btn-action view"
                            title="View Bid Details"
                            onClick={() => handleBidAction(bid, 'view')}
                          >
                            <FaEye />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {filteredBids.length === 0 && (
            <div className="no-results">
              <FaGavel className="no-results-icon" />
              <h3>No bids found</h3>
              <p>Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="AdminBidManagement__pagination">
          <div className="pagination-info">
            Showing {filteredBids.length} of {pagination.total || displayBids.length} bids
          </div>
          <div className="pagination-controls">
            <button
              className="btn btn-outline"
              disabled={pagination.current <= 1}
              onClick={() => dispatch(setBidFilters({ page: pagination.current - 1 }))}
            >
              Previous
            </button>
            <span className="page-number active">{pagination.current}</span>
            <button
              className="btn btn-outline"
              disabled={pagination.current >= pagination.pages}
              onClick={() => dispatch(setBidFilters({ page: pagination.current + 1 }))}
            >
              Next
            </button>
          </div>
        </div>
      </div>
      <BidDetailModal />
    </AdminLayout>
  );
};

export default AdminBidManagement;
