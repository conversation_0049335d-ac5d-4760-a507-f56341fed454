import { configureStore } from "@reduxjs/toolkit";
// Remove the thunk import as it's already included in Redux Toolkit's default middleware

// Import reducers
import authReducer from "./slices/authSlice";
import userReducer from "./slices/userSlice";
import contentReducer from "./slices/contentSlice";
import orderReducer from "./slices/orderSlice";
import bidReducer from "./slices/bidSlice";
import offerReducer from "./slices/offerSlice";
import paymentReducer from "./slices/paymentSlice";
import cardReducer from "./slices/cardSlice";
import notificationReducer from "./slices/notificationSlice";
import wishlistReducer from "./slices/wishlistSlice";
import buyerDashboardReducer from "./slices/buyerDashboardSlice";
import sellerDashboardReducer from "./slices/sellerDashboardSlice";
import adminDashboardReducer from "./slices/adminDashboardSlice";
import contactReducer from "./slices/contactSlice";
import reviewReducer from "./slices/reviewSlice";

// Middleware to clear user-specific data on logout
const clearDataOnLogout = (store) => (next) => (action) => {
  // Check if this is a logout action
  if (action.type === 'auth/logout/fulfilled' || action.type === 'auth/verifyOTP/fulfilled') {
    // If it's verifyOTP, we want to clear data before setting the new user
    if (action.type === 'auth/verifyOTP/fulfilled') {
      // Clear all user-specific data before new login
      store.dispatch({ type: 'offer/clearOffers' });
      store.dispatch({ type: 'bid/clearBids' });
      store.dispatch({ type: 'order/clearOrders' });
      store.dispatch({ type: 'wishlist/clearWishlist' });
      store.dispatch({ type: 'buyerDashboard/reset' });
      store.dispatch({ type: 'sellerDashboard/reset' });
      store.dispatch({ type: 'reviews/clearReviews' });
    }

    // If it's logout, clear everything
    if (action.type === 'auth/logout/fulfilled') {
      store.dispatch({ type: 'offer/clearOffers' });
      store.dispatch({ type: 'bid/clearBids' });
      store.dispatch({ type: 'order/clearOrders' });
      store.dispatch({ type: 'wishlist/clearWishlist' });
      store.dispatch({ type: 'buyerDashboard/reset' });
      store.dispatch({ type: 'sellerDashboard/reset' });
      store.dispatch({ type: 'reviews/clearReviews' });
    }
  }

  return next(action);
};

export const store = configureStore({
  reducer: {
    auth: authReducer,
    user: userReducer,
    content: contentReducer,
    order: orderReducer,
    bid: bidReducer,
    offer: offerReducer,
    payment: paymentReducer,
    cards: cardReducer,
    notification: notificationReducer,
    wishlist: wishlistReducer,
    buyerDashboard: buyerDashboardReducer,
    sellerDashboard: sellerDashboardReducer,
    adminDashboard: adminDashboardReducer,
    contact: contactReducer,
    reviews: reviewReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(clearDataOnLogout),
  devTools: import.meta.env.MODE !== "production",
});

export default store;
