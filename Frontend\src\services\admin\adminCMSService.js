import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/cms`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Get all CMS pages with filtering and pagination
export const getAllCMSPages = async (params = {}) => {
  try {
    const response = await api.get('/', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get CMS page by ID
export const getCMSPageById = async (id) => {
  try {
    const response = await api.get(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get CMS page by slug
export const getCMSPageBySlug = async (slug) => {
  try {
    const response = await api.get(`/slug/${slug}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Create CMS page
export const createCMSPage = async (pageData) => {
  try {
    const response = await api.post('/', pageData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update CMS page
export const updateCMSPage = async (id, pageData) => {
  try {
    const response = await api.put(`/${id}`, pageData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Delete CMS page
export const deleteCMSPage = async (id) => {
  try {
    const response = await api.delete(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk delete CMS pages
export const bulkDeleteCMSPages = async (pageIds) => {
  try {
    const response = await api.post('/bulk-delete', { pageIds });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Publish CMS page
export const publishCMSPage = async (id) => {
  try {
    const response = await api.put(`/${id}/publish`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Unpublish CMS page
export const unpublishCMSPage = async (id) => {
  try {
    const response = await api.put(`/${id}/unpublish`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update CMS page status
export const updateCMSPageStatus = async (id, status) => {
  try {
    const response = await api.put(`/${id}/status`, { status });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Duplicate CMS page
export const duplicateCMSPage = async (id, title, slug) => {
  try {
    const response = await api.post(`/${id}/duplicate`, { title, slug });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get CMS statistics
export const getCMSStats = async () => {
  try {
    const response = await api.get('/stats');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Export CMS pages
export const exportCMSPages = async (params = {}) => {
  try {
    const response = await api.get('/export', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// CMS page actions
export const cmsPageActions = {
  publish: (id) => publishCMSPage(id),
  unpublish: (id) => unpublishCMSPage(id),
  duplicate: (id, title, slug) => duplicateCMSPage(id, title, slug),
};

// CMS page status actions
export const cmsPageStatusActions = {
  publish: (id) => updateCMSPageStatus(id, 'published'),
  draft: (id) => updateCMSPageStatus(id, 'draft'),
  archive: (id) => updateCMSPageStatus(id, 'archived'),
};

// CMS page templates (for creating new pages)
export const cmsPageTemplates = {
  basic: {
    title: '',
    slug: '',
    content: '<h1>Page Title</h1>\n<p>Page content goes here...</p>',
    status: 'draft',
    metaTitle: '',
    metaDescription: '',
    metaKeywords: ''
  },
  aboutUs: {
    title: 'About Us',
    slug: 'about-us',
    content: `
      <h1>About XOSportsHub</h1>
      <p>Welcome to XOSportsHub, the premier marketplace for sports content...</p>
      <h2>Our Mission</h2>
      <p>Our mission is to connect sports enthusiasts with high-quality content...</p>
      <h2>Our Team</h2>
      <p>We are a passionate team of sports lovers and technology experts...</p>
    `,
    status: 'draft',
    metaTitle: 'About Us - XOSportsHub',
    metaDescription: 'Learn about XOSportsHub, our mission, and our team.',
    metaKeywords: 'about, xosportshub, sports, marketplace, team'
  },
  privacyPolicy: {
    title: 'Privacy Policy',
    slug: 'privacy-policy',
    content: `
      <h1>Privacy Policy</h1>
      <p>Last updated: [Date]</p>
      <h2>Information We Collect</h2>
      <p>We collect information you provide directly to us...</p>
      <h2>How We Use Your Information</h2>
      <p>We use the information we collect to...</p>
    `,
    status: 'draft',
    metaTitle: 'Privacy Policy - XOSportsHub',
    metaDescription: 'Read our privacy policy to understand how we handle your data.',
    metaKeywords: 'privacy, policy, data, protection'
  },
  termsOfService: {
    title: 'Terms of Service',
    slug: 'terms-of-service',
    content: `
      <h1>Terms of Service</h1>
      <p>Last updated: [Date]</p>
      <h2>Acceptance of Terms</h2>
      <p>By accessing and using this website, you accept and agree to be bound by...</p>
      <h2>Use License</h2>
      <p>Permission is granted to temporarily download one copy of...</p>
    `,
    status: 'draft',
    metaTitle: 'Terms of Service - XOSportsHub',
    metaDescription: 'Read our terms of service for using XOSportsHub.',
    metaKeywords: 'terms, service, legal, agreement'
  },
  faq: {
    title: 'Frequently Asked Questions',
    slug: 'faq',
    content: `
      <h1>Frequently Asked Questions</h1>
      <h2>General Questions</h2>
      <h3>What is XOSportsHub?</h3>
      <p>XOSportsHub is a marketplace for sports content...</p>
      <h3>How do I create an account?</h3>
      <p>You can create an account by clicking the "Sign Up" button...</p>
      <h2>Buying Content</h2>
      <h3>How do I purchase content?</h3>
      <p>To purchase content, browse our marketplace and...</p>
    `,
    status: 'draft',
    metaTitle: 'FAQ - XOSportsHub',
    metaDescription: 'Find answers to frequently asked questions about XOSportsHub.',
    metaKeywords: 'faq, questions, help, support'
  }
};

export default {
  getAllCMSPages,
  getCMSPageById,
  getCMSPageBySlug,
  createCMSPage,
  updateCMSPage,
  deleteCMSPage,
  bulkDeleteCMSPages,
  publishCMSPage,
  unpublishCMSPage,
  updateCMSPageStatus,
  duplicateCMSPage,
  getCMSStats,
  exportCMSPages,
  cmsPageActions,
  cmsPageStatusActions,
  cmsPageTemplates
};
