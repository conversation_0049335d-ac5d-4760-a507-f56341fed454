const express = require('express');
const { check } = require('express-validator');
const {
  getAllPayments,
  getPaymentById,
  updatePaymentStatus,
  processRefund,
  bulkUpdatePayments,
  getPaymentStats,
  exportPayments,
  getPaymentAnalytics,
  getFailedPayments,
  retryPayment,
  flagPayment,
  unflagPayment,
  getPaymentDisputes,
  resolveDispute
} = require('../../controllers/admin/payments');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin payment routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get all payments with filtering, sorting, and pagination
router.get('/', getAllPayments);

// Get payment statistics
router.get('/stats', getPaymentStats);

// Get payment analytics
router.get('/analytics', [
  check('period', 'Period must be valid').optional().isIn(['7d', '30d', '90d', '1y', 'all']),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month']),
], getPaymentAnalytics);

// Get failed payments
router.get('/failed', getFailedPayments);

// Get payment disputes
router.get('/disputes', getPaymentDisputes);

// Export payments data
router.get('/export', exportPayments);

// Bulk operations
router.post('/bulk-update', [
  check('paymentIds', 'Payment IDs array is required').isArray(),
  check('status', 'Status is required').isIn(['Pending', 'Completed', 'Failed', 'Refunded']),
], bulkUpdatePayments);

// Individual payment operations
router.get('/:id', getPaymentById);

router.put('/:id/status', [
  check('status', 'Status is required').isIn(['Pending', 'Completed', 'Failed', 'Refunded']),
  check('notes', 'Notes must be a string').optional().isString(),
], updatePaymentStatus);

router.post('/:id/refund', [
  check('amount', 'Refund amount is required').isFloat({ min: 0.01 }),
  check('reason', 'Refund reason is required').not().isEmpty(),
], processRefund);

router.post('/:id/retry', retryPayment);

router.put('/:id/flag', [
  check('reason', 'Flag reason is required').not().isEmpty(),
], flagPayment);

router.put('/:id/unflag', unflagPayment);

router.post('/:id/resolve-dispute', [
  check('resolution', 'Resolution is required').not().isEmpty(),
  check('refundAmount', 'Refund amount must be a number').optional().isFloat({ min: 0 }),
], resolveDispute);

module.exports = router;
