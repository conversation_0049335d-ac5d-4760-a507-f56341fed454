import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/reports`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Get revenue report
export const getRevenueReport = async (params = {}) => {
  try {
    const response = await api.get('/revenue', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get user report
export const getUserReport = async (params = {}) => {
  try {
    const response = await api.get('/users', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get content report
export const getContentReport = async (params = {}) => {
  try {
    const response = await api.get('/content', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get sales report
export const getSalesReport = async (params = {}) => {
  try {
    const response = await api.get('/sales', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get performance report
export const getPerformanceReport = async (params = {}) => {
  try {
    const response = await api.get('/performance', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get custom report
export const getCustomReport = async (reportType, parameters) => {
  try {
    const response = await api.post('/custom', { reportType, parameters });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Export report
export const exportReport = async (reportType, format, parameters) => {
  try {
    const response = await api.post('/export', { reportType, format, parameters });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Predefined report configurations
export const reportConfigs = {
  revenue: {
    daily: {
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      groupBy: 'day'
    },
    weekly: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      groupBy: 'week'
    },
    monthly: {
      startDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      groupBy: 'month'
    }
  },
  users: {
    registrations: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      userType: 'all'
    },
    buyers: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      userType: 'buyer'
    },
    sellers: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      userType: 'seller'
    }
  },
  content: {
    uploads: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      contentType: '',
      sport: ''
    },
    performance: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      contentType: '',
      sport: ''
    }
  },
  sales: {
    overview: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      groupBy: 'day'
    },
    conversion: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      groupBy: 'day'
    }
  }
};

// Report generation helpers
export const generateReport = async (reportType, config = {}) => {
  const defaultConfig = reportConfigs[reportType]?.overview || {};
  const finalConfig = { ...defaultConfig, ...config };

  switch (reportType) {
    case 'revenue':
      return await getRevenueReport(finalConfig);
    case 'users':
      return await getUserReport(finalConfig);
    case 'content':
      return await getContentReport(finalConfig);
    case 'sales':
      return await getSalesReport(finalConfig);
    case 'performance':
      return await getPerformanceReport(finalConfig);
    default:
      throw new Error('Invalid report type');
  }
};

// Batch report generation
export const generateBatchReports = async (reportTypes, config = {}) => {
  try {
    const reports = await Promise.all(
      reportTypes.map(async (type) => {
        const data = await generateReport(type, config);
        return { type, data };
      })
    );

    return {
      success: true,
      data: reports,
      generatedAt: new Date()
    };
  } catch (error) {
    throw error;
  }
};

// Report scheduling helpers
export const scheduleReport = async (reportConfig) => {
  // This would integrate with a job scheduler in a real implementation
  return {
    success: true,
    message: 'Report scheduled successfully',
    scheduleId: `schedule_${Date.now()}`,
    config: reportConfig
  };
};

// Report templates
export const reportTemplates = {
  dailyDashboard: {
    reports: ['revenue', 'users', 'sales'],
    config: {
      startDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
      endDate: new Date(),
      groupBy: 'hour'
    }
  },
  weeklyExecutive: {
    reports: ['revenue', 'users', 'content', 'sales', 'performance'],
    config: {
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      groupBy: 'day'
    }
  },
  monthlyComprehensive: {
    reports: ['revenue', 'users', 'content', 'sales', 'performance'],
    config: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(),
      groupBy: 'week'
    }
  }
};

// Generate report from template
export const generateTemplateReport = async (templateName) => {
  const template = reportTemplates[templateName];
  if (!template) {
    throw new Error('Invalid template name');
  }

  return await generateBatchReports(template.reports, template.config);
};

// Date range helpers
export const dateRanges = {
  today: () => ({
    startDate: new Date(new Date().setHours(0, 0, 0, 0)),
    endDate: new Date()
  }),
  yesterday: () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return {
      startDate: new Date(yesterday.setHours(0, 0, 0, 0)),
      endDate: new Date(yesterday.setHours(23, 59, 59, 999))
    };
  },
  last7Days: () => ({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    endDate: new Date()
  }),
  last30Days: () => ({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    endDate: new Date()
  }),
  last90Days: () => ({
    startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
    endDate: new Date()
  }),
  thisMonth: () => {
    const now = new Date();
    return {
      startDate: new Date(now.getFullYear(), now.getMonth(), 1),
      endDate: new Date()
    };
  },
  lastMonth: () => {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastDayOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
    return {
      startDate: lastMonth,
      endDate: lastDayOfLastMonth
    };
  },
  thisYear: () => {
    const now = new Date();
    return {
      startDate: new Date(now.getFullYear(), 0, 1),
      endDate: new Date()
    };
  },
  lastYear: () => {
    const now = new Date();
    return {
      startDate: new Date(now.getFullYear() - 1, 0, 1),
      endDate: new Date(now.getFullYear() - 1, 11, 31)
    };
  }
};

export default {
  getRevenueReport,
  getUserReport,
  getContentReport,
  getSalesReport,
  getPerformanceReport,
  getCustomReport,
  exportReport,
  generateReport,
  generateBatchReports,
  generateTemplateReport,
  scheduleReport,
  reportConfigs,
  reportTemplates,
  dateRanges
};
