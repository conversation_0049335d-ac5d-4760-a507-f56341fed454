const express = require('express');
const { check } = require('express-validator');
const {
  getAllContent,
  getContentById,
  approveContent,
  rejectContent,
  deleteContent,
  bulkApproveContent,
  bulkRejectContent,
  bulkDeleteContent,
  getContentStats,
  exportContent,
  updateContentStatus,
  getContentReviews,
  moderateContent,
  featureContent,
  unfeatureContent
} = require('../../controllers/admin/content');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin content routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get all content with filtering, sorting, and pagination
router.get('/', getAllContent);

// Get content statistics
router.get('/stats', getContentStats);

// Export content data
router.get('/export', exportContent);

// Bulk operations
router.post('/bulk-approve', [
  check('contentIds', 'Content IDs array is required').isArray(),
], bulkApproveContent);

router.post('/bulk-reject', [
  check('contentIds', 'Content IDs array is required').isArray(),
  check('reason', 'Rejection reason is required').not().isEmpty(),
], bulkRejectContent);

router.post('/bulk-delete', [
  check('contentIds', 'Content IDs array is required').isArray(),
], bulkDeleteContent);

// Individual content operations
router.get('/:id', getContentById);
router.delete('/:id', deleteContent);

// Content approval workflow
router.put('/:id/approve', [
  check('approvalNotes', 'Approval notes are optional').optional(),
], approveContent);

router.put('/:id/reject', [
  check('reason', 'Rejection reason is required').not().isEmpty(),
  check('rejectionNotes', 'Rejection notes are optional').optional(),
], rejectContent);

// Content status management
router.put('/:id/status', [
  check('status', 'Status is required').isIn(['Draft', 'Under Review', 'Published', 'Rejected', 'Archived']),
], updateContentStatus);

// Content moderation
router.put('/:id/moderate', [
  check('action', 'Moderation action is required').isIn(['flag', 'unflag', 'restrict', 'unrestrict']),
  check('reason', 'Moderation reason is required').not().isEmpty(),
], moderateContent);

// Content featuring
router.put('/:id/feature', featureContent);
router.put('/:id/unfeature', unfeatureContent);

// Content reviews
router.get('/:id/reviews', getContentReviews);

module.exports = router;
