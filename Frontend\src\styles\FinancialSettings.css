/* FinancialSettings Component Styles */
.FinancialSettings {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

.settings-section {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.section-header {
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
}

.section-header h3 {
  margin: 0 0 4px 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
}

.section-header p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Settings Form */
.settings-form {
  padding: var(--heading6);
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Settings Groups */
.settings-group {
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--bg-gray);
}

.settings-group h4 {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin: 0 0 var(--basefont) 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
}

.group-icon {
  color: var(--btn-color);
  font-size: var(--basefont);
}

/* Form Elements */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
  margin-bottom: var(--basefont);
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.form-group label {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.form-input,
.form-select {
  padding: var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.form-input:read-only {
  background-color: var(--bg-gray);
  color: var(--dark-gray);
  cursor: not-allowed;
}

/* Input with Icon */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--smallfont);
  z-index: 1;
}

.input-with-icon .form-input {
  padding-left: 32px;
}

/* Form Help Text */
.form-help {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  font-style: italic;
}

/* Commission Preview */
.commission-preview {
  padding: var(--basefont);
  border: 2px solid var(--btn-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-blue);
}

.commission-preview h4 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.preview-example {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: var(--basefont);
}

.example-sale {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.sale-label {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  text-align: center;
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
}

.breakdown {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--smallfont);
  color: var(--text-color);
}

.breakdown-item.total {
  font-weight: 600;
  font-size: var(--basefont);
  color: var(--secondary-color);
  padding-top: var(--smallfont);
  border-top: 1px solid var(--light-gray);
  margin-top: var(--smallfont);
}

.breakdown-item .amount {
  font-weight: 600;
  color: var(--btn-color);
}

.breakdown-item.total .amount {
  color: #10b981;
  font-size: var(--basefont);
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: var(--basefont);
  justify-content: flex-end;
  padding-top: var(--basefont);
  border-top: 1px solid var(--light-gray);
}

/* Buttons */
.btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.btn.btn-primary:hover:not(:disabled) {
  background-color: #d32f2f;
}

.btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.btn.btn-outline:hover:not(:disabled) {
  background-color: var(--bg-gray);
}

/* Responsive styles */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .settings-form {
    padding: var(--basefont);
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .btn {
    justify-content: center;
  }

  .breakdown-item {
    font-size: var(--extrasmallfont);
  }

  .sale-label {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .section-header {
    padding: var(--basefont);
  }

  .settings-group {
    padding: var(--smallfont);
  }

  .commission-preview {
    padding: var(--smallfont);
  }

  .preview-example {
    padding: var(--smallfont);
  }

  .breakdown-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  .breakdown-item .amount {
    align-self: flex-end;
  }
}

/* Animation for changes */
.form-input.changed,
.form-select.changed {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Validation states */
.form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input.success {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Loading state */
.form-actions.loading .btn {
  position: relative;
  color: transparent;
}

.form-actions.loading .btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
