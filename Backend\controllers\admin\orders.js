const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const Order = require('../../models/Order');
const User = require('../../models/User');
const Content = require('../../models/Content');
const Payment = require('../../models/Payment');

// @desc    Get all orders with filtering, sorting, and pagination
// @route   GET /api/admin/orders
// @access  Private/Admin
exports.getAllOrders = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      paymentStatus = '',
      orderType = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom = '',
      dateTo = '',
      minAmount = '',
      maxAmount = '',
      buyerId = '',
      sellerId = ''
    } = req.query;

    // Build query
    let query = {};

    // Search functionality
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      const users = await User.find({
        $or: [
          { firstName: searchRegex },
          { lastName: searchRegex },
          { email: searchRegex }
        ]
      }).select('_id');

      const userIds = users.map(user => user._id);

      query.$or = [
        { buyer: { $in: userIds } },
        { seller: { $in: userIds } }
      ];
    }

    // Filter by status
    if (status) {
      query.status = status;
    }

    // Filter by payment status
    if (paymentStatus) {
      query.paymentStatus = paymentStatus;
    }

    // Filter by order type
    if (orderType) {
      query.orderType = orderType;
    }

    // Filter by buyer
    if (buyerId) {
      query.buyer = buyerId;
    }

    // Filter by seller
    if (sellerId) {
      query.seller = sellerId;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) {
        query.createdAt.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        query.createdAt.$lte = new Date(dateTo);
      }
    }

    // Amount range filter
    if (minAmount || maxAmount) {
      query.totalAmount = {};
      if (minAmount) {
        query.totalAmount.$gte = parseFloat(minAmount);
      }
      if (maxAmount) {
        query.totalAmount.$lte = parseFloat(maxAmount);
      }
    }

    // Sorting
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const orders = await Order.find(query)
      .populate('buyer', 'firstName lastName email profileImage')
      .populate('seller', 'firstName lastName email profileImage')
      .populate('content', 'title sport contentType price')
      .sort(sortOptions)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    // Get total count for pagination
    const total = await Order.countDocuments(query);

    res.status(200).json({
      success: true,
      data: orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get order by ID
// @route   GET /api/admin/orders/:id
// @access  Private/Admin
exports.getOrderById = async (req, res, next) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('buyer', 'firstName lastName email profileImage mobile phone')
      .populate('seller', 'firstName lastName email profileImage mobile phone')
      .populate('content', 'title description sport contentType price files')
      .populate('bidId', 'amount createdAt')
      .populate('customRequestId', 'title description budget');

    if (!order) {
      return next(new ErrorResponse('Order not found', 404));
    }

    // Get related payments
    const payments = await Payment.find({ orderId: order._id });

    res.status(200).json({
      success: true,
      data: {
        order,
        payments
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update order status
// @route   PUT /api/admin/orders/:id/status
// @access  Private/Admin
exports.updateOrderStatus = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { status, notes } = req.body;

    const order = await Order.findById(req.params.id);
    if (!order) {
      return next(new ErrorResponse('Order not found', 404));
    }

    const oldStatus = order.status;
    order.status = status;

    // Add status change to order history if notes provided
    if (notes) {
      if (!order.statusHistory) {
        order.statusHistory = [];
      }
      order.statusHistory.push({
        status: status,
        notes: notes,
        changedBy: req.user.id,
        changedAt: new Date()
      });
    }

    await order.save();

    res.status(200).json({
      success: true,
      data: order,
      message: `Order status updated from ${oldStatus} to ${status}`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get order statistics
// @route   GET /api/admin/orders/stats
// @access  Private/Admin
exports.getOrderStats = async (req, res, next) => {
  try {
    const totalOrders = await Order.countDocuments();
    const pendingOrders = await Order.countDocuments({ status: 'Pending' });
    const processingOrders = await Order.countDocuments({ status: 'Processing' });
    const completedOrders = await Order.countDocuments({ status: 'Completed' });
    const cancelledOrders = await Order.countDocuments({ status: 'Cancelled' });
    const refundedOrders = await Order.countDocuments({ status: 'Refunded' });

    // Payment status stats
    const pendingPayments = await Order.countDocuments({ paymentStatus: 'Pending' });
    const completedPayments = await Order.countDocuments({ paymentStatus: 'Completed' });
    const failedPayments = await Order.countDocuments({ paymentStatus: 'Failed' });
    const expiredPayments = await Order.countDocuments({ paymentStatus: 'Expired' });

    // Order type stats
    const fixedOrders = await Order.countDocuments({ orderType: 'Fixed' });
    const auctionOrders = await Order.countDocuments({ orderType: 'Auction' });
    const customOrders = await Order.countDocuments({ orderType: 'Custom' });

    // Revenue stats
    const revenueStats = await Order.aggregate([
      { $match: { paymentStatus: 'Completed' } },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          totalPlatformFees: { $sum: '$platformFee' },
          totalSellerEarnings: { $sum: '$sellerEarnings' },
          averageOrderValue: { $avg: '$totalAmount' }
        }
      }
    ]);

    // Monthly stats for current year
    const currentYear = new Date().getFullYear();
    const monthlyStats = await Order.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(`${currentYear}-01-01`),
            $lte: new Date(`${currentYear}-12-31`)
          }
        }
      },
      {
        $group: {
          _id: { $month: '$createdAt' },
          count: { $sum: 1 },
          revenue: { $sum: '$totalAmount' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: {
        overview: {
          total: totalOrders,
          pending: pendingOrders,
          processing: processingOrders,
          completed: completedOrders,
          cancelled: cancelledOrders,
          refunded: refundedOrders
        },
        paymentStatus: {
          pending: pendingPayments,
          completed: completedPayments,
          failed: failedPayments,
          expired: expiredPayments
        },
        orderTypes: {
          fixed: fixedOrders,
          auction: auctionOrders,
          custom: customOrders
        },
        revenue: revenueStats[0] || {
          totalRevenue: 0,
          totalPlatformFees: 0,
          totalSellerEarnings: 0,
          averageOrderValue: 0
        },
        monthlyStats
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete order
// @route   DELETE /api/admin/orders/:id
// @access  Private/Admin
exports.deleteOrder = async (req, res, next) => {
  try {
    const order = await Order.findById(req.params.id);
    if (!order) {
      return next(new ErrorResponse('Order not found', 404));
    }

    // Check if order can be deleted (only pending/cancelled orders)
    if (!['Pending', 'Cancelled'].includes(order.status)) {
      return next(new ErrorResponse('Cannot delete orders that are processing or completed', 400));
    }

    await order.deleteOne();

    res.status(200).json({
      success: true,
      message: 'Order deleted successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk update orders
// @route   POST /api/admin/orders/bulk-update
// @access  Private/Admin
exports.bulkUpdateOrders = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { orderIds, status } = req.body;

    const result = await Order.updateMany(
      { _id: { $in: orderIds } },
      { status: status }
    );

    res.status(200).json({
      success: true,
      data: {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount
      },
      message: `${result.modifiedCount} orders updated to ${status}`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk delete orders
// @route   POST /api/admin/orders/bulk-delete
// @access  Private/Admin
exports.bulkDeleteOrders = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { orderIds } = req.body;

    // Only allow deletion of pending/cancelled orders
    const result = await Order.deleteMany({
      _id: { $in: orderIds },
      status: { $in: ['Pending', 'Cancelled'] }
    });

    res.status(200).json({
      success: true,
      data: {
        deletedCount: result.deletedCount
      },
      message: `${result.deletedCount} orders deleted successfully`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export orders data
// @route   GET /api/admin/orders/export
// @access  Private/Admin
exports.exportOrders = async (req, res, next) => {
  try {
    const {
      format = 'csv',
      status = '',
      dateFrom = '',
      dateTo = ''
    } = req.query;

    // Build query for export
    let query = {};
    if (status) query.status = status;
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
      if (dateTo) query.createdAt.$lte = new Date(dateTo);
    }

    const orders = await Order.find(query)
      .populate('buyer', 'firstName lastName email')
      .populate('seller', 'firstName lastName email')
      .populate('content', 'title sport contentType')
      .sort('-createdAt');

    // Format data for export
    const exportData = orders.map(order => ({
      orderId: order._id,
      orderType: order.orderType,
      buyerName: `${order.buyer.firstName} ${order.buyer.lastName}`,
      buyerEmail: order.buyer.email,
      sellerName: `${order.seller.firstName} ${order.seller.lastName}`,
      sellerEmail: order.seller.email,
      contentTitle: order.content.title,
      contentType: order.content.contentType,
      sport: order.content.sport,
      amount: order.amount,
      platformFee: order.platformFee,
      sellerEarnings: order.sellerEarnings,
      totalAmount: order.totalAmount,
      status: order.status,
      paymentStatus: order.paymentStatus,
      createdAt: order.createdAt,
      downloadCount: order.downloadCount
    }));

    if (format === 'json') {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=orders.json');
      return res.json(exportData);
    }

    // Default CSV format
    const csv = [
      // CSV headers
      'Order ID,Order Type,Buyer Name,Buyer Email,Seller Name,Seller Email,Content Title,Content Type,Sport,Amount,Platform Fee,Seller Earnings,Total Amount,Status,Payment Status,Created At,Download Count',
      // CSV data
      ...exportData.map(order =>
        `${order.orderId},${order.orderType},"${order.buyerName}",${order.buyerEmail},"${order.sellerName}",${order.sellerEmail},"${order.contentTitle}",${order.contentType},${order.sport},${order.amount},${order.platformFee},${order.sellerEarnings},${order.totalAmount},${order.status},${order.paymentStatus},${order.createdAt},${order.downloadCount}`
      )
    ].join('\n');

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=orders.csv');
    res.send(csv);
  } catch (err) {
    next(err);
  }
};

// @desc    Process refund for order
// @route   POST /api/admin/orders/:id/refund
// @access  Private/Admin
exports.processRefund = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { amount, reason } = req.body;
    const order = await Order.findById(req.params.id);

    if (!order) {
      return next(new ErrorResponse('Order not found', 404));
    }

    if (order.paymentStatus !== 'Completed') {
      return next(new ErrorResponse('Cannot refund order that is not completed', 400));
    }

    if (amount > order.totalAmount) {
      return next(new ErrorResponse('Refund amount cannot exceed order total', 400));
    }

    // Update order status
    order.status = 'Refunded';
    order.paymentStatus = 'Refunded';
    order.refundAmount = amount;
    order.refundReason = reason;
    order.refundedAt = new Date();
    order.refundedBy = req.user.id;

    await order.save();

    res.status(200).json({
      success: true,
      data: order,
      message: `Refund of $${amount} processed successfully`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get order analytics
// @route   GET /api/admin/orders/analytics
// @access  Private/Admin
exports.getOrderAnalytics = async (req, res, next) => {
  try {
    const { period = '30d', groupBy = 'day' } = req.query;

    // Calculate date range
    let startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate = new Date('2020-01-01'); // All time
    }

    // Group by format
    let groupFormat;
    switch (groupBy) {
      case 'day':
        groupFormat = { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } };
        break;
      case 'week':
        groupFormat = { $dateToString: { format: "%Y-W%U", date: "$createdAt" } };
        break;
      case 'month':
        groupFormat = { $dateToString: { format: "%Y-%m", date: "$createdAt" } };
        break;
      default:
        groupFormat = { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } };
    }

    const analytics = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: groupFormat,
          totalOrders: { $sum: 1 },
          totalRevenue: { $sum: '$totalAmount' },
          completedOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'Completed'] }, 1, 0] }
          },
          cancelledOrders: {
            $sum: { $cond: [{ $eq: ['$status', 'Cancelled'] }, 1, 0] }
          },
          averageOrderValue: { $avg: '$totalAmount' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get order timeline
// @route   GET /api/admin/orders/:id/timeline
// @access  Private/Admin
exports.getOrderTimeline = async (req, res, next) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('buyer', 'firstName lastName')
      .populate('seller', 'firstName lastName');

    if (!order) {
      return next(new ErrorResponse('Order not found', 404));
    }

    // Build timeline from order data and status history
    let timeline = [
      {
        event: 'Order Created',
        timestamp: order.createdAt,
        status: 'Pending',
        description: `Order created by ${order.buyer.firstName} ${order.buyer.lastName}`
      }
    ];

    // Add status history if available
    if (order.statusHistory && order.statusHistory.length > 0) {
      const statusEvents = order.statusHistory.map(history => ({
        event: 'Status Changed',
        timestamp: history.changedAt,
        status: history.status,
        description: history.notes || `Status changed to ${history.status}`,
        changedBy: history.changedBy
      }));
      timeline = [...timeline, ...statusEvents];
    }

    // Add payment events
    const payments = await Payment.find({ orderId: order._id });
    payments.forEach(payment => {
      timeline.push({
        event: 'Payment Event',
        timestamp: payment.createdAt,
        status: payment.status,
        description: `Payment ${payment.status.toLowerCase()} - $${payment.amount}`
      });
    });

    // Sort timeline by timestamp
    timeline.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    res.status(200).json({
      success: true,
      data: timeline
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Flag order
// @route   PUT /api/admin/orders/:id/flag
// @access  Private/Admin
exports.flagOrder = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reason } = req.body;
    const order = await Order.findById(req.params.id);

    if (!order) {
      return next(new ErrorResponse('Order not found', 404));
    }

    order.isFlagged = true;
    order.flagReason = reason;
    order.flaggedBy = req.user.id;
    order.flaggedAt = new Date();

    await order.save();

    res.status(200).json({
      success: true,
      data: order,
      message: 'Order flagged successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Unflag order
// @route   PUT /api/admin/orders/:id/unflag
// @access  Private/Admin
exports.unflagOrder = async (req, res, next) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return next(new ErrorResponse('Order not found', 404));
    }

    order.isFlagged = false;
    order.flagReason = undefined;
    order.flaggedBy = undefined;
    order.flaggedAt = undefined;
    order.unflaggedBy = req.user.id;
    order.unflaggedAt = new Date();

    await order.save();

    res.status(200).json({
      success: true,
      data: order,
      message: 'Order unflagged successfully'
    });
  } catch (err) {
    next(err);
  }
};
