.sidebar-component {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--z-index-modal);
  pointer-events: none;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s linear 0.3s,
    opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.sidebar-component.active {
  pointer-events: auto;
  visibility: visible;
  opacity: 1;
  transition: visibility 0s, opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.sidebar-component .sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 120%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: opacity;
  cursor: pointer;
  touch-action: none;
}

.sidebar-component.active .sidebar-overlay {
  opacity: 1;
}

.sidebar-component .sidebar {
  position: absolute;
  top: 0;
  left: 0;
  width: 75%;
  height: 100%;
  background-color: var(--white);
  box-shadow: var(--box-shadow);
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  padding: 20px;
  will-change: transform;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* for iOS Safari momentum */
  overscroll-behavior: contain; /* prevent scroll chaining */
  scroll-behavior: smooth;
  touch-action: pan-y;
}

.sidebar-component.active .sidebar {
  transform: translateX(0);
}

.sidebar-component .sidebar-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--light-gray);
}

.sidebar-component .sidebar-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-component .sidebar-logo img {
  height: 60px;
  aspect-ratio: 1/1;

  transition: opacity 0.3s ease;
}

.sidebar-component .sidebar-logo a:hover img {
  opacity: 0.8;
}

.sidebar-component .sidebar-links {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sidebar-component .sidebar-links a {
  text-decoration: none;
  color: var(--secondary-color);
  font-weight: 500;
  font-size: var(--basefont);
  transition: color 0.3s ease;
  padding: 5px 0;
  display: flex;
  align-items: center;
}

.sidebar-component .sidebar-icon {
  margin-right: 10px;
  font-size: var(--basefont);
}

.sidebar-component .sidebar-links a:hover {
  color: var(--btn-color);
}

.sidebar-component .sidebar-links a.active {
  color: var(--btn-color);
  font-weight: 600;
}

.sidebar-component .sidebar-auth {
  display: flex;
  flex-direction: column;
  gap: 10px;

  align-items: center;
}

.sidebar-component .sidebar-auth .btn {
  display: flex;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  text-align: center;
  font-weight: 500;
  transition: all 0.3s ease;
}

.sidebar-component .sidebar-auth .signinbtn {
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
}

.sidebar-component .sidebar-auth .signinbtn:hover {
  background-color: var(--btn-color);
  color: var(--white);
}

.sidebar-component .sidebar-auth .signupbtn {
  background-color: var(--btn-color);
  color: var(--white);
  border: 1px solid var(--btn-color);
}

.sidebar-component .sidebar-auth .signupbtn:hover {
  background-color: var(--btn-color);
  border-color: var(--btn-color);
}

.sidebar-component .sidebar-auth .btn-outline {
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
}

.sidebar-component .sidebar-auth .btn-outline:hover {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Account dropdown styles */
.sidebar-component .sidebar-account-section {
  margin-top: 10px;

  padding-top: 10px;
}

.sidebar-component .sidebar-account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 5px;
  cursor: pointer;
  color: var(--secondary-color);
  font-weight: 500;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.sidebar-component .sidebar-account-header:hover {
  color: var(--btn-color);
}

.sidebar-component .sidebar-dropdown-icon {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.sidebar-component .sidebar-dropdown-icon.active {
  transform: rotate(180deg);
}

.sidebar-component .sidebar-account-dropdown {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 10px 0 10px 15px;
  margin-top: 5px;
  border-left: 2px solid var(--light-gray);
}

.sidebar-component .sidebar-account-dropdown a {
  padding: 5px 0;
}

/* Role dropdown in sidebar */
.sidebar-component .sidebar-role-dropdown {
  padding: var(--basefont) 0;
  margin: var(--smallfont) 0;
}
.sidebar-component .sidebtnset {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  width: 100%;
  gap: 10px;
}
/* Only show sidebar on small screens */
@media (min-width: 769px) {
  .sidebar-component {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .sidebar-component .sidebar-auth {
    display: flex;

    flex-direction: row;
    width: fit-content;
    justify-content: space-between;
    align-items: center;
  }
}
@media (max-width: 480px) {
  .sidebar-component .sidebar {
    width: 85%;
  }
}

@media (max-width: 330px) {
  .sidebar-component .sidebar {
    width: 90%;
  }

  .sidebar-component .sidebtnset {
    display: grid;
    margin-top: 2rem;
  }

  .sidebar-role-dropdown {
    padding: 0 0;
    margin: 0 0;
    height: fit-content;
  }
  .sidebar-component .sidebar-role-dropdown {
    padding: 0;
    margin: 0;
  }
}
