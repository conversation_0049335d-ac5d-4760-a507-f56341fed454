const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const Bid = require('../../models/Bid');
const Content = require('../../models/Content');
const User = require('../../models/User');
const Order = require('../../models/Order');

// @desc    Get all bids with filtering, sorting, and pagination
// @route   GET /api/admin/bids
// @access  Private/Admin
exports.getAllBids = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      contentId = '',
      bidderId = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom = '',
      dateTo = '',
      minAmount = '',
      maxAmount = ''
    } = req.query;

    // Build query
    let query = {};

    // Search filter (search in content title or bidder name)
    if (search) {
      const users = await User.find({
        $or: [
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      const content = await Content.find({
        title: { $regex: search, $options: 'i' }
      }).select('_id');

      query.$or = [
        { bidder: { $in: users.map(u => u._id) } },
        { content: { $in: content.map(c => c._id) } }
      ];
    }

    // Status filter
    if (status) {
      query.status = status;
    }

    // Content filter
    if (contentId) {
      query.content = contentId;
    }

    // Bidder filter
    if (bidderId) {
      query.bidder = bidderId;
    }

    // Amount range filter
    if (minAmount || maxAmount) {
      query.amount = {};
      if (minAmount) query.amount.$gte = parseFloat(minAmount);
      if (maxAmount) query.amount.$lte = parseFloat(maxAmount);
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
      if (dateTo) query.createdAt.$lte = new Date(dateTo);
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query
    const bids = await Bid.find(query)
      .populate('bidder', 'firstName lastName email')
      .populate('content', 'title sport contentType seller')
      .populate({
        path: 'content',
        populate: {
          path: 'seller',
          select: 'firstName lastName email'
        }
      })
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Bid.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        bids,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
          limit: parseInt(limit)
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get bid by ID with detailed information
// @route   GET /api/admin/bids/:id
// @access  Private/Admin
exports.getBidById = async (req, res, next) => {
  try {
    const bid = await Bid.findById(req.params.id)
      .populate('bidder', 'firstName lastName email profilePic')
      .populate('content', 'title sport contentType price seller auctionDetails')
      .populate({
        path: 'content',
        populate: {
          path: 'seller',
          select: 'firstName lastName email'
        }
      });

    if (!bid) {
      return next(new ErrorResponse('Bid not found', 404));
    }

    // Get related bids for the same content
    const relatedBids = await Bid.find({
      content: bid.content._id,
      _id: { $ne: bid._id }
    })
      .populate('bidder', 'firstName lastName')
      .sort('-amount')
      .limit(10);

    // Get bidder's bid history
    const bidderHistory = await Bid.find({ bidder: bid.bidder._id })
      .populate('content', 'title')
      .sort('-createdAt')
      .limit(5);

    res.status(200).json({
      success: true,
      data: {
        bid,
        relatedBids,
        bidderHistory
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Approve bid
// @route   PUT /api/admin/bids/:id/approve
// @access  Private/Admin
exports.approveBid = async (req, res, next) => {
  try {
    const { approvalNotes } = req.body;

    const bid = await Bid.findById(req.params.id);
    if (!bid) {
      return next(new ErrorResponse('Bid not found', 404));
    }

    const updatedBid = await Bid.findByIdAndUpdate(
      req.params.id,
      {
        status: 'Active',
        approvalDate: new Date(),
        approvalNotes,
        approvedBy: req.user.id
      },
      { new: true }
    ).populate('bidder', 'firstName lastName email')
      .populate('content', 'title');

    res.status(200).json({
      success: true,
      data: updatedBid
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Reject bid
// @route   PUT /api/admin/bids/:id/reject
// @access  Private/Admin
exports.rejectBid = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reason, rejectionNotes } = req.body;

    const bid = await Bid.findById(req.params.id);
    if (!bid) {
      return next(new ErrorResponse('Bid not found', 404));
    }

    const updatedBid = await Bid.findByIdAndUpdate(
      req.params.id,
      {
        status: 'Rejected',
        rejectionReason: reason,
        rejectionNotes,
        rejectionDate: new Date(),
        rejectedBy: req.user.id
      },
      { new: true }
    ).populate('bidder', 'firstName lastName email')
      .populate('content', 'title');

    res.status(200).json({
      success: true,
      data: updatedBid
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete bid
// @route   DELETE /api/admin/bids/:id
// @access  Private/Admin
exports.deleteBid = async (req, res, next) => {
  try {
    const bid = await Bid.findById(req.params.id);

    if (!bid) {
      return next(new ErrorResponse('Bid not found', 404));
    }

    // Check if bid is part of a completed order
    const hasOrder = await Order.countDocuments({
      content: bid.content,
      buyer: bid.bidder,
      status: 'Completed'
    });

    if (hasOrder > 0) {
      // Soft delete - mark as cancelled
      await Bid.findByIdAndUpdate(req.params.id, {
        status: 'Cancelled',
        cancelledAt: new Date(),
        cancelledBy: req.user.id,
        cancellationReason: 'Deleted by admin'
      });
    } else {
      // Hard delete if no completed orders
      await Bid.findByIdAndDelete(req.params.id);
    }

    res.status(200).json({
      success: true,
      message: 'Bid deleted successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk approve bids
// @route   POST /api/admin/bids/bulk-approve
// @access  Private/Admin
exports.bulkApproveBids = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { bidIds } = req.body;

    const result = await Bid.updateMany(
      { _id: { $in: bidIds } },
      {
        status: 'Active',
        approvalDate: new Date(),
        approvedBy: req.user.id
      }
    );

    res.status(200).json({
      success: true,
      message: `${result.modifiedCount} bids approved successfully`,
      data: {
        matched: result.matchedCount,
        modified: result.modifiedCount
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk reject bids
// @route   POST /api/admin/bids/bulk-reject
// @access  Private/Admin
exports.bulkRejectBids = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { bidIds, reason } = req.body;

    const result = await Bid.updateMany(
      { _id: { $in: bidIds } },
      {
        status: 'Rejected',
        rejectionReason: reason,
        rejectionDate: new Date(),
        rejectedBy: req.user.id
      }
    );

    res.status(200).json({
      success: true,
      message: `${result.modifiedCount} bids rejected successfully`,
      data: {
        matched: result.matchedCount,
        modified: result.modifiedCount
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk delete bids
// @route   POST /api/admin/bids/bulk-delete
// @access  Private/Admin
exports.bulkDeleteBids = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { bidIds } = req.body;

    let softDeleted = 0;
    let hardDeleted = 0;

    for (const bidId of bidIds) {
      const bid = await Bid.findById(bidId);
      if (!bid) continue;

      const hasOrder = await Order.countDocuments({
        content: bid.content,
        buyer: bid.bidder,
        status: 'Completed'
      });

      if (hasOrder > 0) {
        // Soft delete
        await Bid.findByIdAndUpdate(bidId, {
          status: 'Cancelled',
          cancelledAt: new Date(),
          cancelledBy: req.user.id,
          cancellationReason: 'Bulk deleted by admin'
        });
        softDeleted++;
      } else {
        // Hard delete
        await Bid.findByIdAndDelete(bidId);
        hardDeleted++;
      }
    }

    res.status(200).json({
      success: true,
      message: `${softDeleted + hardDeleted} bids deleted successfully`,
      data: {
        softDeleted,
        hardDeleted,
        total: softDeleted + hardDeleted
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get bid statistics
// @route   GET /api/admin/bids/stats
// @access  Private/Admin
exports.getBidStats = async (req, res, next) => {
  try {
    const totalBids = await Bid.countDocuments();
    const activeBids = await Bid.countDocuments({ status: 'Active' });
    const wonBids = await Bid.countDocuments({ status: 'Won' });
    const expiredBids = await Bid.countDocuments({ status: 'Expired' });
    const cancelledBids = await Bid.countDocuments({ status: 'Cancelled' });

    const bidsByStatus = await Bid.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Average bid amount
    const avgBidAmount = await Bid.aggregate([
      {
        $group: {
          _id: null,
          avgAmount: { $avg: '$amount' }
        }
      }
    ]);

    // Bids in last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const newBids = await Bid.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });

    // Top bidders
    const topBidders = await Bid.aggregate([
      {
        $group: {
          _id: '$bidder',
          totalBids: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          wonBids: {
            $sum: { $cond: [{ $eq: ['$status', 'Won'] }, 1, 0] }
          }
        }
      },
      { $sort: { totalAmount: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'bidderInfo'
        }
      },
      { $unwind: '$bidderInfo' },
      {
        $project: {
          name: { $concat: ['$bidderInfo.firstName', ' ', '$bidderInfo.lastName'] },
          email: '$bidderInfo.email',
          totalBids: 1,
          totalAmount: 1,
          wonBids: 1
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        total: totalBids,
        active: activeBids,
        won: wonBids,
        expired: expiredBids,
        cancelled: cancelledBids,
        newInLast30Days: newBids,
        averageAmount: avgBidAmount[0]?.avgAmount || 0,
        byStatus: bidsByStatus,
        topBidders
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export bids data
// @route   GET /api/admin/bids/export
// @access  Private/Admin
exports.exportBids = async (req, res, next) => {
  try {
    const { format = 'csv', status = '', dateFrom = '', dateTo = '' } = req.query;

    let query = {};
    if (status) query.status = status;
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
      if (dateTo) query.createdAt.$lte = new Date(dateTo);
    }

    const bids = await Bid.find(query)
      .populate('bidder', 'firstName lastName email')
      .populate('content', 'title sport contentType')
      .sort('-createdAt');

    if (format === 'json') {
      res.status(200).json({
        success: true,
        data: bids
      });
    } else {
      // For CSV format, you would implement CSV generation here
      res.status(200).json({
        success: true,
        message: 'CSV export functionality to be implemented',
        data: bids
      });
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Update bid status
// @route   PUT /api/admin/bids/:id/status
// @access  Private/Admin
exports.updateBidStatus = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { status } = req.body;

    const bid = await Bid.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true }
    ).populate('bidder', 'firstName lastName email')
      .populate('content', 'title');

    if (!bid) {
      return next(new ErrorResponse('Bid not found', 404));
    }

    res.status(200).json({
      success: true,
      data: bid
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Cancel bid
// @route   PUT /api/admin/bids/:id/cancel
// @access  Private/Admin
exports.cancelBid = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reason } = req.body;

    const bid = await Bid.findByIdAndUpdate(
      req.params.id,
      {
        status: 'Cancelled',
        cancellationReason: reason,
        cancelledAt: new Date(),
        cancelledBy: req.user.id
      },
      { new: true }
    ).populate('bidder', 'firstName lastName email')
      .populate('content', 'title');

    if (!bid) {
      return next(new ErrorResponse('Bid not found', 404));
    }

    res.status(200).json({
      success: true,
      data: bid
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Flag bid
// @route   PUT /api/admin/bids/:id/flag
// @access  Private/Admin
exports.flagBid = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reason } = req.body;

    const bid = await Bid.findByIdAndUpdate(
      req.params.id,
      {
        isFlagged: true,
        flagReason: reason,
        flaggedAt: new Date(),
        flaggedBy: req.user.id
      },
      { new: true }
    ).populate('bidder', 'firstName lastName email')
      .populate('content', 'title');

    if (!bid) {
      return next(new ErrorResponse('Bid not found', 404));
    }

    res.status(200).json({
      success: true,
      data: bid
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Unflag bid
// @route   PUT /api/admin/bids/:id/unflag
// @access  Private/Admin
exports.unflagBid = async (req, res, next) => {
  try {
    const bid = await Bid.findByIdAndUpdate(
      req.params.id,
      {
        isFlagged: false,
        $unset: { flagReason: 1, flaggedAt: 1, flaggedBy: 1 }
      },
      { new: true }
    ).populate('bidder', 'firstName lastName email')
      .populate('content', 'title');

    if (!bid) {
      return next(new ErrorResponse('Bid not found', 404));
    }

    res.status(200).json({
      success: true,
      data: bid
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get auction statistics
// @route   GET /api/admin/bids/auction-stats
// @access  Private/Admin
exports.getAuctionStats = async (req, res, next) => {
  try {
    // Active auctions
    const activeAuctions = await Content.countDocuments({
      saleType: { $in: ['Auction', 'Both'] },
      status: 'Published',
      'auctionDetails.endTime': { $gt: new Date() }
    });

    // Completed auctions
    const completedAuctions = await Content.countDocuments({
      saleType: { $in: ['Auction', 'Both'] },
      'auctionDetails.endTime': { $lt: new Date() }
    });

    // Auctions with bids
    const auctionsWithBids = await Bid.aggregate([
      {
        $group: {
          _id: '$content',
          bidCount: { $sum: 1 }
        }
      },
      { $count: 'total' }
    ]);

    // Average bids per auction
    const avgBidsPerAuction = await Bid.aggregate([
      {
        $group: {
          _id: '$content',
          bidCount: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: null,
          avgBids: { $avg: '$bidCount' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        activeAuctions,
        completedAuctions,
        auctionsWithBids: auctionsWithBids[0]?.total || 0,
        averageBidsPerAuction: avgBidsPerAuction[0]?.avgBids || 0
      }
    });
  } catch (err) {
    next(err);
  }
};
