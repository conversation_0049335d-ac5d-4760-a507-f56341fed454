// Admin Services
export { default as adminDashboardService } from './adminDashboardService';
export { default as adminUserService } from './adminUserService';
export { default as adminContentService } from './adminContentService';
export { default as adminBidService } from './adminBidService';
export { default as adminCMSService } from './adminCMSService';
export { default as adminReportsService } from './adminReportsService';
export { default as adminSettingsService } from './adminSettingsService';
export { default as adminAnalyticsService } from './adminAnalyticsService';

// Re-export specific functions for convenience
export {
  // Dashboard
  getDashboardStats,
  getRecentActivity,
  getPendingApprovals,
  getAnalytics,
  getTopPerformers,
  getSystemHealth
} from './adminDashboardService';

export {
  // Users
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  bulkUpdateUsers,
  bulkDeleteUsers,
  verifyUser,
  suspendUser,
  getUserActivityHistory,
  getUserStats,
  exportUsers
} from './adminUserService';

export {
  // Content
  getAllContent,
  getContentById,
  updateContent,
  approveContent,
  rejectContent,
  deleteContent,
  bulkApproveContent,
  bulkRejectContent,
  bulkDeleteContent,
  featureContent,
  unfeatureContent,
  contentModerationActions,
  getContentStats,
  exportContent
} from './adminContentService';

export {
  // Bids
  getAllBids,
  getBidById,
  approveBid,
  rejectBid,
  deleteBid,
  bulkApproveBids,
  bulkRejectBids,
  bulkDeleteBids,
  updateBidStatus,
  cancelBid,
  flagBid,
  unflagBid,
  getBidStats,
  getAuctionStats,
  exportBids
} from './adminBidService';

export {
  // CMS
  getAllCMSPages,
  getCMSPageById,
  getCMSPageBySlug,
  createCMSPage,
  updateCMSPage,
  deleteCMSPage,
  bulkDeleteCMSPages,
  publishCMSPage,
  unpublishCMSPage,
  updateCMSPageStatus,
  duplicateCMSPage,
  getCMSStats,
  exportCMSPages
} from './adminCMSService';

export {
  // Reports
  getRevenueReport,
  getUserReport,
  getContentReport,
  getSalesReport,
  getPerformanceReport,
  getCustomReport,
  exportReport,
  generateReport,
  generateBatchReports,
  generateTemplateReport
} from './adminReportsService';

export {
  // Settings
  getAllSettings,
  updateSettings,
  getFinancialSettings,
  updateFinancialSettings,
  getEmailSettings,
  updateEmailSettings,
  getSecuritySettings,
  updateSecuritySettings,
  getSystemSettings,
  updateSystemSettings,
  resetSettings,
  exportSettings,
  importSettings,
  getSettingsHistory,
  restoreSettings
} from './adminSettingsService';

export {
  // Analytics
  getDashboardAnalytics,
  getUserAnalytics,
  getContentAnalytics,
  getRevenueAnalytics,
  getSalesAnalytics,
  getTrafficAnalytics,
  getConversionAnalytics,
  getRealTimeAnalytics,
  getComprehensiveAnalytics
} from './adminAnalyticsService';

// Admin service utilities
export const adminServiceUtils = {
  // Common API configurations
  getApiConfig: () => ({
    baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    }
  }),

  // Common error handler
  handleApiError: (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error.response?.data || error.message);
  },

  // Common success handler
  handleApiSuccess: (response) => {
    return response.data;
  },

  // Pagination helpers
  getPaginationParams: (page = 1, limit = 10, search = '', sortBy = 'createdAt', sortOrder = 'desc') => ({
    page,
    limit,
    search,
    sortBy,
    sortOrder
  }),

  // Date range helpers
  getDateRange: (period) => {
    const now = new Date();
    const ranges = {
      '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      '90d': new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
      '1y': new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
    };
    return {
      startDate: ranges[period] || ranges['30d'],
      endDate: now
    };
  },

  // Format helpers
  formatCurrency: (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  },

  formatNumber: (number) => {
    return new Intl.NumberFormat('en-US').format(number);
  },

  formatPercentage: (value, decimals = 1) => {
    return `${value.toFixed(decimals)}%`;
  },

  formatDate: (date, options = {}) => {
    const defaultOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(new Date(date));
  },

  // Validation helpers
  validateEmail: (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  },

  validateRequired: (value) => {
    return value !== null && value !== undefined && value !== '';
  },

  validateMinLength: (value, minLength) => {
    return value && value.length >= minLength;
  },

  validateMaxLength: (value, maxLength) => {
    return !value || value.length <= maxLength;
  },

  // File helpers
  downloadFile: (data, filename, type = 'application/json') => {
    const blob = new Blob([data], { type });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  },

  // Status helpers
  getStatusColor: (status) => {
    const colors = {
      active: 'green',
      inactive: 'red',
      pending: 'yellow',
      approved: 'green',
      rejected: 'red',
      published: 'green',
      draft: 'gray',
      archived: 'orange',
      completed: 'green',
      cancelled: 'red',
      processing: 'blue'
    };
    return colors[status?.toLowerCase()] || 'gray';
  },

  getStatusIcon: (status) => {
    const icons = {
      active: 'check-circle',
      inactive: 'x-circle',
      pending: 'clock',
      approved: 'check-circle',
      rejected: 'x-circle',
      published: 'eye',
      draft: 'edit',
      archived: 'archive',
      completed: 'check-circle',
      cancelled: 'x-circle',
      processing: 'loader'
    };
    return icons[status?.toLowerCase()] || 'circle';
  }
};

// Admin service constants
export const adminServiceConstants = {
  API_ENDPOINTS: {
    DASHBOARD: '/admin/dashboard',
    USERS: '/admin/users',
    CONTENT: '/admin/content',
    BIDS: '/admin/bids',
    CMS: '/admin/cms',
    REPORTS: '/admin/reports',
    SETTINGS: '/admin/settings',
    ANALYTICS: '/admin/analytics'
  },

  STATUS_OPTIONS: {
    USER: ['active', 'inactive', 'suspended', 'pending'],
    CONTENT: ['published', 'draft', 'under_review', 'rejected', 'archived'],
    BID: ['active', 'won', 'expired', 'cancelled'],
    CMS: ['published', 'draft', 'archived'],
    ORDER: ['pending', 'processing', 'completed', 'cancelled', 'refunded']
  },

  ROLES: ['admin', 'user', 'seller', 'buyer'],

  CONTENT_TYPES: ['video', 'course', 'ebook', 'live', 'audio'],

  SPORTS: ['football', 'basketball', 'soccer', 'tennis', 'baseball', 'hockey', 'golf', 'swimming'],

  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 10,
    MAX_LIMIT: 100
  },

  DATE_FORMATS: {
    SHORT: 'MMM dd, yyyy',
    LONG: 'MMMM dd, yyyy HH:mm',
    TIME: 'HH:mm:ss'
  }
};
