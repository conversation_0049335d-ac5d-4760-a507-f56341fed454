const express = require('express');
const { check } = require('express-validator');
const {
  getAllSettings,
  updateSettings,
  getFinancialSettings,
  updateFinancialSettings,
  getEmailSettings,
  updateEmailSettings,
  getSecuritySettings,
  updateSecuritySettings,
  getSystemSettings,
  updateSystemSettings,
  resetSettings,
  exportSettings,
  importSettings,
  getSettingsHistory,
  restoreSettings
} = require('../../controllers/admin/settings');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin settings routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// General settings
router.get('/', getAllSettings);
router.put('/', updateSettings);

// Financial settings
router.get('/financial', getFinancialSettings);
router.put('/financial', [
  check('platformCommission', 'Platform commission must be a number between 0 and 100')
    .optional()
    .isFloat({ min: 0, max: 100 }),
  check('sellerPayout', 'Seller payout must be a number between 0 and 100')
    .optional()
    .isFloat({ min: 0, max: 100 }),
  check('minimumPayout', 'Minimum payout must be a positive number')
    .optional()
    .isFloat({ min: 0 }),
  check('processingFee', 'Processing fee must be a number between 0 and 100')
    .optional()
    .isFloat({ min: 0, max: 100 }),
  check('payoutSchedule', 'Payout schedule must be valid')
    .optional()
    .isIn(['daily', 'weekly', 'monthly']),
], updateFinancialSettings);

// Email settings
router.get('/email', getEmailSettings);
router.put('/email', [
  check('smtpHost', 'SMTP host is required').optional().not().isEmpty(),
  check('smtpPort', 'SMTP port must be a valid port number')
    .optional()
    .isInt({ min: 1, max: 65535 }),
  check('smtpUser', 'SMTP user is required').optional().not().isEmpty(),
  check('smtpPassword', 'SMTP password is required').optional().not().isEmpty(),
  check('fromEmail', 'From email must be valid').optional().isEmail(),
  check('fromName', 'From name is required').optional().not().isEmpty(),
], updateEmailSettings);

// Security settings
router.get('/security', getSecuritySettings);
router.put('/security', [
  check('sessionTimeout', 'Session timeout must be a positive number')
    .optional()
    .isInt({ min: 1 }),
  check('passwordExpiry', 'Password expiry must be a positive number')
    .optional()
    .isInt({ min: 1 }),
  check('maxLoginAttempts', 'Max login attempts must be a positive number')
    .optional()
    .isInt({ min: 1 }),
  check('lockoutDuration', 'Lockout duration must be a positive number')
    .optional()
    .isInt({ min: 1 }),
], updateSecuritySettings);

// System settings
router.get('/system', getSystemSettings);
router.put('/system', [
  check('siteName', 'Site name is required').optional().not().isEmpty(),
  check('siteDescription', 'Site description is required').optional().not().isEmpty(),
  check('contactEmail', 'Contact email must be valid').optional().isEmail(),
  check('supportEmail', 'Support email must be valid').optional().isEmail(),
  check('maxFileSize', 'Max file size must be a positive number')
    .optional()
    .isInt({ min: 1 }),
  check('allowedFormats', 'Allowed formats must be an array')
    .optional()
    .isArray(),
], updateSystemSettings);

// Settings management
router.post('/reset', [
  check('category', 'Settings category is required').not().isEmpty(),
], resetSettings);

router.get('/export', exportSettings);

router.post('/import', [
  check('settings', 'Settings object is required').isObject(),
], importSettings);

// Settings history
router.get('/history', getSettingsHistory);

router.post('/restore/:id', restoreSettings);

module.exports = router;
