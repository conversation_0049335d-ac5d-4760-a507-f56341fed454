import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/analytics`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Get dashboard analytics overview
export const getDashboardAnalytics = async (period = '30d') => {
  try {
    const response = await api.get('/dashboard', { params: { period } });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get user analytics
export const getUserAnalytics = async (params = {}) => {
  try {
    const response = await api.get('/users', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get content analytics
export const getContentAnalytics = async (params = {}) => {
  try {
    const response = await api.get('/content', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get revenue analytics
export const getRevenueAnalytics = async (params = {}) => {
  try {
    const response = await api.get('/revenue', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get sales analytics
export const getSalesAnalytics = async (params = {}) => {
  try {
    const response = await api.get('/sales', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get traffic analytics
export const getTrafficAnalytics = async (params = {}) => {
  try {
    const response = await api.get('/traffic', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get conversion analytics
export const getConversionAnalytics = async (params = {}) => {
  try {
    const response = await api.get('/conversion', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get real-time analytics
export const getRealTimeAnalytics = async () => {
  try {
    const response = await api.get('/realtime');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Analytics configurations
export const analyticsConfigs = {
  periods: [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' },
    { value: 'all', label: 'All time' }
  ],
  groupBy: [
    { value: 'day', label: 'Daily' },
    { value: 'week', label: 'Weekly' },
    { value: 'month', label: 'Monthly' },
    { value: 'year', label: 'Yearly' }
  ],
  userTypes: [
    { value: 'all', label: 'All Users' },
    { value: 'buyer', label: 'Buyers' },
    { value: 'seller', label: 'Sellers' }
  ],
  contentTypes: [
    { value: '', label: 'All Content' },
    { value: 'video', label: 'Videos' },
    { value: 'course', label: 'Courses' },
    { value: 'ebook', label: 'E-books' },
    { value: 'live', label: 'Live Sessions' }
  ],
  sports: [
    { value: '', label: 'All Sports' },
    { value: 'football', label: 'Football' },
    { value: 'basketball', label: 'Basketball' },
    { value: 'soccer', label: 'Soccer' },
    { value: 'tennis', label: 'Tennis' },
    { value: 'baseball', label: 'Baseball' }
  ],
  metrics: [
    { value: 'revenue', label: 'Revenue' },
    { value: 'users', label: 'Users' },
    { value: 'content', label: 'Content' },
    { value: 'orders', label: 'Orders' }
  ],
  funnelTypes: [
    { value: 'registration', label: 'User Registration' },
    { value: 'purchase', label: 'Purchase Funnel' },
    { value: 'seller_onboarding', label: 'Seller Onboarding' }
  ]
};

// Chart data formatters
export const formatChartData = {
  line: (data, xKey, yKey, label) => ({
    labels: data.map(item => item[xKey]),
    datasets: [{
      label,
      data: data.map(item => item[yKey]),
      borderColor: 'rgb(75, 192, 192)',
      backgroundColor: 'rgba(75, 192, 192, 0.2)',
      tension: 0.1
    }]
  }),

  bar: (data, xKey, yKey, label) => ({
    labels: data.map(item => item[xKey]),
    datasets: [{
      label,
      data: data.map(item => item[yKey]),
      backgroundColor: 'rgba(54, 162, 235, 0.5)',
      borderColor: 'rgba(54, 162, 235, 1)',
      borderWidth: 1
    }]
  }),

  pie: (data, labelKey, valueKey) => ({
    labels: data.map(item => item[labelKey]),
    datasets: [{
      data: data.map(item => item[valueKey]),
      backgroundColor: [
        '#FF6384',
        '#36A2EB',
        '#FFCE56',
        '#4BC0C0',
        '#9966FF',
        '#FF9F40'
      ]
    }]
  }),

  doughnut: (data, labelKey, valueKey) => ({
    labels: data.map(item => item[labelKey]),
    datasets: [{
      data: data.map(item => item[valueKey]),
      backgroundColor: [
        '#FF6384',
        '#36A2EB',
        '#FFCE56',
        '#4BC0C0',
        '#9966FF',
        '#FF9F40'
      ],
      hoverBackgroundColor: [
        '#FF6384',
        '#36A2EB',
        '#FFCE56',
        '#4BC0C0',
        '#9966FF',
        '#FF9F40'
      ]
    }]
  }),

  multiLine: (data, xKey, yKeys, labels) => ({
    labels: data.map(item => item[xKey]),
    datasets: yKeys.map((yKey, index) => ({
      label: labels[index],
      data: data.map(item => item[yKey]),
      borderColor: [
        'rgb(75, 192, 192)',
        'rgb(255, 99, 132)',
        'rgb(54, 162, 235)',
        'rgb(255, 205, 86)'
      ][index],
      backgroundColor: [
        'rgba(75, 192, 192, 0.2)',
        'rgba(255, 99, 132, 0.2)',
        'rgba(54, 162, 235, 0.2)',
        'rgba(255, 205, 86, 0.2)'
      ][index],
      tension: 0.1
    }))
  })
};

// Analytics dashboard widgets
export const dashboardWidgets = {
  overview: {
    title: 'Overview',
    type: 'metrics',
    endpoint: 'dashboard',
    refreshInterval: 300000 // 5 minutes
  },
  revenueChart: {
    title: 'Revenue Trend',
    type: 'line',
    endpoint: 'revenue',
    chartConfig: {
      xKey: '_id',
      yKey: 'totalRevenue',
      label: 'Revenue'
    }
  },
  userGrowth: {
    title: 'User Growth',
    type: 'line',
    endpoint: 'users',
    chartConfig: {
      xKey: '_id',
      yKey: 'count',
      label: 'New Users'
    }
  },
  contentByType: {
    title: 'Content by Type',
    type: 'pie',
    endpoint: 'content',
    chartConfig: {
      labelKey: '_id',
      valueKey: 'count'
    }
  },
  salesFunnel: {
    title: 'Sales Conversion',
    type: 'funnel',
    endpoint: 'conversion',
    params: { funnelType: 'purchase' }
  },
  topPerformers: {
    title: 'Top Performers',
    type: 'table',
    endpoint: 'content',
    params: { sortBy: 'revenue', limit: 10 }
  }
};

// Real-time analytics helpers
export const realTimeHelpers = {
  startPolling: (callback, interval = 30000) => {
    const poll = async () => {
      try {
        const data = await getRealTimeAnalytics();
        callback(data);
      } catch (error) {
        console.error('Real-time analytics polling error:', error);
      }
    };

    poll(); // Initial call
    return setInterval(poll, interval);
  },

  stopPolling: (intervalId) => {
    if (intervalId) {
      clearInterval(intervalId);
    }
  }
};

// Analytics export helpers
export const exportHelpers = {
  downloadCSV: (data, filename) => {
    const csv = convertToCSV(data);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
  },

  downloadJSON: (data, filename) => {
    const json = JSON.stringify(data, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
  }
};

// Helper function to convert data to CSV
const convertToCSV = (data) => {
  if (!data || data.length === 0) return '';

  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row =>
      headers.map(header => {
        const value = row[header];
        return typeof value === 'string' ? `"${value}"` : value;
      }).join(',')
    )
  ].join('\n');

  return csvContent;
};

// Comprehensive analytics service
export const getComprehensiveAnalytics = async (period = '30d') => {
  try {
    const [
      dashboard,
      users,
      content,
      revenue,
      sales,
      traffic,
      conversion
    ] = await Promise.all([
      getDashboardAnalytics(period),
      getUserAnalytics({ period }),
      getContentAnalytics({ period }),
      getRevenueAnalytics({ period }),
      getSalesAnalytics({ period }),
      getTrafficAnalytics({ period }),
      getConversionAnalytics({ period, funnelType: 'purchase' })
    ]);

    return {
      dashboard: dashboard.data,
      users: users.data,
      content: content.data,
      revenue: revenue.data,
      sales: sales.data,
      traffic: traffic.data,
      conversion: conversion.data,
      generatedAt: new Date()
    };
  } catch (error) {
    throw error;
  }
};

export default {
  getDashboardAnalytics,
  getUserAnalytics,
  getContentAnalytics,
  getRevenueAnalytics,
  getSalesAnalytics,
  getTrafficAnalytics,
  getConversionAnalytics,
  getRealTimeAnalytics,
  getComprehensiveAnalytics,
  analyticsConfigs,
  formatChartData,
  dashboardWidgets,
  realTimeHelpers,
  exportHelpers
};
