# XOSportsHub Admin Panel API Documentation

## Overview

This document provides comprehensive documentation for the XOSportsHub Admin Panel APIs. The admin panel has been transformed from using static data to a fully functional backend-integrated system with REST APIs, Redux state management, and comprehensive CRUD operations.

## Architecture

### Backend Structure
```
Backend/
├── routes/admin/
│   ├── dashboard.js      # Dashboard statistics and analytics
│   ├── users.js          # User management operations
│   ├── content.js        # Content management and approval
│   ├── bids.js           # Bid and auction management
│   ├── cms.js            # CMS pages management
│   ├── reports.js        # Reports and analytics
│   ├── settings.js       # Platform settings
│   └── analytics.js      # Advanced analytics
├── controllers/admin/
│   ├── dashboard.js      # Dashboard controller logic
│   ├── users.js          # User management logic
│   └── content.js        # Content management logic
└── postman/
    └── XOSportsHub_Admin_APIs.postman_collection.json
```

### Frontend Structure
```
Frontend/src/
├── services/admin/
│   ├── adminDashboardService.js  # Dashboard API calls
│   ├── adminUserService.js       # User management API calls
│   └── adminContentService.js    # Content management API calls
├── redux/slices/
│   ├── adminDashboardSlice.js    # Redux state management
│   └── adminDashboardThunks.js   # Async thunk actions
```

## Authentication & Authorization

All admin APIs require:
- **Authentication**: Bearer token in Authorization header
- **Authorization**: Admin role verification
- **Rate Limiting**: Applied to prevent abuse

### Headers Required
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

## API Endpoints

### Dashboard APIs

#### GET /api/admin/dashboard/stats
Get comprehensive dashboard statistics including users, content, revenue, and system metrics.

**Response:**
```json
{
  "success": true,
  "data": {
    "users": {
      "total": 1336,
      "buyers": 1247,
      "sellers": 89,
      "active": 1156,
      "newThisMonth": 45,
      "pendingVerifications": 12
    },
    "content": {
      "total": 456,
      "published": 389,
      "pending": 23,
      "draft": 44,
      "newThisMonth": 67
    },
    "revenue": {
      "total": 125450.00,
      "platformFees": 18817.50,
      "sellerEarnings": 106632.50,
      "monthly": 12450.00,
      "monthlyPlatformFees": 1867.50
    }
  }
}
```

#### GET /api/admin/dashboard/activity
Get recent platform activity with pagination.

**Query Parameters:**
- `limit` (optional): Number of activities to return (default: 20)

#### GET /api/admin/dashboard/pending-approvals
Get all pending approvals for content and seller verifications.

#### GET /api/admin/dashboard/analytics
Get analytics data for charts and graphs.

**Query Parameters:**
- `period` (optional): Time period (7d, 30d, 6m, 1y, default: 6m)

### User Management APIs

#### GET /api/admin/users
Get all users with advanced filtering, sorting, and pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `search` (optional): Search in name and email
- `role` (optional): Filter by role (buyer, seller, admin)
- `status` (optional): Filter by status (active, inactive, suspended)
- `sortBy` (optional): Sort field (default: createdAt)
- `sortOrder` (optional): Sort order (asc, desc, default: desc)
- `dateFrom` (optional): Filter from date
- `dateTo` (optional): Filter to date

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "_id": "user_id",
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "role": "buyer",
        "status": "active",
        "isVerified": true,
        "createdAt": "2024-01-15T10:30:00Z",
        "totalPurchases": 12,
        "totalSpent": 450.00
      }
    ],
    "pagination": {
      "current": 1,
      "pages": 10,
      "total": 100,
      "limit": 10
    }
  }
}
```

#### GET /api/admin/users/:id
Get detailed user information including statistics and activity.

#### POST /api/admin/users
Create a new user.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "mobile": "+1234567890",
  "role": "buyer"
}
```

#### PUT /api/admin/users/:id
Update user information.

#### DELETE /api/admin/users/:id
Delete user (soft delete if has associated data).

#### POST /api/admin/users/bulk-update
Bulk update multiple users.

**Request Body:**
```json
{
  "userIds": ["user_id_1", "user_id_2"],
  "action": "activate",
  "data": {}
}
```

**Available Actions:**
- `activate`: Set status to active
- `deactivate`: Set status to inactive
- `verify`: Set isVerified to true
- `unverify`: Set isVerified to false
- `update_role`: Update user role (requires data.role)

#### POST /api/admin/users/bulk-delete
Bulk delete multiple users.

#### PUT /api/admin/users/:id/toggle-status
Toggle user status between active and inactive.

#### PUT /api/admin/users/:id/verify
Verify a user.

#### PUT /api/admin/users/:id/suspend
Suspend a user with reason.

#### PUT /api/admin/users/:id/unsuspend
Remove suspension from a user.

#### GET /api/admin/users/:id/activity
Get user activity history with pagination.

#### GET /api/admin/users/stats
Get user statistics and metrics.

#### GET /api/admin/users/export
Export users data in JSON or CSV format.

### Content Management APIs

#### GET /api/admin/content
Get all content with filtering, sorting, and pagination.

**Query Parameters:**
- `page`, `limit`: Pagination
- `search`: Search in title and description
- `status`: Filter by status (Draft, Under Review, Published, Rejected, Archived)
- `contentType`: Filter by content type
- `sport`: Filter by sport
- `seller`: Filter by seller ID
- `sortBy`, `sortOrder`: Sorting options
- `dateFrom`, `dateTo`: Date range filtering

#### GET /api/admin/content/:id
Get detailed content information including statistics and reviews.

#### PUT /api/admin/content/:id/approve
Approve content for publication.

**Request Body:**
```json
{
  "approvalNotes": "Content meets all quality standards"
}
```

#### PUT /api/admin/content/:id/reject
Reject content with reason.

**Request Body:**
```json
{
  "reason": "Poor quality",
  "rejectionNotes": "Content does not meet minimum standards"
}
```

#### DELETE /api/admin/content/:id
Delete content (soft delete if has orders).

#### POST /api/admin/content/bulk-approve
Bulk approve multiple content items.

#### POST /api/admin/content/bulk-reject
Bulk reject multiple content items.

#### POST /api/admin/content/bulk-delete
Bulk delete multiple content items.

#### PUT /api/admin/content/:id/status
Update content status.

#### PUT /api/admin/content/:id/moderate
Moderate content (flag, unflag, restrict, unrestrict).

#### PUT /api/admin/content/:id/feature
Feature content on the platform.

#### PUT /api/admin/content/:id/unfeature
Remove featured status from content.

#### GET /api/admin/content/:id/reviews
Get reviews for specific content.

#### GET /api/admin/content/stats
Get content statistics and metrics.

#### GET /api/admin/content/export
Export content data.

## Error Handling

All APIs return consistent error responses:

```json
{
  "success": false,
  "error": "Error message",
  "details": ["Detailed error information"]
}
```

### Common HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `429`: Too Many Requests (rate limited)
- `500`: Internal Server Error

## Rate Limiting

- **General APIs**: 100 requests per 15 minutes per IP
- **Bulk Operations**: 10 requests per 15 minutes per IP
- **Export APIs**: 5 requests per 15 minutes per IP

## Validation

All input data is validated using express-validator:
- Required fields validation
- Data type validation
- Format validation (email, phone, etc.)
- Length constraints
- Enum value validation

## Security Features

1. **Authentication**: JWT token verification
2. **Authorization**: Role-based access control
3. **Input Validation**: Comprehensive validation on all inputs
4. **Rate Limiting**: Protection against abuse
5. **CORS**: Configured for admin domain only
6. **Helmet**: Security headers
7. **Data Sanitization**: XSS protection

## Testing

Use the provided Postman collection:
`Backend/postman/XOSportsHub_Admin_APIs.postman_collection.json`

### Environment Variables
Set these variables in Postman:
- `base_url`: http://localhost:5000/api
- `admin_token`: Your admin JWT token

## Frontend Integration

The frontend uses Redux Toolkit for state management with async thunks for API calls:

### Key Features
1. **Async Thunks**: Handle API calls with loading states
2. **Error Handling**: Comprehensive error management
3. **Caching**: Intelligent data caching
4. **Optimistic Updates**: UI updates before API confirmation
5. **Pagination**: Built-in pagination support
6. **Filtering**: Advanced filtering capabilities

### Usage Example
```javascript
import { useDispatch, useSelector } from 'react-redux';
import { fetchUsers, selectUsers, selectLoading } from '../redux/slices/adminDashboardSlice';

const UserManagement = () => {
  const dispatch = useDispatch();
  const users = useSelector(selectUsers);
  const loading = useSelector(state => state.adminDashboard.loading.users);

  useEffect(() => {
    dispatch(fetchUsers({ page: 1, limit: 10 }));
  }, [dispatch]);

  // Component logic
};
```
