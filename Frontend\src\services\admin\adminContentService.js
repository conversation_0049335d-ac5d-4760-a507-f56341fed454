import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/content`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Get all content with filtering and pagination
export const getAllContent = async (params = {}) => {
  try {
    const response = await api.get('/', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get content by ID
export const getContentById = async (id) => {
  try {
    const response = await api.get(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update content (admin can update any content)
export const updateContent = async (id, contentData) => {
  try {
    // Use the general content API endpoint which allows admin updates
    const response = await fetch(`${import.meta.env.VITE_API_URL}/api/content/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      },
      body: JSON.stringify(contentData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw errorData;
    }

    return await response.json();
  } catch (error) {
    throw error.response?.data || error.message || error;
  }
};

// Approve content
export const approveContent = async (id, approvalNotes = '') => {
  try {
    const response = await api.put(`/${id}/approve`, { approvalNotes });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Reject content
export const rejectContent = async (id, reason, rejectionNotes = '') => {
  try {
    const response = await api.put(`/${id}/reject`, { reason, rejectionNotes });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Delete content
export const deleteContent = async (id) => {
  try {
    const response = await api.delete(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk approve content
export const bulkApproveContent = async (contentIds) => {
  try {
    const response = await api.post('/bulk-approve', { contentIds });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk reject content
export const bulkRejectContent = async (contentIds, reason) => {
  try {
    const response = await api.post('/bulk-reject', { contentIds, reason });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk delete content
export const bulkDeleteContent = async (contentIds) => {
  try {
    const response = await api.post('/bulk-delete', { contentIds });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update content status
export const updateContentStatus = async (id, status) => {
  try {
    const response = await api.put(`/${id}/status`, { status });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Moderate content
export const moderateContent = async (id, action, reason) => {
  try {
    const response = await api.put(`/${id}/moderate`, { action, reason });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Feature content
export const featureContent = async (id) => {
  try {
    const response = await api.put(`/${id}/feature`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Unfeature content
export const unfeatureContent = async (id) => {
  try {
    const response = await api.put(`/${id}/unfeature`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get content reviews
export const getContentReviews = async (id, params = {}) => {
  try {
    const response = await api.get(`/${id}/reviews`, { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get content statistics
export const getContentStats = async () => {
  try {
    const response = await api.get('/stats');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Export content
export const exportContent = async (params = {}) => {
  try {
    const response = await api.get('/export', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Content moderation actions
export const contentModerationActions = {
  flag: (id, reason) => moderateContent(id, 'flag', reason),
  unflag: (id) => moderateContent(id, 'unflag'),
  restrict: (id, reason) => moderateContent(id, 'restrict', reason),
  unrestrict: (id) => moderateContent(id, 'unrestrict'),
};

// Content status actions
export const contentStatusActions = {
  publish: (id) => updateContentStatus(id, 'Published'),
  unpublish: (id) => updateContentStatus(id, 'Draft'),
  archive: (id) => updateContentStatus(id, 'Archived'),
  review: (id) => updateContentStatus(id, 'Under Review'),
};

export default {
  getAllContent,
  getContentById,
  updateContent,
  approveContent,
  rejectContent,
  deleteContent,
  bulkApproveContent,
  bulkRejectContent,
  bulkDeleteContent,
  updateContentStatus,
  moderateContent,
  featureContent,
  unfeatureContent,
  getContentReviews,
  getContentStats,
  exportContent,
  contentModerationActions,
  contentStatusActions
};
