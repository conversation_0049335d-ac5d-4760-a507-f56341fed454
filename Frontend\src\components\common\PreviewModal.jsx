import React, { useState, useEffect, useCallback } from 'react';
import { FaTimes, FaExpand, FaCompress } from 'react-icons/fa';
import DocumentViewer from './DocumentViewer';
import useModalScrollLock from '../../hooks/useModalScrollLock';
import '../../styles/PreviewModal.css';

const PreviewModal = ({ 
  isOpen, 
  onClose, 
  fileUrl, 
  fileName = '', 
  title = 'Preview', 
  contentType = 'document',
  className = '' 
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Use modal scroll lock hook
  useModalScrollLock(isOpen);

  // Determine if content is video
  const isVideo = useCallback(() => {
    if (contentType?.toLowerCase() === 'video') return true;
    if (!fileUrl) return false;

    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
    const extension = fileUrl.split('.').pop()?.toLowerCase();
    return videoExtensions.includes(extension);
  }, [contentType, fileUrl]);

  // Reset loading state when modal opens/closes or content changes
  useEffect(() => {
    if (isOpen && fileUrl) {
      setIsLoading(true);
      setHasError(false);
      // For documents, set a timeout to hide loading after a reasonable time
      // since DocumentViewer manages its own loading state
      if (!isVideo()) {
        const timer = setTimeout(() => {
          setIsLoading(false);
        }, 1000); // Give DocumentViewer 1 second to load
        return () => clearTimeout(timer);
      }
    }
  }, [isOpen, fileUrl, isVideo]);

  // Handle ESC key to close modal
  const handleKeyDown = useCallback((event) => {
    if (event.key === 'Escape') {
      onClose();
    }
  }, [onClose]);

  // Add/remove event listeners
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    } else {
      document.removeEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, handleKeyDown]);

  // Handle overlay click to close
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Handle content load
  const handleContentLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  // Handle content error
  const handleContentError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  if (!isOpen) return null;

  return (
    <div 
      className={`preview-modal-overlay ${className}`} 
      onClick={handleOverlayClick}
    >
      <div className={`preview-modal ${isFullscreen ? 'preview-modal--fullscreen' : ''}`}>
        {/* Modal Header */}
        <div className="preview-modal__header">
          <div className="preview-modal__title-section">
            <h3 className="preview-modal__title">{title}</h3>
            {fileName && (
              <span className="preview-modal__filename">{fileName}</span>
            )}
          </div>
          <div className="preview-modal__controls">
            <button
              className="preview-modal__control-btn"
              onClick={toggleFullscreen}
              title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
            >
              {isFullscreen ? <FaCompress /> : <FaExpand />}
            </button>
            <button
              className="preview-modal__close-btn"
              onClick={onClose}
              title="Close Preview"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        {/* Modal Content */}
        <div className="preview-modal__content">
          {isLoading && (
            <div className="preview-modal__loading">
              <div className="preview-modal__spinner"></div>
              <p>Loading preview...</p>
            </div>
          )}

          {hasError && (
            <div className="preview-modal__error">
              <p>Unable to load preview</p>
              <button className="btn-outline" onClick={onClose}>
                Close
              </button>
            </div>
          )}

          {!hasError && (
            <>
              {isVideo() ? (
                <div className="preview-modal__video-container">
                  <video
                    className="preview-modal__video"
                    controls
                    autoPlay={false}
                    controlsList="nodownload noremoteplayback"
                    disablePictureInPicture
                    onLoadedData={handleContentLoad}
                    onError={handleContentError}
                  >
                    <source src={fileUrl} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
              ) : (
                <div className="preview-modal__document-container">
                  <DocumentViewer
                    fileUrl={fileUrl}
                    fileName={fileName}
                    title={title}
                    className="preview-modal__document-viewer"
                    height="100%"
                    showDownload={false}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default PreviewModal;
