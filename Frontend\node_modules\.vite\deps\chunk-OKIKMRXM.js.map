{"version": 3, "sources": ["../../jquery/dist/jquery.js"], "sourcesContent": ["/*!\n * jQuery JavaScript Library v3.7.1\n * https://jquery.com/\n *\n * Copyright OpenJS Foundation and other contributors\n * Released under the MIT license\n * https://jquery.org/license\n *\n * Date: 2023-08-28T13:37Z\n */\n( function( global, factory ) {\n\n\t\"use strict\";\n\n\tif ( typeof module === \"object\" && typeof module.exports === \"object\" ) {\n\n\t\t// For CommonJS and CommonJS-like environments where a proper `window`\n\t\t// is present, execute the factory and get jQuery.\n\t\t// For environments that do not have a `window` with a `document`\n\t\t// (such as Node.js), expose a factory as module.exports.\n\t\t// This accentuates the need for the creation of a real `window`.\n\t\t// e.g. var jQuery = require(\"jquery\")(window);\n\t\t// See ticket trac-14549 for more info.\n\t\tmodule.exports = global.document ?\n\t\t\tfactory( global, true ) :\n\t\t\tfunction( w ) {\n\t\t\t\tif ( !w.document ) {\n\t\t\t\t\tthrow new Error( \"jQuery requires a window with a document\" );\n\t\t\t\t}\n\t\t\t\treturn factory( w );\n\t\t\t};\n\t} else {\n\t\tfactory( global );\n\t}\n\n// Pass this if window is not defined yet\n} )( typeof window !== \"undefined\" ? window : this, function( window, noGlobal ) {\n\n// Edge <= 12 - 13+, Firefox <=18 - 45+, IE 10 - 11, Safari 5.1 - 9+, iOS 6 - 9.1\n// throw exceptions when non-strict code (e.g., ASP.NET 4.5) accesses strict mode\n// arguments.callee.caller (trac-13335). But as of jQuery 3.0 (2016), strict mode should be common\n// enough that all such attempts are guarded in a try block.\n\"use strict\";\n\nvar arr = [];\n\nvar getProto = Object.getPrototypeOf;\n\nvar slice = arr.slice;\n\nvar flat = arr.flat ? function( array ) {\n\treturn arr.flat.call( array );\n} : function( array ) {\n\treturn arr.concat.apply( [], array );\n};\n\n\nvar push = arr.push;\n\nvar indexOf = arr.indexOf;\n\nvar class2type = {};\n\nvar toString = class2type.toString;\n\nvar hasOwn = class2type.hasOwnProperty;\n\nvar fnToString = hasOwn.toString;\n\nvar ObjectFunctionString = fnToString.call( Object );\n\nvar support = {};\n\nvar isFunction = function isFunction( obj ) {\n\n\t\t// Support: Chrome <=57, Firefox <=52\n\t\t// In some browsers, typeof returns \"function\" for HTML <object> elements\n\t\t// (i.e., `typeof document.createElement( \"object\" ) === \"function\"`).\n\t\t// We don't want to classify *any* DOM node as a function.\n\t\t// Support: QtWeb <=3.8.5, WebKit <=534.34, wkhtmltopdf tool <=0.12.5\n\t\t// Plus for old WebKit, typeof returns \"function\" for HTML collections\n\t\t// (e.g., `typeof document.getElementsByTagName(\"div\") === \"function\"`). (gh-4756)\n\t\treturn typeof obj === \"function\" && typeof obj.nodeType !== \"number\" &&\n\t\t\ttypeof obj.item !== \"function\";\n\t};\n\n\nvar isWindow = function isWindow( obj ) {\n\t\treturn obj != null && obj === obj.window;\n\t};\n\n\nvar document = window.document;\n\n\n\n\tvar preservedScriptAttributes = {\n\t\ttype: true,\n\t\tsrc: true,\n\t\tnonce: true,\n\t\tnoModule: true\n\t};\n\n\tfunction DOMEval( code, node, doc ) {\n\t\tdoc = doc || document;\n\n\t\tvar i, val,\n\t\t\tscript = doc.createElement( \"script\" );\n\n\t\tscript.text = code;\n\t\tif ( node ) {\n\t\t\tfor ( i in preservedScriptAttributes ) {\n\n\t\t\t\t// Support: Firefox 64+, Edge 18+\n\t\t\t\t// Some browsers don't support the \"nonce\" property on scripts.\n\t\t\t\t// On the other hand, just using `getAttribute` is not enough as\n\t\t\t\t// the `nonce` attribute is reset to an empty string whenever it\n\t\t\t\t// becomes browsing-context connected.\n\t\t\t\t// See https://github.com/whatwg/html/issues/2369\n\t\t\t\t// See https://html.spec.whatwg.org/#nonce-attributes\n\t\t\t\t// The `node.getAttribute` check was added for the sake of\n\t\t\t\t// `jQuery.globalEval` so that it can fake a nonce-containing node\n\t\t\t\t// via an object.\n\t\t\t\tval = node[ i ] || node.getAttribute && node.getAttribute( i );\n\t\t\t\tif ( val ) {\n\t\t\t\t\tscript.setAttribute( i, val );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tdoc.head.appendChild( script ).parentNode.removeChild( script );\n\t}\n\n\nfunction toType( obj ) {\n\tif ( obj == null ) {\n\t\treturn obj + \"\";\n\t}\n\n\t// Support: Android <=2.3 only (functionish RegExp)\n\treturn typeof obj === \"object\" || typeof obj === \"function\" ?\n\t\tclass2type[ toString.call( obj ) ] || \"object\" :\n\t\ttypeof obj;\n}\n/* global Symbol */\n// Defining this global in .eslintrc.json would create a danger of using the global\n// unguarded in another place, it seems safer to define global only for this module\n\n\n\nvar version = \"3.7.1\",\n\n\trhtmlSuffix = /HTML$/i,\n\n\t// Define a local copy of jQuery\n\tjQuery = function( selector, context ) {\n\n\t\t// The jQuery object is actually just the init constructor 'enhanced'\n\t\t// Need init if jQuery is called (just allow error to be thrown if not included)\n\t\treturn new jQuery.fn.init( selector, context );\n\t};\n\njQuery.fn = jQuery.prototype = {\n\n\t// The current version of jQuery being used\n\tjquery: version,\n\n\tconstructor: jQuery,\n\n\t// The default length of a jQuery object is 0\n\tlength: 0,\n\n\ttoArray: function() {\n\t\treturn slice.call( this );\n\t},\n\n\t// Get the Nth element in the matched element set OR\n\t// Get the whole matched element set as a clean array\n\tget: function( num ) {\n\n\t\t// Return all the elements in a clean array\n\t\tif ( num == null ) {\n\t\t\treturn slice.call( this );\n\t\t}\n\n\t\t// Return just the one element from the set\n\t\treturn num < 0 ? this[ num + this.length ] : this[ num ];\n\t},\n\n\t// Take an array of elements and push it onto the stack\n\t// (returning the new matched element set)\n\tpushStack: function( elems ) {\n\n\t\t// Build a new jQuery matched element set\n\t\tvar ret = jQuery.merge( this.constructor(), elems );\n\n\t\t// Add the old object onto the stack (as a reference)\n\t\tret.prevObject = this;\n\n\t\t// Return the newly-formed element set\n\t\treturn ret;\n\t},\n\n\t// Execute a callback for every element in the matched set.\n\teach: function( callback ) {\n\t\treturn jQuery.each( this, callback );\n\t},\n\n\tmap: function( callback ) {\n\t\treturn this.pushStack( jQuery.map( this, function( elem, i ) {\n\t\t\treturn callback.call( elem, i, elem );\n\t\t} ) );\n\t},\n\n\tslice: function() {\n\t\treturn this.pushStack( slice.apply( this, arguments ) );\n\t},\n\n\tfirst: function() {\n\t\treturn this.eq( 0 );\n\t},\n\n\tlast: function() {\n\t\treturn this.eq( -1 );\n\t},\n\n\teven: function() {\n\t\treturn this.pushStack( jQuery.grep( this, function( _elem, i ) {\n\t\t\treturn ( i + 1 ) % 2;\n\t\t} ) );\n\t},\n\n\todd: function() {\n\t\treturn this.pushStack( jQuery.grep( this, function( _elem, i ) {\n\t\t\treturn i % 2;\n\t\t} ) );\n\t},\n\n\teq: function( i ) {\n\t\tvar len = this.length,\n\t\t\tj = +i + ( i < 0 ? len : 0 );\n\t\treturn this.pushStack( j >= 0 && j < len ? [ this[ j ] ] : [] );\n\t},\n\n\tend: function() {\n\t\treturn this.prevObject || this.constructor();\n\t},\n\n\t// For internal use only.\n\t// Behaves like an Array's method, not like a jQuery method.\n\tpush: push,\n\tsort: arr.sort,\n\tsplice: arr.splice\n};\n\njQuery.extend = jQuery.fn.extend = function() {\n\tvar options, name, src, copy, copyIsArray, clone,\n\t\ttarget = arguments[ 0 ] || {},\n\t\ti = 1,\n\t\tlength = arguments.length,\n\t\tdeep = false;\n\n\t// Handle a deep copy situation\n\tif ( typeof target === \"boolean\" ) {\n\t\tdeep = target;\n\n\t\t// Skip the boolean and the target\n\t\ttarget = arguments[ i ] || {};\n\t\ti++;\n\t}\n\n\t// Handle case when target is a string or something (possible in deep copy)\n\tif ( typeof target !== \"object\" && !isFunction( target ) ) {\n\t\ttarget = {};\n\t}\n\n\t// Extend jQuery itself if only one argument is passed\n\tif ( i === length ) {\n\t\ttarget = this;\n\t\ti--;\n\t}\n\n\tfor ( ; i < length; i++ ) {\n\n\t\t// Only deal with non-null/undefined values\n\t\tif ( ( options = arguments[ i ] ) != null ) {\n\n\t\t\t// Extend the base object\n\t\t\tfor ( name in options ) {\n\t\t\t\tcopy = options[ name ];\n\n\t\t\t\t// Prevent Object.prototype pollution\n\t\t\t\t// Prevent never-ending loop\n\t\t\t\tif ( name === \"__proto__\" || target === copy ) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Recurse if we're merging plain objects or arrays\n\t\t\t\tif ( deep && copy && ( jQuery.isPlainObject( copy ) ||\n\t\t\t\t\t( copyIsArray = Array.isArray( copy ) ) ) ) {\n\t\t\t\t\tsrc = target[ name ];\n\n\t\t\t\t\t// Ensure proper type for the source value\n\t\t\t\t\tif ( copyIsArray && !Array.isArray( src ) ) {\n\t\t\t\t\t\tclone = [];\n\t\t\t\t\t} else if ( !copyIsArray && !jQuery.isPlainObject( src ) ) {\n\t\t\t\t\t\tclone = {};\n\t\t\t\t\t} else {\n\t\t\t\t\t\tclone = src;\n\t\t\t\t\t}\n\t\t\t\t\tcopyIsArray = false;\n\n\t\t\t\t\t// Never move original objects, clone them\n\t\t\t\t\ttarget[ name ] = jQuery.extend( deep, clone, copy );\n\n\t\t\t\t// Don't bring in undefined values\n\t\t\t\t} else if ( copy !== undefined ) {\n\t\t\t\t\ttarget[ name ] = copy;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Return the modified object\n\treturn target;\n};\n\njQuery.extend( {\n\n\t// Unique for each copy of jQuery on the page\n\texpando: \"jQuery\" + ( version + Math.random() ).replace( /\\D/g, \"\" ),\n\n\t// Assume jQuery is ready without the ready module\n\tisReady: true,\n\n\terror: function( msg ) {\n\t\tthrow new Error( msg );\n\t},\n\n\tnoop: function() {},\n\n\tisPlainObject: function( obj ) {\n\t\tvar proto, Ctor;\n\n\t\t// Detect obvious negatives\n\t\t// Use toString instead of jQuery.type to catch host objects\n\t\tif ( !obj || toString.call( obj ) !== \"[object Object]\" ) {\n\t\t\treturn false;\n\t\t}\n\n\t\tproto = getProto( obj );\n\n\t\t// Objects with no prototype (e.g., `Object.create( null )`) are plain\n\t\tif ( !proto ) {\n\t\t\treturn true;\n\t\t}\n\n\t\t// Objects with prototype are plain iff they were constructed by a global Object function\n\t\tCtor = hasOwn.call( proto, \"constructor\" ) && proto.constructor;\n\t\treturn typeof Ctor === \"function\" && fnToString.call( Ctor ) === ObjectFunctionString;\n\t},\n\n\tisEmptyObject: function( obj ) {\n\t\tvar name;\n\n\t\tfor ( name in obj ) {\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t},\n\n\t// Evaluates a script in a provided context; falls back to the global one\n\t// if not specified.\n\tglobalEval: function( code, options, doc ) {\n\t\tDOMEval( code, { nonce: options && options.nonce }, doc );\n\t},\n\n\teach: function( obj, callback ) {\n\t\tvar length, i = 0;\n\n\t\tif ( isArrayLike( obj ) ) {\n\t\t\tlength = obj.length;\n\t\t\tfor ( ; i < length; i++ ) {\n\t\t\t\tif ( callback.call( obj[ i ], i, obj[ i ] ) === false ) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tfor ( i in obj ) {\n\t\t\t\tif ( callback.call( obj[ i ], i, obj[ i ] ) === false ) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn obj;\n\t},\n\n\n\t// Retrieve the text value of an array of DOM nodes\n\ttext: function( elem ) {\n\t\tvar node,\n\t\t\tret = \"\",\n\t\t\ti = 0,\n\t\t\tnodeType = elem.nodeType;\n\n\t\tif ( !nodeType ) {\n\n\t\t\t// If no nodeType, this is expected to be an array\n\t\t\twhile ( ( node = elem[ i++ ] ) ) {\n\n\t\t\t\t// Do not traverse comment nodes\n\t\t\t\tret += jQuery.text( node );\n\t\t\t}\n\t\t}\n\t\tif ( nodeType === 1 || nodeType === 11 ) {\n\t\t\treturn elem.textContent;\n\t\t}\n\t\tif ( nodeType === 9 ) {\n\t\t\treturn elem.documentElement.textContent;\n\t\t}\n\t\tif ( nodeType === 3 || nodeType === 4 ) {\n\t\t\treturn elem.nodeValue;\n\t\t}\n\n\t\t// Do not include comment or processing instruction nodes\n\n\t\treturn ret;\n\t},\n\n\t// results is for internal usage only\n\tmakeArray: function( arr, results ) {\n\t\tvar ret = results || [];\n\n\t\tif ( arr != null ) {\n\t\t\tif ( isArrayLike( Object( arr ) ) ) {\n\t\t\t\tjQuery.merge( ret,\n\t\t\t\t\ttypeof arr === \"string\" ?\n\t\t\t\t\t\t[ arr ] : arr\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tpush.call( ret, arr );\n\t\t\t}\n\t\t}\n\n\t\treturn ret;\n\t},\n\n\tinArray: function( elem, arr, i ) {\n\t\treturn arr == null ? -1 : indexOf.call( arr, elem, i );\n\t},\n\n\tisXMLDoc: function( elem ) {\n\t\tvar namespace = elem && elem.namespaceURI,\n\t\t\tdocElem = elem && ( elem.ownerDocument || elem ).documentElement;\n\n\t\t// Assume HTML when documentElement doesn't yet exist, such as inside\n\t\t// document fragments.\n\t\treturn !rhtmlSuffix.test( namespace || docElem && docElem.nodeName || \"HTML\" );\n\t},\n\n\t// Support: Android <=4.0 only, PhantomJS 1 only\n\t// push.apply(_, arraylike) throws on ancient WebKit\n\tmerge: function( first, second ) {\n\t\tvar len = +second.length,\n\t\t\tj = 0,\n\t\t\ti = first.length;\n\n\t\tfor ( ; j < len; j++ ) {\n\t\t\tfirst[ i++ ] = second[ j ];\n\t\t}\n\n\t\tfirst.length = i;\n\n\t\treturn first;\n\t},\n\n\tgrep: function( elems, callback, invert ) {\n\t\tvar callbackInverse,\n\t\t\tmatches = [],\n\t\t\ti = 0,\n\t\t\tlength = elems.length,\n\t\t\tcallbackExpect = !invert;\n\n\t\t// Go through the array, only saving the items\n\t\t// that pass the validator function\n\t\tfor ( ; i < length; i++ ) {\n\t\t\tcallbackInverse = !callback( elems[ i ], i );\n\t\t\tif ( callbackInverse !== callbackExpect ) {\n\t\t\t\tmatches.push( elems[ i ] );\n\t\t\t}\n\t\t}\n\n\t\treturn matches;\n\t},\n\n\t// arg is for internal usage only\n\tmap: function( elems, callback, arg ) {\n\t\tvar length, value,\n\t\t\ti = 0,\n\t\t\tret = [];\n\n\t\t// Go through the array, translating each of the items to their new values\n\t\tif ( isArrayLike( elems ) ) {\n\t\t\tlength = elems.length;\n\t\t\tfor ( ; i < length; i++ ) {\n\t\t\t\tvalue = callback( elems[ i ], i, arg );\n\n\t\t\t\tif ( value != null ) {\n\t\t\t\t\tret.push( value );\n\t\t\t\t}\n\t\t\t}\n\n\t\t// Go through every key on the object,\n\t\t} else {\n\t\t\tfor ( i in elems ) {\n\t\t\t\tvalue = callback( elems[ i ], i, arg );\n\n\t\t\t\tif ( value != null ) {\n\t\t\t\t\tret.push( value );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Flatten any nested arrays\n\t\treturn flat( ret );\n\t},\n\n\t// A global GUID counter for objects\n\tguid: 1,\n\n\t// jQuery.support is not used in Core but other projects attach their\n\t// properties to it so it needs to exist.\n\tsupport: support\n} );\n\nif ( typeof Symbol === \"function\" ) {\n\tjQuery.fn[ Symbol.iterator ] = arr[ Symbol.iterator ];\n}\n\n// Populate the class2type map\njQuery.each( \"Boolean Number String Function Array Date RegExp Object Error Symbol\".split( \" \" ),\n\tfunction( _i, name ) {\n\t\tclass2type[ \"[object \" + name + \"]\" ] = name.toLowerCase();\n\t} );\n\nfunction isArrayLike( obj ) {\n\n\t// Support: real iOS 8.2 only (not reproducible in simulator)\n\t// `in` check used to prevent JIT error (gh-2145)\n\t// hasOwn isn't used here due to false negatives\n\t// regarding Nodelist length in IE\n\tvar length = !!obj && \"length\" in obj && obj.length,\n\t\ttype = toType( obj );\n\n\tif ( isFunction( obj ) || isWindow( obj ) ) {\n\t\treturn false;\n\t}\n\n\treturn type === \"array\" || length === 0 ||\n\t\ttypeof length === \"number\" && length > 0 && ( length - 1 ) in obj;\n}\n\n\nfunction nodeName( elem, name ) {\n\n\treturn elem.nodeName && elem.nodeName.toLowerCase() === name.toLowerCase();\n\n}\nvar pop = arr.pop;\n\n\nvar sort = arr.sort;\n\n\nvar splice = arr.splice;\n\n\nvar whitespace = \"[\\\\x20\\\\t\\\\r\\\\n\\\\f]\";\n\n\nvar rtrimCSS = new RegExp(\n\t\"^\" + whitespace + \"+|((?:^|[^\\\\\\\\])(?:\\\\\\\\.)*)\" + whitespace + \"+$\",\n\t\"g\"\n);\n\n\n\n\n// Note: an element does not contain itself\njQuery.contains = function( a, b ) {\n\tvar bup = b && b.parentNode;\n\n\treturn a === bup || !!( bup && bup.nodeType === 1 && (\n\n\t\t// Support: IE 9 - 11+\n\t\t// IE doesn't have `contains` on SVG.\n\t\ta.contains ?\n\t\t\ta.contains( bup ) :\n\t\t\ta.compareDocumentPosition && a.compareDocumentPosition( bup ) & 16\n\t) );\n};\n\n\n\n\n// CSS string/identifier serialization\n// https://drafts.csswg.org/cssom/#common-serializing-idioms\nvar rcssescape = /([\\0-\\x1f\\x7f]|^-?\\d)|^-$|[^\\x80-\\uFFFF\\w-]/g;\n\nfunction fcssescape( ch, asCodePoint ) {\n\tif ( asCodePoint ) {\n\n\t\t// U+0000 NULL becomes U+FFFD REPLACEMENT CHARACTER\n\t\tif ( ch === \"\\0\" ) {\n\t\t\treturn \"\\uFFFD\";\n\t\t}\n\n\t\t// Control characters and (dependent upon position) numbers get escaped as code points\n\t\treturn ch.slice( 0, -1 ) + \"\\\\\" + ch.charCodeAt( ch.length - 1 ).toString( 16 ) + \" \";\n\t}\n\n\t// Other potentially-special ASCII characters get backslash-escaped\n\treturn \"\\\\\" + ch;\n}\n\njQuery.escapeSelector = function( sel ) {\n\treturn ( sel + \"\" ).replace( rcssescape, fcssescape );\n};\n\n\n\n\nvar preferredDoc = document,\n\tpushNative = push;\n\n( function() {\n\nvar i,\n\tExpr,\n\toutermostContext,\n\tsortInput,\n\thasDuplicate,\n\tpush = pushNative,\n\n\t// Local document vars\n\tdocument,\n\tdocumentElement,\n\tdocumentIsHTML,\n\trbuggyQSA,\n\tmatches,\n\n\t// Instance-specific data\n\texpando = jQuery.expando,\n\tdirruns = 0,\n\tdone = 0,\n\tclassCache = createCache(),\n\ttokenCache = createCache(),\n\tcompilerCache = createCache(),\n\tnonnativeSelectorCache = createCache(),\n\tsortOrder = function( a, b ) {\n\t\tif ( a === b ) {\n\t\t\thasDuplicate = true;\n\t\t}\n\t\treturn 0;\n\t},\n\n\tbooleans = \"checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|\" +\n\t\t\"loop|multiple|open|readonly|required|scoped\",\n\n\t// Regular expressions\n\n\t// https://www.w3.org/TR/css-syntax-3/#ident-token-diagram\n\tidentifier = \"(?:\\\\\\\\[\\\\da-fA-F]{1,6}\" + whitespace +\n\t\t\"?|\\\\\\\\[^\\\\r\\\\n\\\\f]|[\\\\w-]|[^\\0-\\\\x7f])+\",\n\n\t// Attribute selectors: https://www.w3.org/TR/selectors/#attribute-selectors\n\tattributes = \"\\\\[\" + whitespace + \"*(\" + identifier + \")(?:\" + whitespace +\n\n\t\t// Operator (capture 2)\n\t\t\"*([*^$|!~]?=)\" + whitespace +\n\n\t\t// \"Attribute values must be CSS identifiers [capture 5] or strings [capture 3 or capture 4]\"\n\t\t\"*(?:'((?:\\\\\\\\.|[^\\\\\\\\'])*)'|\\\"((?:\\\\\\\\.|[^\\\\\\\\\\\"])*)\\\"|(\" + identifier + \"))|)\" +\n\t\twhitespace + \"*\\\\]\",\n\n\tpseudos = \":(\" + identifier + \")(?:\\\\((\" +\n\n\t\t// To reduce the number of selectors needing tokenize in the preFilter, prefer arguments:\n\t\t// 1. quoted (capture 3; capture 4 or capture 5)\n\t\t\"('((?:\\\\\\\\.|[^\\\\\\\\'])*)'|\\\"((?:\\\\\\\\.|[^\\\\\\\\\\\"])*)\\\")|\" +\n\n\t\t// 2. simple (capture 6)\n\t\t\"((?:\\\\\\\\.|[^\\\\\\\\()[\\\\]]|\" + attributes + \")*)|\" +\n\n\t\t// 3. anything else (capture 2)\n\t\t\".*\" +\n\t\t\")\\\\)|)\",\n\n\t// Leading and non-escaped trailing whitespace, capturing some non-whitespace characters preceding the latter\n\trwhitespace = new RegExp( whitespace + \"+\", \"g\" ),\n\n\trcomma = new RegExp( \"^\" + whitespace + \"*,\" + whitespace + \"*\" ),\n\trleadingCombinator = new RegExp( \"^\" + whitespace + \"*([>+~]|\" + whitespace + \")\" +\n\t\twhitespace + \"*\" ),\n\trdescend = new RegExp( whitespace + \"|>\" ),\n\n\trpseudo = new RegExp( pseudos ),\n\tridentifier = new RegExp( \"^\" + identifier + \"$\" ),\n\n\tmatchExpr = {\n\t\tID: new RegExp( \"^#(\" + identifier + \")\" ),\n\t\tCLASS: new RegExp( \"^\\\\.(\" + identifier + \")\" ),\n\t\tTAG: new RegExp( \"^(\" + identifier + \"|[*])\" ),\n\t\tATTR: new RegExp( \"^\" + attributes ),\n\t\tPSEUDO: new RegExp( \"^\" + pseudos ),\n\t\tCHILD: new RegExp(\n\t\t\t\"^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\\\(\" +\n\t\t\t\twhitespace + \"*(even|odd|(([+-]|)(\\\\d*)n|)\" + whitespace + \"*(?:([+-]|)\" +\n\t\t\t\twhitespace + \"*(\\\\d+)|))\" + whitespace + \"*\\\\)|)\", \"i\" ),\n\t\tbool: new RegExp( \"^(?:\" + booleans + \")$\", \"i\" ),\n\n\t\t// For use in libraries implementing .is()\n\t\t// We use this for POS matching in `select`\n\t\tneedsContext: new RegExp( \"^\" + whitespace +\n\t\t\t\"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\\\(\" + whitespace +\n\t\t\t\"*((?:-\\\\d)?\\\\d*)\" + whitespace + \"*\\\\)|)(?=[^-]|$)\", \"i\" )\n\t},\n\n\trinputs = /^(?:input|select|textarea|button)$/i,\n\trheader = /^h\\d$/i,\n\n\t// Easily-parseable/retrievable ID or TAG or CLASS selectors\n\trquickExpr = /^(?:#([\\w-]+)|(\\w+)|\\.([\\w-]+))$/,\n\n\trsibling = /[+~]/,\n\n\t// CSS escapes\n\t// https://www.w3.org/TR/CSS21/syndata.html#escaped-characters\n\trunescape = new RegExp( \"\\\\\\\\[\\\\da-fA-F]{1,6}\" + whitespace +\n\t\t\"?|\\\\\\\\([^\\\\r\\\\n\\\\f])\", \"g\" ),\n\tfunescape = function( escape, nonHex ) {\n\t\tvar high = \"0x\" + escape.slice( 1 ) - 0x10000;\n\n\t\tif ( nonHex ) {\n\n\t\t\t// Strip the backslash prefix from a non-hex escape sequence\n\t\t\treturn nonHex;\n\t\t}\n\n\t\t// Replace a hexadecimal escape sequence with the encoded Unicode code point\n\t\t// Support: IE <=11+\n\t\t// For values outside the Basic Multilingual Plane (BMP), manually construct a\n\t\t// surrogate pair\n\t\treturn high < 0 ?\n\t\t\tString.fromCharCode( high + 0x10000 ) :\n\t\t\tString.fromCharCode( high >> 10 | 0xD800, high & 0x3FF | 0xDC00 );\n\t},\n\n\t// Used for iframes; see `setDocument`.\n\t// Support: IE 9 - 11+, Edge 12 - 18+\n\t// Removing the function wrapper causes a \"Permission Denied\"\n\t// error in IE/Edge.\n\tunloadHandler = function() {\n\t\tsetDocument();\n\t},\n\n\tinDisabledFieldset = addCombinator(\n\t\tfunction( elem ) {\n\t\t\treturn elem.disabled === true && nodeName( elem, \"fieldset\" );\n\t\t},\n\t\t{ dir: \"parentNode\", next: \"legend\" }\n\t);\n\n// Support: IE <=9 only\n// Accessing document.activeElement can throw unexpectedly\n// https://bugs.jquery.com/ticket/13393\nfunction safeActiveElement() {\n\ttry {\n\t\treturn document.activeElement;\n\t} catch ( err ) { }\n}\n\n// Optimize for push.apply( _, NodeList )\ntry {\n\tpush.apply(\n\t\t( arr = slice.call( preferredDoc.childNodes ) ),\n\t\tpreferredDoc.childNodes\n\t);\n\n\t// Support: Android <=4.0\n\t// Detect silently failing push.apply\n\t// eslint-disable-next-line no-unused-expressions\n\tarr[ preferredDoc.childNodes.length ].nodeType;\n} catch ( e ) {\n\tpush = {\n\t\tapply: function( target, els ) {\n\t\t\tpushNative.apply( target, slice.call( els ) );\n\t\t},\n\t\tcall: function( target ) {\n\t\t\tpushNative.apply( target, slice.call( arguments, 1 ) );\n\t\t}\n\t};\n}\n\nfunction find( selector, context, results, seed ) {\n\tvar m, i, elem, nid, match, groups, newSelector,\n\t\tnewContext = context && context.ownerDocument,\n\n\t\t// nodeType defaults to 9, since context defaults to document\n\t\tnodeType = context ? context.nodeType : 9;\n\n\tresults = results || [];\n\n\t// Return early from calls with invalid selector or context\n\tif ( typeof selector !== \"string\" || !selector ||\n\t\tnodeType !== 1 && nodeType !== 9 && nodeType !== 11 ) {\n\n\t\treturn results;\n\t}\n\n\t// Try to shortcut find operations (as opposed to filters) in HTML documents\n\tif ( !seed ) {\n\t\tsetDocument( context );\n\t\tcontext = context || document;\n\n\t\tif ( documentIsHTML ) {\n\n\t\t\t// If the selector is sufficiently simple, try using a \"get*By*\" DOM method\n\t\t\t// (excepting DocumentFragment context, where the methods don't exist)\n\t\t\tif ( nodeType !== 11 && ( match = rquickExpr.exec( selector ) ) ) {\n\n\t\t\t\t// ID selector\n\t\t\t\tif ( ( m = match[ 1 ] ) ) {\n\n\t\t\t\t\t// Document context\n\t\t\t\t\tif ( nodeType === 9 ) {\n\t\t\t\t\t\tif ( ( elem = context.getElementById( m ) ) ) {\n\n\t\t\t\t\t\t\t// Support: IE 9 only\n\t\t\t\t\t\t\t// getElementById can match elements by name instead of ID\n\t\t\t\t\t\t\tif ( elem.id === m ) {\n\t\t\t\t\t\t\t\tpush.call( results, elem );\n\t\t\t\t\t\t\t\treturn results;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\treturn results;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t// Element context\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\t// Support: IE 9 only\n\t\t\t\t\t\t// getElementById can match elements by name instead of ID\n\t\t\t\t\t\tif ( newContext && ( elem = newContext.getElementById( m ) ) &&\n\t\t\t\t\t\t\tfind.contains( context, elem ) &&\n\t\t\t\t\t\t\telem.id === m ) {\n\n\t\t\t\t\t\t\tpush.call( results, elem );\n\t\t\t\t\t\t\treturn results;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t// Type selector\n\t\t\t\t} else if ( match[ 2 ] ) {\n\t\t\t\t\tpush.apply( results, context.getElementsByTagName( selector ) );\n\t\t\t\t\treturn results;\n\n\t\t\t\t// Class selector\n\t\t\t\t} else if ( ( m = match[ 3 ] ) && context.getElementsByClassName ) {\n\t\t\t\t\tpush.apply( results, context.getElementsByClassName( m ) );\n\t\t\t\t\treturn results;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Take advantage of querySelectorAll\n\t\t\tif ( !nonnativeSelectorCache[ selector + \" \" ] &&\n\t\t\t\t( !rbuggyQSA || !rbuggyQSA.test( selector ) ) ) {\n\n\t\t\t\tnewSelector = selector;\n\t\t\t\tnewContext = context;\n\n\t\t\t\t// qSA considers elements outside a scoping root when evaluating child or\n\t\t\t\t// descendant combinators, which is not what we want.\n\t\t\t\t// In such cases, we work around the behavior by prefixing every selector in the\n\t\t\t\t// list with an ID selector referencing the scope context.\n\t\t\t\t// The technique has to be used as well when a leading combinator is used\n\t\t\t\t// as such selectors are not recognized by querySelectorAll.\n\t\t\t\t// Thanks to Andrew Dupont for this technique.\n\t\t\t\tif ( nodeType === 1 &&\n\t\t\t\t\t( rdescend.test( selector ) || rleadingCombinator.test( selector ) ) ) {\n\n\t\t\t\t\t// Expand context for sibling selectors\n\t\t\t\t\tnewContext = rsibling.test( selector ) && testContext( context.parentNode ) ||\n\t\t\t\t\t\tcontext;\n\n\t\t\t\t\t// We can use :scope instead of the ID hack if the browser\n\t\t\t\t\t// supports it & if we're not changing the context.\n\t\t\t\t\t// Support: IE 11+, Edge 17 - 18+\n\t\t\t\t\t// IE/Edge sometimes throw a \"Permission denied\" error when\n\t\t\t\t\t// strict-comparing two documents; shallow comparisons work.\n\t\t\t\t\t// eslint-disable-next-line eqeqeq\n\t\t\t\t\tif ( newContext != context || !support.scope ) {\n\n\t\t\t\t\t\t// Capture the context ID, setting it first if necessary\n\t\t\t\t\t\tif ( ( nid = context.getAttribute( \"id\" ) ) ) {\n\t\t\t\t\t\t\tnid = jQuery.escapeSelector( nid );\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tcontext.setAttribute( \"id\", ( nid = expando ) );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// Prefix every selector in the list\n\t\t\t\t\tgroups = tokenize( selector );\n\t\t\t\t\ti = groups.length;\n\t\t\t\t\twhile ( i-- ) {\n\t\t\t\t\t\tgroups[ i ] = ( nid ? \"#\" + nid : \":scope\" ) + \" \" +\n\t\t\t\t\t\t\ttoSelector( groups[ i ] );\n\t\t\t\t\t}\n\t\t\t\t\tnewSelector = groups.join( \",\" );\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tpush.apply( results,\n\t\t\t\t\t\tnewContext.querySelectorAll( newSelector )\n\t\t\t\t\t);\n\t\t\t\t\treturn results;\n\t\t\t\t} catch ( qsaError ) {\n\t\t\t\t\tnonnativeSelectorCache( selector, true );\n\t\t\t\t} finally {\n\t\t\t\t\tif ( nid === expando ) {\n\t\t\t\t\t\tcontext.removeAttribute( \"id\" );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// All others\n\treturn select( selector.replace( rtrimCSS, \"$1\" ), context, results, seed );\n}\n\n/**\n * Create key-value caches of limited size\n * @returns {function(string, object)} Returns the Object data after storing it on itself with\n *\tproperty name the (space-suffixed) string and (if the cache is larger than Expr.cacheLength)\n *\tdeleting the oldest entry\n */\nfunction createCache() {\n\tvar keys = [];\n\n\tfunction cache( key, value ) {\n\n\t\t// Use (key + \" \") to avoid collision with native prototype properties\n\t\t// (see https://github.com/jquery/sizzle/issues/157)\n\t\tif ( keys.push( key + \" \" ) > Expr.cacheLength ) {\n\n\t\t\t// Only keep the most recent entries\n\t\t\tdelete cache[ keys.shift() ];\n\t\t}\n\t\treturn ( cache[ key + \" \" ] = value );\n\t}\n\treturn cache;\n}\n\n/**\n * Mark a function for special use by jQuery selector module\n * @param {Function} fn The function to mark\n */\nfunction markFunction( fn ) {\n\tfn[ expando ] = true;\n\treturn fn;\n}\n\n/**\n * Support testing using an element\n * @param {Function} fn Passed the created element and returns a boolean result\n */\nfunction assert( fn ) {\n\tvar el = document.createElement( \"fieldset\" );\n\n\ttry {\n\t\treturn !!fn( el );\n\t} catch ( e ) {\n\t\treturn false;\n\t} finally {\n\n\t\t// Remove from its parent by default\n\t\tif ( el.parentNode ) {\n\t\t\tel.parentNode.removeChild( el );\n\t\t}\n\n\t\t// release memory in IE\n\t\tel = null;\n\t}\n}\n\n/**\n * Returns a function to use in pseudos for input types\n * @param {String} type\n */\nfunction createInputPseudo( type ) {\n\treturn function( elem ) {\n\t\treturn nodeName( elem, \"input\" ) && elem.type === type;\n\t};\n}\n\n/**\n * Returns a function to use in pseudos for buttons\n * @param {String} type\n */\nfunction createButtonPseudo( type ) {\n\treturn function( elem ) {\n\t\treturn ( nodeName( elem, \"input\" ) || nodeName( elem, \"button\" ) ) &&\n\t\t\telem.type === type;\n\t};\n}\n\n/**\n * Returns a function to use in pseudos for :enabled/:disabled\n * @param {Boolean} disabled true for :disabled; false for :enabled\n */\nfunction createDisabledPseudo( disabled ) {\n\n\t// Known :disabled false positives: fieldset[disabled] > legend:nth-of-type(n+2) :can-disable\n\treturn function( elem ) {\n\n\t\t// Only certain elements can match :enabled or :disabled\n\t\t// https://html.spec.whatwg.org/multipage/scripting.html#selector-enabled\n\t\t// https://html.spec.whatwg.org/multipage/scripting.html#selector-disabled\n\t\tif ( \"form\" in elem ) {\n\n\t\t\t// Check for inherited disabledness on relevant non-disabled elements:\n\t\t\t// * listed form-associated elements in a disabled fieldset\n\t\t\t//   https://html.spec.whatwg.org/multipage/forms.html#category-listed\n\t\t\t//   https://html.spec.whatwg.org/multipage/forms.html#concept-fe-disabled\n\t\t\t// * option elements in a disabled optgroup\n\t\t\t//   https://html.spec.whatwg.org/multipage/forms.html#concept-option-disabled\n\t\t\t// All such elements have a \"form\" property.\n\t\t\tif ( elem.parentNode && elem.disabled === false ) {\n\n\t\t\t\t// Option elements defer to a parent optgroup if present\n\t\t\t\tif ( \"label\" in elem ) {\n\t\t\t\t\tif ( \"label\" in elem.parentNode ) {\n\t\t\t\t\t\treturn elem.parentNode.disabled === disabled;\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn elem.disabled === disabled;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Support: IE 6 - 11+\n\t\t\t\t// Use the isDisabled shortcut property to check for disabled fieldset ancestors\n\t\t\t\treturn elem.isDisabled === disabled ||\n\n\t\t\t\t\t// Where there is no isDisabled, check manually\n\t\t\t\t\telem.isDisabled !== !disabled &&\n\t\t\t\t\t\tinDisabledFieldset( elem ) === disabled;\n\t\t\t}\n\n\t\t\treturn elem.disabled === disabled;\n\n\t\t// Try to winnow out elements that can't be disabled before trusting the disabled property.\n\t\t// Some victims get caught in our net (label, legend, menu, track), but it shouldn't\n\t\t// even exist on them, let alone have a boolean value.\n\t\t} else if ( \"label\" in elem ) {\n\t\t\treturn elem.disabled === disabled;\n\t\t}\n\n\t\t// Remaining elements are neither :enabled nor :disabled\n\t\treturn false;\n\t};\n}\n\n/**\n * Returns a function to use in pseudos for positionals\n * @param {Function} fn\n */\nfunction createPositionalPseudo( fn ) {\n\treturn markFunction( function( argument ) {\n\t\targument = +argument;\n\t\treturn markFunction( function( seed, matches ) {\n\t\t\tvar j,\n\t\t\t\tmatchIndexes = fn( [], seed.length, argument ),\n\t\t\t\ti = matchIndexes.length;\n\n\t\t\t// Match elements found at the specified indexes\n\t\t\twhile ( i-- ) {\n\t\t\t\tif ( seed[ ( j = matchIndexes[ i ] ) ] ) {\n\t\t\t\t\tseed[ j ] = !( matches[ j ] = seed[ j ] );\n\t\t\t\t}\n\t\t\t}\n\t\t} );\n\t} );\n}\n\n/**\n * Checks a node for validity as a jQuery selector context\n * @param {Element|Object=} context\n * @returns {Element|Object|Boolean} The input node if acceptable, otherwise a falsy value\n */\nfunction testContext( context ) {\n\treturn context && typeof context.getElementsByTagName !== \"undefined\" && context;\n}\n\n/**\n * Sets document-related variables once based on the current document\n * @param {Element|Object} [node] An element or document object to use to set the document\n * @returns {Object} Returns the current document\n */\nfunction setDocument( node ) {\n\tvar subWindow,\n\t\tdoc = node ? node.ownerDocument || node : preferredDoc;\n\n\t// Return early if doc is invalid or already selected\n\t// Support: IE 11+, Edge 17 - 18+\n\t// IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n\t// two documents; shallow comparisons work.\n\t// eslint-disable-next-line eqeqeq\n\tif ( doc == document || doc.nodeType !== 9 || !doc.documentElement ) {\n\t\treturn document;\n\t}\n\n\t// Update global variables\n\tdocument = doc;\n\tdocumentElement = document.documentElement;\n\tdocumentIsHTML = !jQuery.isXMLDoc( document );\n\n\t// Support: iOS 7 only, IE 9 - 11+\n\t// Older browsers didn't support unprefixed `matches`.\n\tmatches = documentElement.matches ||\n\t\tdocumentElement.webkitMatchesSelector ||\n\t\tdocumentElement.msMatchesSelector;\n\n\t// Support: IE 9 - 11+, Edge 12 - 18+\n\t// Accessing iframe documents after unload throws \"permission denied\" errors\n\t// (see trac-13936).\n\t// Limit the fix to IE & Edge Legacy; despite Edge 15+ implementing `matches`,\n\t// all IE 9+ and Edge Legacy versions implement `msMatchesSelector` as well.\n\tif ( documentElement.msMatchesSelector &&\n\n\t\t// Support: IE 11+, Edge 17 - 18+\n\t\t// IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n\t\t// two documents; shallow comparisons work.\n\t\t// eslint-disable-next-line eqeqeq\n\t\tpreferredDoc != document &&\n\t\t( subWindow = document.defaultView ) && subWindow.top !== subWindow ) {\n\n\t\t// Support: IE 9 - 11+, Edge 12 - 18+\n\t\tsubWindow.addEventListener( \"unload\", unloadHandler );\n\t}\n\n\t// Support: IE <10\n\t// Check if getElementById returns elements by name\n\t// The broken getElementById methods don't pick up programmatically-set names,\n\t// so use a roundabout getElementsByName test\n\tsupport.getById = assert( function( el ) {\n\t\tdocumentElement.appendChild( el ).id = jQuery.expando;\n\t\treturn !document.getElementsByName ||\n\t\t\t!document.getElementsByName( jQuery.expando ).length;\n\t} );\n\n\t// Support: IE 9 only\n\t// Check to see if it's possible to do matchesSelector\n\t// on a disconnected node.\n\tsupport.disconnectedMatch = assert( function( el ) {\n\t\treturn matches.call( el, \"*\" );\n\t} );\n\n\t// Support: IE 9 - 11+, Edge 12 - 18+\n\t// IE/Edge don't support the :scope pseudo-class.\n\tsupport.scope = assert( function() {\n\t\treturn document.querySelectorAll( \":scope\" );\n\t} );\n\n\t// Support: Chrome 105 - 111 only, Safari 15.4 - 16.3 only\n\t// Make sure the `:has()` argument is parsed unforgivingly.\n\t// We include `*` in the test to detect buggy implementations that are\n\t// _selectively_ forgiving (specifically when the list includes at least\n\t// one valid selector).\n\t// Note that we treat complete lack of support for `:has()` as if it were\n\t// spec-compliant support, which is fine because use of `:has()` in such\n\t// environments will fail in the qSA path and fall back to jQuery traversal\n\t// anyway.\n\tsupport.cssHas = assert( function() {\n\t\ttry {\n\t\t\tdocument.querySelector( \":has(*,:jqfake)\" );\n\t\t\treturn false;\n\t\t} catch ( e ) {\n\t\t\treturn true;\n\t\t}\n\t} );\n\n\t// ID filter and find\n\tif ( support.getById ) {\n\t\tExpr.filter.ID = function( id ) {\n\t\t\tvar attrId = id.replace( runescape, funescape );\n\t\t\treturn function( elem ) {\n\t\t\t\treturn elem.getAttribute( \"id\" ) === attrId;\n\t\t\t};\n\t\t};\n\t\tExpr.find.ID = function( id, context ) {\n\t\t\tif ( typeof context.getElementById !== \"undefined\" && documentIsHTML ) {\n\t\t\t\tvar elem = context.getElementById( id );\n\t\t\t\treturn elem ? [ elem ] : [];\n\t\t\t}\n\t\t};\n\t} else {\n\t\tExpr.filter.ID =  function( id ) {\n\t\t\tvar attrId = id.replace( runescape, funescape );\n\t\t\treturn function( elem ) {\n\t\t\t\tvar node = typeof elem.getAttributeNode !== \"undefined\" &&\n\t\t\t\t\telem.getAttributeNode( \"id\" );\n\t\t\t\treturn node && node.value === attrId;\n\t\t\t};\n\t\t};\n\n\t\t// Support: IE 6 - 7 only\n\t\t// getElementById is not reliable as a find shortcut\n\t\tExpr.find.ID = function( id, context ) {\n\t\t\tif ( typeof context.getElementById !== \"undefined\" && documentIsHTML ) {\n\t\t\t\tvar node, i, elems,\n\t\t\t\t\telem = context.getElementById( id );\n\n\t\t\t\tif ( elem ) {\n\n\t\t\t\t\t// Verify the id attribute\n\t\t\t\t\tnode = elem.getAttributeNode( \"id\" );\n\t\t\t\t\tif ( node && node.value === id ) {\n\t\t\t\t\t\treturn [ elem ];\n\t\t\t\t\t}\n\n\t\t\t\t\t// Fall back on getElementsByName\n\t\t\t\t\telems = context.getElementsByName( id );\n\t\t\t\t\ti = 0;\n\t\t\t\t\twhile ( ( elem = elems[ i++ ] ) ) {\n\t\t\t\t\t\tnode = elem.getAttributeNode( \"id\" );\n\t\t\t\t\t\tif ( node && node.value === id ) {\n\t\t\t\t\t\t\treturn [ elem ];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn [];\n\t\t\t}\n\t\t};\n\t}\n\n\t// Tag\n\tExpr.find.TAG = function( tag, context ) {\n\t\tif ( typeof context.getElementsByTagName !== \"undefined\" ) {\n\t\t\treturn context.getElementsByTagName( tag );\n\n\t\t// DocumentFragment nodes don't have gEBTN\n\t\t} else {\n\t\t\treturn context.querySelectorAll( tag );\n\t\t}\n\t};\n\n\t// Class\n\tExpr.find.CLASS = function( className, context ) {\n\t\tif ( typeof context.getElementsByClassName !== \"undefined\" && documentIsHTML ) {\n\t\t\treturn context.getElementsByClassName( className );\n\t\t}\n\t};\n\n\t/* QSA/matchesSelector\n\t---------------------------------------------------------------------- */\n\n\t// QSA and matchesSelector support\n\n\trbuggyQSA = [];\n\n\t// Build QSA regex\n\t// Regex strategy adopted from Diego Perini\n\tassert( function( el ) {\n\n\t\tvar input;\n\n\t\tdocumentElement.appendChild( el ).innerHTML =\n\t\t\t\"<a id='\" + expando + \"' href='' disabled='disabled'></a>\" +\n\t\t\t\"<select id='\" + expando + \"-\\r\\\\' disabled='disabled'>\" +\n\t\t\t\"<option selected=''></option></select>\";\n\n\t\t// Support: iOS <=7 - 8 only\n\t\t// Boolean attributes and \"value\" are not treated correctly in some XML documents\n\t\tif ( !el.querySelectorAll( \"[selected]\" ).length ) {\n\t\t\trbuggyQSA.push( \"\\\\[\" + whitespace + \"*(?:value|\" + booleans + \")\" );\n\t\t}\n\n\t\t// Support: iOS <=7 - 8 only\n\t\tif ( !el.querySelectorAll( \"[id~=\" + expando + \"-]\" ).length ) {\n\t\t\trbuggyQSA.push( \"~=\" );\n\t\t}\n\n\t\t// Support: iOS 8 only\n\t\t// https://bugs.webkit.org/show_bug.cgi?id=136851\n\t\t// In-page `selector#id sibling-combinator selector` fails\n\t\tif ( !el.querySelectorAll( \"a#\" + expando + \"+*\" ).length ) {\n\t\t\trbuggyQSA.push( \".#.+[+~]\" );\n\t\t}\n\n\t\t// Support: Chrome <=105+, Firefox <=104+, Safari <=15.4+\n\t\t// In some of the document kinds, these selectors wouldn't work natively.\n\t\t// This is probably OK but for backwards compatibility we want to maintain\n\t\t// handling them through jQuery traversal in jQuery 3.x.\n\t\tif ( !el.querySelectorAll( \":checked\" ).length ) {\n\t\t\trbuggyQSA.push( \":checked\" );\n\t\t}\n\n\t\t// Support: Windows 8 Native Apps\n\t\t// The type and name attributes are restricted during .innerHTML assignment\n\t\tinput = document.createElement( \"input\" );\n\t\tinput.setAttribute( \"type\", \"hidden\" );\n\t\tel.appendChild( input ).setAttribute( \"name\", \"D\" );\n\n\t\t// Support: IE 9 - 11+\n\t\t// IE's :disabled selector does not pick up the children of disabled fieldsets\n\t\t// Support: Chrome <=105+, Firefox <=104+, Safari <=15.4+\n\t\t// In some of the document kinds, these selectors wouldn't work natively.\n\t\t// This is probably OK but for backwards compatibility we want to maintain\n\t\t// handling them through jQuery traversal in jQuery 3.x.\n\t\tdocumentElement.appendChild( el ).disabled = true;\n\t\tif ( el.querySelectorAll( \":disabled\" ).length !== 2 ) {\n\t\t\trbuggyQSA.push( \":enabled\", \":disabled\" );\n\t\t}\n\n\t\t// Support: IE 11+, Edge 15 - 18+\n\t\t// IE 11/Edge don't find elements on a `[name='']` query in some cases.\n\t\t// Adding a temporary attribute to the document before the selection works\n\t\t// around the issue.\n\t\t// Interestingly, IE 10 & older don't seem to have the issue.\n\t\tinput = document.createElement( \"input\" );\n\t\tinput.setAttribute( \"name\", \"\" );\n\t\tel.appendChild( input );\n\t\tif ( !el.querySelectorAll( \"[name='']\" ).length ) {\n\t\t\trbuggyQSA.push( \"\\\\[\" + whitespace + \"*name\" + whitespace + \"*=\" +\n\t\t\t\twhitespace + \"*(?:''|\\\"\\\")\" );\n\t\t}\n\t} );\n\n\tif ( !support.cssHas ) {\n\n\t\t// Support: Chrome 105 - 110+, Safari 15.4 - 16.3+\n\t\t// Our regular `try-catch` mechanism fails to detect natively-unsupported\n\t\t// pseudo-classes inside `:has()` (such as `:has(:contains(\"Foo\"))`)\n\t\t// in browsers that parse the `:has()` argument as a forgiving selector list.\n\t\t// https://drafts.csswg.org/selectors/#relational now requires the argument\n\t\t// to be parsed unforgivingly, but browsers have not yet fully adjusted.\n\t\trbuggyQSA.push( \":has\" );\n\t}\n\n\trbuggyQSA = rbuggyQSA.length && new RegExp( rbuggyQSA.join( \"|\" ) );\n\n\t/* Sorting\n\t---------------------------------------------------------------------- */\n\n\t// Document order sorting\n\tsortOrder = function( a, b ) {\n\n\t\t// Flag for duplicate removal\n\t\tif ( a === b ) {\n\t\t\thasDuplicate = true;\n\t\t\treturn 0;\n\t\t}\n\n\t\t// Sort on method existence if only one input has compareDocumentPosition\n\t\tvar compare = !a.compareDocumentPosition - !b.compareDocumentPosition;\n\t\tif ( compare ) {\n\t\t\treturn compare;\n\t\t}\n\n\t\t// Calculate position if both inputs belong to the same document\n\t\t// Support: IE 11+, Edge 17 - 18+\n\t\t// IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n\t\t// two documents; shallow comparisons work.\n\t\t// eslint-disable-next-line eqeqeq\n\t\tcompare = ( a.ownerDocument || a ) == ( b.ownerDocument || b ) ?\n\t\t\ta.compareDocumentPosition( b ) :\n\n\t\t\t// Otherwise we know they are disconnected\n\t\t\t1;\n\n\t\t// Disconnected nodes\n\t\tif ( compare & 1 ||\n\t\t\t( !support.sortDetached && b.compareDocumentPosition( a ) === compare ) ) {\n\n\t\t\t// Choose the first element that is related to our preferred document\n\t\t\t// Support: IE 11+, Edge 17 - 18+\n\t\t\t// IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n\t\t\t// two documents; shallow comparisons work.\n\t\t\t// eslint-disable-next-line eqeqeq\n\t\t\tif ( a === document || a.ownerDocument == preferredDoc &&\n\t\t\t\tfind.contains( preferredDoc, a ) ) {\n\t\t\t\treturn -1;\n\t\t\t}\n\n\t\t\t// Support: IE 11+, Edge 17 - 18+\n\t\t\t// IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n\t\t\t// two documents; shallow comparisons work.\n\t\t\t// eslint-disable-next-line eqeqeq\n\t\t\tif ( b === document || b.ownerDocument == preferredDoc &&\n\t\t\t\tfind.contains( preferredDoc, b ) ) {\n\t\t\t\treturn 1;\n\t\t\t}\n\n\t\t\t// Maintain original order\n\t\t\treturn sortInput ?\n\t\t\t\t( indexOf.call( sortInput, a ) - indexOf.call( sortInput, b ) ) :\n\t\t\t\t0;\n\t\t}\n\n\t\treturn compare & 4 ? -1 : 1;\n\t};\n\n\treturn document;\n}\n\nfind.matches = function( expr, elements ) {\n\treturn find( expr, null, null, elements );\n};\n\nfind.matchesSelector = function( elem, expr ) {\n\tsetDocument( elem );\n\n\tif ( documentIsHTML &&\n\t\t!nonnativeSelectorCache[ expr + \" \" ] &&\n\t\t( !rbuggyQSA || !rbuggyQSA.test( expr ) ) ) {\n\n\t\ttry {\n\t\t\tvar ret = matches.call( elem, expr );\n\n\t\t\t// IE 9's matchesSelector returns false on disconnected nodes\n\t\t\tif ( ret || support.disconnectedMatch ||\n\n\t\t\t\t\t// As well, disconnected nodes are said to be in a document\n\t\t\t\t\t// fragment in IE 9\n\t\t\t\t\telem.document && elem.document.nodeType !== 11 ) {\n\t\t\t\treturn ret;\n\t\t\t}\n\t\t} catch ( e ) {\n\t\t\tnonnativeSelectorCache( expr, true );\n\t\t}\n\t}\n\n\treturn find( expr, document, null, [ elem ] ).length > 0;\n};\n\nfind.contains = function( context, elem ) {\n\n\t// Set document vars if needed\n\t// Support: IE 11+, Edge 17 - 18+\n\t// IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n\t// two documents; shallow comparisons work.\n\t// eslint-disable-next-line eqeqeq\n\tif ( ( context.ownerDocument || context ) != document ) {\n\t\tsetDocument( context );\n\t}\n\treturn jQuery.contains( context, elem );\n};\n\n\nfind.attr = function( elem, name ) {\n\n\t// Set document vars if needed\n\t// Support: IE 11+, Edge 17 - 18+\n\t// IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n\t// two documents; shallow comparisons work.\n\t// eslint-disable-next-line eqeqeq\n\tif ( ( elem.ownerDocument || elem ) != document ) {\n\t\tsetDocument( elem );\n\t}\n\n\tvar fn = Expr.attrHandle[ name.toLowerCase() ],\n\n\t\t// Don't get fooled by Object.prototype properties (see trac-13807)\n\t\tval = fn && hasOwn.call( Expr.attrHandle, name.toLowerCase() ) ?\n\t\t\tfn( elem, name, !documentIsHTML ) :\n\t\t\tundefined;\n\n\tif ( val !== undefined ) {\n\t\treturn val;\n\t}\n\n\treturn elem.getAttribute( name );\n};\n\nfind.error = function( msg ) {\n\tthrow new Error( \"Syntax error, unrecognized expression: \" + msg );\n};\n\n/**\n * Document sorting and removing duplicates\n * @param {ArrayLike} results\n */\njQuery.uniqueSort = function( results ) {\n\tvar elem,\n\t\tduplicates = [],\n\t\tj = 0,\n\t\ti = 0;\n\n\t// Unless we *know* we can detect duplicates, assume their presence\n\t//\n\t// Support: Android <=4.0+\n\t// Testing for detecting duplicates is unpredictable so instead assume we can't\n\t// depend on duplicate detection in all browsers without a stable sort.\n\thasDuplicate = !support.sortStable;\n\tsortInput = !support.sortStable && slice.call( results, 0 );\n\tsort.call( results, sortOrder );\n\n\tif ( hasDuplicate ) {\n\t\twhile ( ( elem = results[ i++ ] ) ) {\n\t\t\tif ( elem === results[ i ] ) {\n\t\t\t\tj = duplicates.push( i );\n\t\t\t}\n\t\t}\n\t\twhile ( j-- ) {\n\t\t\tsplice.call( results, duplicates[ j ], 1 );\n\t\t}\n\t}\n\n\t// Clear input after sorting to release objects\n\t// See https://github.com/jquery/sizzle/pull/225\n\tsortInput = null;\n\n\treturn results;\n};\n\njQuery.fn.uniqueSort = function() {\n\treturn this.pushStack( jQuery.uniqueSort( slice.apply( this ) ) );\n};\n\nExpr = jQuery.expr = {\n\n\t// Can be adjusted by the user\n\tcacheLength: 50,\n\n\tcreatePseudo: markFunction,\n\n\tmatch: matchExpr,\n\n\tattrHandle: {},\n\n\tfind: {},\n\n\trelative: {\n\t\t\">\": { dir: \"parentNode\", first: true },\n\t\t\" \": { dir: \"parentNode\" },\n\t\t\"+\": { dir: \"previousSibling\", first: true },\n\t\t\"~\": { dir: \"previousSibling\" }\n\t},\n\n\tpreFilter: {\n\t\tATTR: function( match ) {\n\t\t\tmatch[ 1 ] = match[ 1 ].replace( runescape, funescape );\n\n\t\t\t// Move the given value to match[3] whether quoted or unquoted\n\t\t\tmatch[ 3 ] = ( match[ 3 ] || match[ 4 ] || match[ 5 ] || \"\" )\n\t\t\t\t.replace( runescape, funescape );\n\n\t\t\tif ( match[ 2 ] === \"~=\" ) {\n\t\t\t\tmatch[ 3 ] = \" \" + match[ 3 ] + \" \";\n\t\t\t}\n\n\t\t\treturn match.slice( 0, 4 );\n\t\t},\n\n\t\tCHILD: function( match ) {\n\n\t\t\t/* matches from matchExpr[\"CHILD\"]\n\t\t\t\t1 type (only|nth|...)\n\t\t\t\t2 what (child|of-type)\n\t\t\t\t3 argument (even|odd|\\d*|\\d*n([+-]\\d+)?|...)\n\t\t\t\t4 xn-component of xn+y argument ([+-]?\\d*n|)\n\t\t\t\t5 sign of xn-component\n\t\t\t\t6 x of xn-component\n\t\t\t\t7 sign of y-component\n\t\t\t\t8 y of y-component\n\t\t\t*/\n\t\t\tmatch[ 1 ] = match[ 1 ].toLowerCase();\n\n\t\t\tif ( match[ 1 ].slice( 0, 3 ) === \"nth\" ) {\n\n\t\t\t\t// nth-* requires argument\n\t\t\t\tif ( !match[ 3 ] ) {\n\t\t\t\t\tfind.error( match[ 0 ] );\n\t\t\t\t}\n\n\t\t\t\t// numeric x and y parameters for Expr.filter.CHILD\n\t\t\t\t// remember that false/true cast respectively to 0/1\n\t\t\t\tmatch[ 4 ] = +( match[ 4 ] ?\n\t\t\t\t\tmatch[ 5 ] + ( match[ 6 ] || 1 ) :\n\t\t\t\t\t2 * ( match[ 3 ] === \"even\" || match[ 3 ] === \"odd\" )\n\t\t\t\t);\n\t\t\t\tmatch[ 5 ] = +( ( match[ 7 ] + match[ 8 ] ) || match[ 3 ] === \"odd\" );\n\n\t\t\t// other types prohibit arguments\n\t\t\t} else if ( match[ 3 ] ) {\n\t\t\t\tfind.error( match[ 0 ] );\n\t\t\t}\n\n\t\t\treturn match;\n\t\t},\n\n\t\tPSEUDO: function( match ) {\n\t\t\tvar excess,\n\t\t\t\tunquoted = !match[ 6 ] && match[ 2 ];\n\n\t\t\tif ( matchExpr.CHILD.test( match[ 0 ] ) ) {\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\t// Accept quoted arguments as-is\n\t\t\tif ( match[ 3 ] ) {\n\t\t\t\tmatch[ 2 ] = match[ 4 ] || match[ 5 ] || \"\";\n\n\t\t\t// Strip excess characters from unquoted arguments\n\t\t\t} else if ( unquoted && rpseudo.test( unquoted ) &&\n\n\t\t\t\t// Get excess from tokenize (recursively)\n\t\t\t\t( excess = tokenize( unquoted, true ) ) &&\n\n\t\t\t\t// advance to the next closing parenthesis\n\t\t\t\t( excess = unquoted.indexOf( \")\", unquoted.length - excess ) - unquoted.length ) ) {\n\n\t\t\t\t// excess is a negative index\n\t\t\t\tmatch[ 0 ] = match[ 0 ].slice( 0, excess );\n\t\t\t\tmatch[ 2 ] = unquoted.slice( 0, excess );\n\t\t\t}\n\n\t\t\t// Return only captures needed by the pseudo filter method (type and argument)\n\t\t\treturn match.slice( 0, 3 );\n\t\t}\n\t},\n\n\tfilter: {\n\n\t\tTAG: function( nodeNameSelector ) {\n\t\t\tvar expectedNodeName = nodeNameSelector.replace( runescape, funescape ).toLowerCase();\n\t\t\treturn nodeNameSelector === \"*\" ?\n\t\t\t\tfunction() {\n\t\t\t\t\treturn true;\n\t\t\t\t} :\n\t\t\t\tfunction( elem ) {\n\t\t\t\t\treturn nodeName( elem, expectedNodeName );\n\t\t\t\t};\n\t\t},\n\n\t\tCLASS: function( className ) {\n\t\t\tvar pattern = classCache[ className + \" \" ];\n\n\t\t\treturn pattern ||\n\t\t\t\t( pattern = new RegExp( \"(^|\" + whitespace + \")\" + className +\n\t\t\t\t\t\"(\" + whitespace + \"|$)\" ) ) &&\n\t\t\t\tclassCache( className, function( elem ) {\n\t\t\t\t\treturn pattern.test(\n\t\t\t\t\t\ttypeof elem.className === \"string\" && elem.className ||\n\t\t\t\t\t\t\ttypeof elem.getAttribute !== \"undefined\" &&\n\t\t\t\t\t\t\t\telem.getAttribute( \"class\" ) ||\n\t\t\t\t\t\t\t\"\"\n\t\t\t\t\t);\n\t\t\t\t} );\n\t\t},\n\n\t\tATTR: function( name, operator, check ) {\n\t\t\treturn function( elem ) {\n\t\t\t\tvar result = find.attr( elem, name );\n\n\t\t\t\tif ( result == null ) {\n\t\t\t\t\treturn operator === \"!=\";\n\t\t\t\t}\n\t\t\t\tif ( !operator ) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tresult += \"\";\n\n\t\t\t\tif ( operator === \"=\" ) {\n\t\t\t\t\treturn result === check;\n\t\t\t\t}\n\t\t\t\tif ( operator === \"!=\" ) {\n\t\t\t\t\treturn result !== check;\n\t\t\t\t}\n\t\t\t\tif ( operator === \"^=\" ) {\n\t\t\t\t\treturn check && result.indexOf( check ) === 0;\n\t\t\t\t}\n\t\t\t\tif ( operator === \"*=\" ) {\n\t\t\t\t\treturn check && result.indexOf( check ) > -1;\n\t\t\t\t}\n\t\t\t\tif ( operator === \"$=\" ) {\n\t\t\t\t\treturn check && result.slice( -check.length ) === check;\n\t\t\t\t}\n\t\t\t\tif ( operator === \"~=\" ) {\n\t\t\t\t\treturn ( \" \" + result.replace( rwhitespace, \" \" ) + \" \" )\n\t\t\t\t\t\t.indexOf( check ) > -1;\n\t\t\t\t}\n\t\t\t\tif ( operator === \"|=\" ) {\n\t\t\t\t\treturn result === check || result.slice( 0, check.length + 1 ) === check + \"-\";\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\t\t\t};\n\t\t},\n\n\t\tCHILD: function( type, what, _argument, first, last ) {\n\t\t\tvar simple = type.slice( 0, 3 ) !== \"nth\",\n\t\t\t\tforward = type.slice( -4 ) !== \"last\",\n\t\t\t\tofType = what === \"of-type\";\n\n\t\t\treturn first === 1 && last === 0 ?\n\n\t\t\t\t// Shortcut for :nth-*(n)\n\t\t\t\tfunction( elem ) {\n\t\t\t\t\treturn !!elem.parentNode;\n\t\t\t\t} :\n\n\t\t\t\tfunction( elem, _context, xml ) {\n\t\t\t\t\tvar cache, outerCache, node, nodeIndex, start,\n\t\t\t\t\t\tdir = simple !== forward ? \"nextSibling\" : \"previousSibling\",\n\t\t\t\t\t\tparent = elem.parentNode,\n\t\t\t\t\t\tname = ofType && elem.nodeName.toLowerCase(),\n\t\t\t\t\t\tuseCache = !xml && !ofType,\n\t\t\t\t\t\tdiff = false;\n\n\t\t\t\t\tif ( parent ) {\n\n\t\t\t\t\t\t// :(first|last|only)-(child|of-type)\n\t\t\t\t\t\tif ( simple ) {\n\t\t\t\t\t\t\twhile ( dir ) {\n\t\t\t\t\t\t\t\tnode = elem;\n\t\t\t\t\t\t\t\twhile ( ( node = node[ dir ] ) ) {\n\t\t\t\t\t\t\t\t\tif ( ofType ?\n\t\t\t\t\t\t\t\t\t\tnodeName( node, name ) :\n\t\t\t\t\t\t\t\t\t\tnode.nodeType === 1 ) {\n\n\t\t\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// Reverse direction for :only-* (if we haven't yet done so)\n\t\t\t\t\t\t\t\tstart = dir = type === \"only\" && !start && \"nextSibling\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tstart = [ forward ? parent.firstChild : parent.lastChild ];\n\n\t\t\t\t\t\t// non-xml :nth-child(...) stores cache data on `parent`\n\t\t\t\t\t\tif ( forward && useCache ) {\n\n\t\t\t\t\t\t\t// Seek `elem` from a previously-cached index\n\t\t\t\t\t\t\touterCache = parent[ expando ] || ( parent[ expando ] = {} );\n\t\t\t\t\t\t\tcache = outerCache[ type ] || [];\n\t\t\t\t\t\t\tnodeIndex = cache[ 0 ] === dirruns && cache[ 1 ];\n\t\t\t\t\t\t\tdiff = nodeIndex && cache[ 2 ];\n\t\t\t\t\t\t\tnode = nodeIndex && parent.childNodes[ nodeIndex ];\n\n\t\t\t\t\t\t\twhile ( ( node = ++nodeIndex && node && node[ dir ] ||\n\n\t\t\t\t\t\t\t\t// Fallback to seeking `elem` from the start\n\t\t\t\t\t\t\t\t( diff = nodeIndex = 0 ) || start.pop() ) ) {\n\n\t\t\t\t\t\t\t\t// When found, cache indexes on `parent` and break\n\t\t\t\t\t\t\t\tif ( node.nodeType === 1 && ++diff && node === elem ) {\n\t\t\t\t\t\t\t\t\touterCache[ type ] = [ dirruns, nodeIndex, diff ];\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t// Use previously-cached element index if available\n\t\t\t\t\t\t\tif ( useCache ) {\n\t\t\t\t\t\t\t\touterCache = elem[ expando ] || ( elem[ expando ] = {} );\n\t\t\t\t\t\t\t\tcache = outerCache[ type ] || [];\n\t\t\t\t\t\t\t\tnodeIndex = cache[ 0 ] === dirruns && cache[ 1 ];\n\t\t\t\t\t\t\t\tdiff = nodeIndex;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// xml :nth-child(...)\n\t\t\t\t\t\t\t// or :nth-last-child(...) or :nth(-last)?-of-type(...)\n\t\t\t\t\t\t\tif ( diff === false ) {\n\n\t\t\t\t\t\t\t\t// Use the same loop as above to seek `elem` from the start\n\t\t\t\t\t\t\t\twhile ( ( node = ++nodeIndex && node && node[ dir ] ||\n\t\t\t\t\t\t\t\t\t( diff = nodeIndex = 0 ) || start.pop() ) ) {\n\n\t\t\t\t\t\t\t\t\tif ( ( ofType ?\n\t\t\t\t\t\t\t\t\t\tnodeName( node, name ) :\n\t\t\t\t\t\t\t\t\t\tnode.nodeType === 1 ) &&\n\t\t\t\t\t\t\t\t\t\t++diff ) {\n\n\t\t\t\t\t\t\t\t\t\t// Cache the index of each encountered element\n\t\t\t\t\t\t\t\t\t\tif ( useCache ) {\n\t\t\t\t\t\t\t\t\t\t\touterCache = node[ expando ] ||\n\t\t\t\t\t\t\t\t\t\t\t\t( node[ expando ] = {} );\n\t\t\t\t\t\t\t\t\t\t\touterCache[ type ] = [ dirruns, diff ];\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tif ( node === elem ) {\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Incorporate the offset, then check against cycle size\n\t\t\t\t\t\tdiff -= last;\n\t\t\t\t\t\treturn diff === first || ( diff % first === 0 && diff / first >= 0 );\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t},\n\n\t\tPSEUDO: function( pseudo, argument ) {\n\n\t\t\t// pseudo-class names are case-insensitive\n\t\t\t// https://www.w3.org/TR/selectors/#pseudo-classes\n\t\t\t// Prioritize by case sensitivity in case custom pseudos are added with uppercase letters\n\t\t\t// Remember that setFilters inherits from pseudos\n\t\t\tvar args,\n\t\t\t\tfn = Expr.pseudos[ pseudo ] || Expr.setFilters[ pseudo.toLowerCase() ] ||\n\t\t\t\t\tfind.error( \"unsupported pseudo: \" + pseudo );\n\n\t\t\t// The user may use createPseudo to indicate that\n\t\t\t// arguments are needed to create the filter function\n\t\t\t// just as jQuery does\n\t\t\tif ( fn[ expando ] ) {\n\t\t\t\treturn fn( argument );\n\t\t\t}\n\n\t\t\t// But maintain support for old signatures\n\t\t\tif ( fn.length > 1 ) {\n\t\t\t\targs = [ pseudo, pseudo, \"\", argument ];\n\t\t\t\treturn Expr.setFilters.hasOwnProperty( pseudo.toLowerCase() ) ?\n\t\t\t\t\tmarkFunction( function( seed, matches ) {\n\t\t\t\t\t\tvar idx,\n\t\t\t\t\t\t\tmatched = fn( seed, argument ),\n\t\t\t\t\t\t\ti = matched.length;\n\t\t\t\t\t\twhile ( i-- ) {\n\t\t\t\t\t\t\tidx = indexOf.call( seed, matched[ i ] );\n\t\t\t\t\t\t\tseed[ idx ] = !( matches[ idx ] = matched[ i ] );\n\t\t\t\t\t\t}\n\t\t\t\t\t} ) :\n\t\t\t\t\tfunction( elem ) {\n\t\t\t\t\t\treturn fn( elem, 0, args );\n\t\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn fn;\n\t\t}\n\t},\n\n\tpseudos: {\n\n\t\t// Potentially complex pseudos\n\t\tnot: markFunction( function( selector ) {\n\n\t\t\t// Trim the selector passed to compile\n\t\t\t// to avoid treating leading and trailing\n\t\t\t// spaces as combinators\n\t\t\tvar input = [],\n\t\t\t\tresults = [],\n\t\t\t\tmatcher = compile( selector.replace( rtrimCSS, \"$1\" ) );\n\n\t\t\treturn matcher[ expando ] ?\n\t\t\t\tmarkFunction( function( seed, matches, _context, xml ) {\n\t\t\t\t\tvar elem,\n\t\t\t\t\t\tunmatched = matcher( seed, null, xml, [] ),\n\t\t\t\t\t\ti = seed.length;\n\n\t\t\t\t\t// Match elements unmatched by `matcher`\n\t\t\t\t\twhile ( i-- ) {\n\t\t\t\t\t\tif ( ( elem = unmatched[ i ] ) ) {\n\t\t\t\t\t\t\tseed[ i ] = !( matches[ i ] = elem );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} ) :\n\t\t\t\tfunction( elem, _context, xml ) {\n\t\t\t\t\tinput[ 0 ] = elem;\n\t\t\t\t\tmatcher( input, null, xml, results );\n\n\t\t\t\t\t// Don't keep the element\n\t\t\t\t\t// (see https://github.com/jquery/sizzle/issues/299)\n\t\t\t\t\tinput[ 0 ] = null;\n\t\t\t\t\treturn !results.pop();\n\t\t\t\t};\n\t\t} ),\n\n\t\thas: markFunction( function( selector ) {\n\t\t\treturn function( elem ) {\n\t\t\t\treturn find( selector, elem ).length > 0;\n\t\t\t};\n\t\t} ),\n\n\t\tcontains: markFunction( function( text ) {\n\t\t\ttext = text.replace( runescape, funescape );\n\t\t\treturn function( elem ) {\n\t\t\t\treturn ( elem.textContent || jQuery.text( elem ) ).indexOf( text ) > -1;\n\t\t\t};\n\t\t} ),\n\n\t\t// \"Whether an element is represented by a :lang() selector\n\t\t// is based solely on the element's language value\n\t\t// being equal to the identifier C,\n\t\t// or beginning with the identifier C immediately followed by \"-\".\n\t\t// The matching of C against the element's language value is performed case-insensitively.\n\t\t// The identifier C does not have to be a valid language name.\"\n\t\t// https://www.w3.org/TR/selectors/#lang-pseudo\n\t\tlang: markFunction( function( lang ) {\n\n\t\t\t// lang value must be a valid identifier\n\t\t\tif ( !ridentifier.test( lang || \"\" ) ) {\n\t\t\t\tfind.error( \"unsupported lang: \" + lang );\n\t\t\t}\n\t\t\tlang = lang.replace( runescape, funescape ).toLowerCase();\n\t\t\treturn function( elem ) {\n\t\t\t\tvar elemLang;\n\t\t\t\tdo {\n\t\t\t\t\tif ( ( elemLang = documentIsHTML ?\n\t\t\t\t\t\telem.lang :\n\t\t\t\t\t\telem.getAttribute( \"xml:lang\" ) || elem.getAttribute( \"lang\" ) ) ) {\n\n\t\t\t\t\t\telemLang = elemLang.toLowerCase();\n\t\t\t\t\t\treturn elemLang === lang || elemLang.indexOf( lang + \"-\" ) === 0;\n\t\t\t\t\t}\n\t\t\t\t} while ( ( elem = elem.parentNode ) && elem.nodeType === 1 );\n\t\t\t\treturn false;\n\t\t\t};\n\t\t} ),\n\n\t\t// Miscellaneous\n\t\ttarget: function( elem ) {\n\t\t\tvar hash = window.location && window.location.hash;\n\t\t\treturn hash && hash.slice( 1 ) === elem.id;\n\t\t},\n\n\t\troot: function( elem ) {\n\t\t\treturn elem === documentElement;\n\t\t},\n\n\t\tfocus: function( elem ) {\n\t\t\treturn elem === safeActiveElement() &&\n\t\t\t\tdocument.hasFocus() &&\n\t\t\t\t!!( elem.type || elem.href || ~elem.tabIndex );\n\t\t},\n\n\t\t// Boolean properties\n\t\tenabled: createDisabledPseudo( false ),\n\t\tdisabled: createDisabledPseudo( true ),\n\n\t\tchecked: function( elem ) {\n\n\t\t\t// In CSS3, :checked should return both checked and selected elements\n\t\t\t// https://www.w3.org/TR/2011/REC-css3-selectors-20110929/#checked\n\t\t\treturn ( nodeName( elem, \"input\" ) && !!elem.checked ) ||\n\t\t\t\t( nodeName( elem, \"option\" ) && !!elem.selected );\n\t\t},\n\n\t\tselected: function( elem ) {\n\n\t\t\t// Support: IE <=11+\n\t\t\t// Accessing the selectedIndex property\n\t\t\t// forces the browser to treat the default option as\n\t\t\t// selected when in an optgroup.\n\t\t\tif ( elem.parentNode ) {\n\t\t\t\t// eslint-disable-next-line no-unused-expressions\n\t\t\t\telem.parentNode.selectedIndex;\n\t\t\t}\n\n\t\t\treturn elem.selected === true;\n\t\t},\n\n\t\t// Contents\n\t\tempty: function( elem ) {\n\n\t\t\t// https://www.w3.org/TR/selectors/#empty-pseudo\n\t\t\t// :empty is negated by element (1) or content nodes (text: 3; cdata: 4; entity ref: 5),\n\t\t\t//   but not by others (comment: 8; processing instruction: 7; etc.)\n\t\t\t// nodeType < 6 works because attributes (2) do not appear as children\n\t\t\tfor ( elem = elem.firstChild; elem; elem = elem.nextSibling ) {\n\t\t\t\tif ( elem.nodeType < 6 ) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t},\n\n\t\tparent: function( elem ) {\n\t\t\treturn !Expr.pseudos.empty( elem );\n\t\t},\n\n\t\t// Element/input types\n\t\theader: function( elem ) {\n\t\t\treturn rheader.test( elem.nodeName );\n\t\t},\n\n\t\tinput: function( elem ) {\n\t\t\treturn rinputs.test( elem.nodeName );\n\t\t},\n\n\t\tbutton: function( elem ) {\n\t\t\treturn nodeName( elem, \"input\" ) && elem.type === \"button\" ||\n\t\t\t\tnodeName( elem, \"button\" );\n\t\t},\n\n\t\ttext: function( elem ) {\n\t\t\tvar attr;\n\t\t\treturn nodeName( elem, \"input\" ) && elem.type === \"text\" &&\n\n\t\t\t\t// Support: IE <10 only\n\t\t\t\t// New HTML5 attribute values (e.g., \"search\") appear\n\t\t\t\t// with elem.type === \"text\"\n\t\t\t\t( ( attr = elem.getAttribute( \"type\" ) ) == null ||\n\t\t\t\t\tattr.toLowerCase() === \"text\" );\n\t\t},\n\n\t\t// Position-in-collection\n\t\tfirst: createPositionalPseudo( function() {\n\t\t\treturn [ 0 ];\n\t\t} ),\n\n\t\tlast: createPositionalPseudo( function( _matchIndexes, length ) {\n\t\t\treturn [ length - 1 ];\n\t\t} ),\n\n\t\teq: createPositionalPseudo( function( _matchIndexes, length, argument ) {\n\t\t\treturn [ argument < 0 ? argument + length : argument ];\n\t\t} ),\n\n\t\teven: createPositionalPseudo( function( matchIndexes, length ) {\n\t\t\tvar i = 0;\n\t\t\tfor ( ; i < length; i += 2 ) {\n\t\t\t\tmatchIndexes.push( i );\n\t\t\t}\n\t\t\treturn matchIndexes;\n\t\t} ),\n\n\t\todd: createPositionalPseudo( function( matchIndexes, length ) {\n\t\t\tvar i = 1;\n\t\t\tfor ( ; i < length; i += 2 ) {\n\t\t\t\tmatchIndexes.push( i );\n\t\t\t}\n\t\t\treturn matchIndexes;\n\t\t} ),\n\n\t\tlt: createPositionalPseudo( function( matchIndexes, length, argument ) {\n\t\t\tvar i;\n\n\t\t\tif ( argument < 0 ) {\n\t\t\t\ti = argument + length;\n\t\t\t} else if ( argument > length ) {\n\t\t\t\ti = length;\n\t\t\t} else {\n\t\t\t\ti = argument;\n\t\t\t}\n\n\t\t\tfor ( ; --i >= 0; ) {\n\t\t\t\tmatchIndexes.push( i );\n\t\t\t}\n\t\t\treturn matchIndexes;\n\t\t} ),\n\n\t\tgt: createPositionalPseudo( function( matchIndexes, length, argument ) {\n\t\t\tvar i = argument < 0 ? argument + length : argument;\n\t\t\tfor ( ; ++i < length; ) {\n\t\t\t\tmatchIndexes.push( i );\n\t\t\t}\n\t\t\treturn matchIndexes;\n\t\t} )\n\t}\n};\n\nExpr.pseudos.nth = Expr.pseudos.eq;\n\n// Add button/input type pseudos\nfor ( i in { radio: true, checkbox: true, file: true, password: true, image: true } ) {\n\tExpr.pseudos[ i ] = createInputPseudo( i );\n}\nfor ( i in { submit: true, reset: true } ) {\n\tExpr.pseudos[ i ] = createButtonPseudo( i );\n}\n\n// Easy API for creating new setFilters\nfunction setFilters() {}\nsetFilters.prototype = Expr.filters = Expr.pseudos;\nExpr.setFilters = new setFilters();\n\nfunction tokenize( selector, parseOnly ) {\n\tvar matched, match, tokens, type,\n\t\tsoFar, groups, preFilters,\n\t\tcached = tokenCache[ selector + \" \" ];\n\n\tif ( cached ) {\n\t\treturn parseOnly ? 0 : cached.slice( 0 );\n\t}\n\n\tsoFar = selector;\n\tgroups = [];\n\tpreFilters = Expr.preFilter;\n\n\twhile ( soFar ) {\n\n\t\t// Comma and first run\n\t\tif ( !matched || ( match = rcomma.exec( soFar ) ) ) {\n\t\t\tif ( match ) {\n\n\t\t\t\t// Don't consume trailing commas as valid\n\t\t\t\tsoFar = soFar.slice( match[ 0 ].length ) || soFar;\n\t\t\t}\n\t\t\tgroups.push( ( tokens = [] ) );\n\t\t}\n\n\t\tmatched = false;\n\n\t\t// Combinators\n\t\tif ( ( match = rleadingCombinator.exec( soFar ) ) ) {\n\t\t\tmatched = match.shift();\n\t\t\ttokens.push( {\n\t\t\t\tvalue: matched,\n\n\t\t\t\t// Cast descendant combinators to space\n\t\t\t\ttype: match[ 0 ].replace( rtrimCSS, \" \" )\n\t\t\t} );\n\t\t\tsoFar = soFar.slice( matched.length );\n\t\t}\n\n\t\t// Filters\n\t\tfor ( type in Expr.filter ) {\n\t\t\tif ( ( match = matchExpr[ type ].exec( soFar ) ) && ( !preFilters[ type ] ||\n\t\t\t\t( match = preFilters[ type ]( match ) ) ) ) {\n\t\t\t\tmatched = match.shift();\n\t\t\t\ttokens.push( {\n\t\t\t\t\tvalue: matched,\n\t\t\t\t\ttype: type,\n\t\t\t\t\tmatches: match\n\t\t\t\t} );\n\t\t\t\tsoFar = soFar.slice( matched.length );\n\t\t\t}\n\t\t}\n\n\t\tif ( !matched ) {\n\t\t\tbreak;\n\t\t}\n\t}\n\n\t// Return the length of the invalid excess\n\t// if we're just parsing\n\t// Otherwise, throw an error or return tokens\n\tif ( parseOnly ) {\n\t\treturn soFar.length;\n\t}\n\n\treturn soFar ?\n\t\tfind.error( selector ) :\n\n\t\t// Cache the tokens\n\t\ttokenCache( selector, groups ).slice( 0 );\n}\n\nfunction toSelector( tokens ) {\n\tvar i = 0,\n\t\tlen = tokens.length,\n\t\tselector = \"\";\n\tfor ( ; i < len; i++ ) {\n\t\tselector += tokens[ i ].value;\n\t}\n\treturn selector;\n}\n\nfunction addCombinator( matcher, combinator, base ) {\n\tvar dir = combinator.dir,\n\t\tskip = combinator.next,\n\t\tkey = skip || dir,\n\t\tcheckNonElements = base && key === \"parentNode\",\n\t\tdoneName = done++;\n\n\treturn combinator.first ?\n\n\t\t// Check against closest ancestor/preceding element\n\t\tfunction( elem, context, xml ) {\n\t\t\twhile ( ( elem = elem[ dir ] ) ) {\n\t\t\t\tif ( elem.nodeType === 1 || checkNonElements ) {\n\t\t\t\t\treturn matcher( elem, context, xml );\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t} :\n\n\t\t// Check against all ancestor/preceding elements\n\t\tfunction( elem, context, xml ) {\n\t\t\tvar oldCache, outerCache,\n\t\t\t\tnewCache = [ dirruns, doneName ];\n\n\t\t\t// We can't set arbitrary data on XML nodes, so they don't benefit from combinator caching\n\t\t\tif ( xml ) {\n\t\t\t\twhile ( ( elem = elem[ dir ] ) ) {\n\t\t\t\t\tif ( elem.nodeType === 1 || checkNonElements ) {\n\t\t\t\t\t\tif ( matcher( elem, context, xml ) ) {\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\twhile ( ( elem = elem[ dir ] ) ) {\n\t\t\t\t\tif ( elem.nodeType === 1 || checkNonElements ) {\n\t\t\t\t\t\touterCache = elem[ expando ] || ( elem[ expando ] = {} );\n\n\t\t\t\t\t\tif ( skip && nodeName( elem, skip ) ) {\n\t\t\t\t\t\t\telem = elem[ dir ] || elem;\n\t\t\t\t\t\t} else if ( ( oldCache = outerCache[ key ] ) &&\n\t\t\t\t\t\t\toldCache[ 0 ] === dirruns && oldCache[ 1 ] === doneName ) {\n\n\t\t\t\t\t\t\t// Assign to newCache so results back-propagate to previous elements\n\t\t\t\t\t\t\treturn ( newCache[ 2 ] = oldCache[ 2 ] );\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t// Reuse newcache so results back-propagate to previous elements\n\t\t\t\t\t\t\touterCache[ key ] = newCache;\n\n\t\t\t\t\t\t\t// A match means we're done; a fail means we have to keep checking\n\t\t\t\t\t\t\tif ( ( newCache[ 2 ] = matcher( elem, context, xml ) ) ) {\n\t\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t};\n}\n\nfunction elementMatcher( matchers ) {\n\treturn matchers.length > 1 ?\n\t\tfunction( elem, context, xml ) {\n\t\t\tvar i = matchers.length;\n\t\t\twhile ( i-- ) {\n\t\t\t\tif ( !matchers[ i ]( elem, context, xml ) ) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t} :\n\t\tmatchers[ 0 ];\n}\n\nfunction multipleContexts( selector, contexts, results ) {\n\tvar i = 0,\n\t\tlen = contexts.length;\n\tfor ( ; i < len; i++ ) {\n\t\tfind( selector, contexts[ i ], results );\n\t}\n\treturn results;\n}\n\nfunction condense( unmatched, map, filter, context, xml ) {\n\tvar elem,\n\t\tnewUnmatched = [],\n\t\ti = 0,\n\t\tlen = unmatched.length,\n\t\tmapped = map != null;\n\n\tfor ( ; i < len; i++ ) {\n\t\tif ( ( elem = unmatched[ i ] ) ) {\n\t\t\tif ( !filter || filter( elem, context, xml ) ) {\n\t\t\t\tnewUnmatched.push( elem );\n\t\t\t\tif ( mapped ) {\n\t\t\t\t\tmap.push( i );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn newUnmatched;\n}\n\nfunction setMatcher( preFilter, selector, matcher, postFilter, postFinder, postSelector ) {\n\tif ( postFilter && !postFilter[ expando ] ) {\n\t\tpostFilter = setMatcher( postFilter );\n\t}\n\tif ( postFinder && !postFinder[ expando ] ) {\n\t\tpostFinder = setMatcher( postFinder, postSelector );\n\t}\n\treturn markFunction( function( seed, results, context, xml ) {\n\t\tvar temp, i, elem, matcherOut,\n\t\t\tpreMap = [],\n\t\t\tpostMap = [],\n\t\t\tpreexisting = results.length,\n\n\t\t\t// Get initial elements from seed or context\n\t\t\telems = seed ||\n\t\t\t\tmultipleContexts( selector || \"*\",\n\t\t\t\t\tcontext.nodeType ? [ context ] : context, [] ),\n\n\t\t\t// Prefilter to get matcher input, preserving a map for seed-results synchronization\n\t\t\tmatcherIn = preFilter && ( seed || !selector ) ?\n\t\t\t\tcondense( elems, preMap, preFilter, context, xml ) :\n\t\t\t\telems;\n\n\t\tif ( matcher ) {\n\n\t\t\t// If we have a postFinder, or filtered seed, or non-seed postFilter\n\t\t\t// or preexisting results,\n\t\t\tmatcherOut = postFinder || ( seed ? preFilter : preexisting || postFilter ) ?\n\n\t\t\t\t// ...intermediate processing is necessary\n\t\t\t\t[] :\n\n\t\t\t\t// ...otherwise use results directly\n\t\t\t\tresults;\n\n\t\t\t// Find primary matches\n\t\t\tmatcher( matcherIn, matcherOut, context, xml );\n\t\t} else {\n\t\t\tmatcherOut = matcherIn;\n\t\t}\n\n\t\t// Apply postFilter\n\t\tif ( postFilter ) {\n\t\t\ttemp = condense( matcherOut, postMap );\n\t\t\tpostFilter( temp, [], context, xml );\n\n\t\t\t// Un-match failing elements by moving them back to matcherIn\n\t\t\ti = temp.length;\n\t\t\twhile ( i-- ) {\n\t\t\t\tif ( ( elem = temp[ i ] ) ) {\n\t\t\t\t\tmatcherOut[ postMap[ i ] ] = !( matcherIn[ postMap[ i ] ] = elem );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif ( seed ) {\n\t\t\tif ( postFinder || preFilter ) {\n\t\t\t\tif ( postFinder ) {\n\n\t\t\t\t\t// Get the final matcherOut by condensing this intermediate into postFinder contexts\n\t\t\t\t\ttemp = [];\n\t\t\t\t\ti = matcherOut.length;\n\t\t\t\t\twhile ( i-- ) {\n\t\t\t\t\t\tif ( ( elem = matcherOut[ i ] ) ) {\n\n\t\t\t\t\t\t\t// Restore matcherIn since elem is not yet a final match\n\t\t\t\t\t\t\ttemp.push( ( matcherIn[ i ] = elem ) );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tpostFinder( null, ( matcherOut = [] ), temp, xml );\n\t\t\t\t}\n\n\t\t\t\t// Move matched elements from seed to results to keep them synchronized\n\t\t\t\ti = matcherOut.length;\n\t\t\t\twhile ( i-- ) {\n\t\t\t\t\tif ( ( elem = matcherOut[ i ] ) &&\n\t\t\t\t\t\t( temp = postFinder ? indexOf.call( seed, elem ) : preMap[ i ] ) > -1 ) {\n\n\t\t\t\t\t\tseed[ temp ] = !( results[ temp ] = elem );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t// Add elements to results, through postFinder if defined\n\t\t} else {\n\t\t\tmatcherOut = condense(\n\t\t\t\tmatcherOut === results ?\n\t\t\t\t\tmatcherOut.splice( preexisting, matcherOut.length ) :\n\t\t\t\t\tmatcherOut\n\t\t\t);\n\t\t\tif ( postFinder ) {\n\t\t\t\tpostFinder( null, results, matcherOut, xml );\n\t\t\t} else {\n\t\t\t\tpush.apply( results, matcherOut );\n\t\t\t}\n\t\t}\n\t} );\n}\n\nfunction matcherFromTokens( tokens ) {\n\tvar checkContext, matcher, j,\n\t\tlen = tokens.length,\n\t\tleadingRelative = Expr.relative[ tokens[ 0 ].type ],\n\t\timplicitRelative = leadingRelative || Expr.relative[ \" \" ],\n\t\ti = leadingRelative ? 1 : 0,\n\n\t\t// The foundational matcher ensures that elements are reachable from top-level context(s)\n\t\tmatchContext = addCombinator( function( elem ) {\n\t\t\treturn elem === checkContext;\n\t\t}, implicitRelative, true ),\n\t\tmatchAnyContext = addCombinator( function( elem ) {\n\t\t\treturn indexOf.call( checkContext, elem ) > -1;\n\t\t}, implicitRelative, true ),\n\t\tmatchers = [ function( elem, context, xml ) {\n\n\t\t\t// Support: IE 11+, Edge 17 - 18+\n\t\t\t// IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n\t\t\t// two documents; shallow comparisons work.\n\t\t\t// eslint-disable-next-line eqeqeq\n\t\t\tvar ret = ( !leadingRelative && ( xml || context != outermostContext ) ) || (\n\t\t\t\t( checkContext = context ).nodeType ?\n\t\t\t\t\tmatchContext( elem, context, xml ) :\n\t\t\t\t\tmatchAnyContext( elem, context, xml ) );\n\n\t\t\t// Avoid hanging onto element\n\t\t\t// (see https://github.com/jquery/sizzle/issues/299)\n\t\t\tcheckContext = null;\n\t\t\treturn ret;\n\t\t} ];\n\n\tfor ( ; i < len; i++ ) {\n\t\tif ( ( matcher = Expr.relative[ tokens[ i ].type ] ) ) {\n\t\t\tmatchers = [ addCombinator( elementMatcher( matchers ), matcher ) ];\n\t\t} else {\n\t\t\tmatcher = Expr.filter[ tokens[ i ].type ].apply( null, tokens[ i ].matches );\n\n\t\t\t// Return special upon seeing a positional matcher\n\t\t\tif ( matcher[ expando ] ) {\n\n\t\t\t\t// Find the next relative operator (if any) for proper handling\n\t\t\t\tj = ++i;\n\t\t\t\tfor ( ; j < len; j++ ) {\n\t\t\t\t\tif ( Expr.relative[ tokens[ j ].type ] ) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn setMatcher(\n\t\t\t\t\ti > 1 && elementMatcher( matchers ),\n\t\t\t\t\ti > 1 && toSelector(\n\n\t\t\t\t\t\t// If the preceding token was a descendant combinator, insert an implicit any-element `*`\n\t\t\t\t\t\ttokens.slice( 0, i - 1 )\n\t\t\t\t\t\t\t.concat( { value: tokens[ i - 2 ].type === \" \" ? \"*\" : \"\" } )\n\t\t\t\t\t).replace( rtrimCSS, \"$1\" ),\n\t\t\t\t\tmatcher,\n\t\t\t\t\ti < j && matcherFromTokens( tokens.slice( i, j ) ),\n\t\t\t\t\tj < len && matcherFromTokens( ( tokens = tokens.slice( j ) ) ),\n\t\t\t\t\tj < len && toSelector( tokens )\n\t\t\t\t);\n\t\t\t}\n\t\t\tmatchers.push( matcher );\n\t\t}\n\t}\n\n\treturn elementMatcher( matchers );\n}\n\nfunction matcherFromGroupMatchers( elementMatchers, setMatchers ) {\n\tvar bySet = setMatchers.length > 0,\n\t\tbyElement = elementMatchers.length > 0,\n\t\tsuperMatcher = function( seed, context, xml, results, outermost ) {\n\t\t\tvar elem, j, matcher,\n\t\t\t\tmatchedCount = 0,\n\t\t\t\ti = \"0\",\n\t\t\t\tunmatched = seed && [],\n\t\t\t\tsetMatched = [],\n\t\t\t\tcontextBackup = outermostContext,\n\n\t\t\t\t// We must always have either seed elements or outermost context\n\t\t\t\telems = seed || byElement && Expr.find.TAG( \"*\", outermost ),\n\n\t\t\t\t// Use integer dirruns iff this is the outermost matcher\n\t\t\t\tdirrunsUnique = ( dirruns += contextBackup == null ? 1 : Math.random() || 0.1 ),\n\t\t\t\tlen = elems.length;\n\n\t\t\tif ( outermost ) {\n\n\t\t\t\t// Support: IE 11+, Edge 17 - 18+\n\t\t\t\t// IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n\t\t\t\t// two documents; shallow comparisons work.\n\t\t\t\t// eslint-disable-next-line eqeqeq\n\t\t\t\toutermostContext = context == document || context || outermost;\n\t\t\t}\n\n\t\t\t// Add elements passing elementMatchers directly to results\n\t\t\t// Support: iOS <=7 - 9 only\n\t\t\t// Tolerate NodeList properties (IE: \"length\"; Safari: <number>) matching\n\t\t\t// elements by id. (see trac-14142)\n\t\t\tfor ( ; i !== len && ( elem = elems[ i ] ) != null; i++ ) {\n\t\t\t\tif ( byElement && elem ) {\n\t\t\t\t\tj = 0;\n\n\t\t\t\t\t// Support: IE 11+, Edge 17 - 18+\n\t\t\t\t\t// IE/Edge sometimes throw a \"Permission denied\" error when strict-comparing\n\t\t\t\t\t// two documents; shallow comparisons work.\n\t\t\t\t\t// eslint-disable-next-line eqeqeq\n\t\t\t\t\tif ( !context && elem.ownerDocument != document ) {\n\t\t\t\t\t\tsetDocument( elem );\n\t\t\t\t\t\txml = !documentIsHTML;\n\t\t\t\t\t}\n\t\t\t\t\twhile ( ( matcher = elementMatchers[ j++ ] ) ) {\n\t\t\t\t\t\tif ( matcher( elem, context || document, xml ) ) {\n\t\t\t\t\t\t\tpush.call( results, elem );\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif ( outermost ) {\n\t\t\t\t\t\tdirruns = dirrunsUnique;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Track unmatched elements for set filters\n\t\t\t\tif ( bySet ) {\n\n\t\t\t\t\t// They will have gone through all possible matchers\n\t\t\t\t\tif ( ( elem = !matcher && elem ) ) {\n\t\t\t\t\t\tmatchedCount--;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Lengthen the array for every element, matched or not\n\t\t\t\t\tif ( seed ) {\n\t\t\t\t\t\tunmatched.push( elem );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// `i` is now the count of elements visited above, and adding it to `matchedCount`\n\t\t\t// makes the latter nonnegative.\n\t\t\tmatchedCount += i;\n\n\t\t\t// Apply set filters to unmatched elements\n\t\t\t// NOTE: This can be skipped if there are no unmatched elements (i.e., `matchedCount`\n\t\t\t// equals `i`), unless we didn't visit _any_ elements in the above loop because we have\n\t\t\t// no element matchers and no seed.\n\t\t\t// Incrementing an initially-string \"0\" `i` allows `i` to remain a string only in that\n\t\t\t// case, which will result in a \"00\" `matchedCount` that differs from `i` but is also\n\t\t\t// numerically zero.\n\t\t\tif ( bySet && i !== matchedCount ) {\n\t\t\t\tj = 0;\n\t\t\t\twhile ( ( matcher = setMatchers[ j++ ] ) ) {\n\t\t\t\t\tmatcher( unmatched, setMatched, context, xml );\n\t\t\t\t}\n\n\t\t\t\tif ( seed ) {\n\n\t\t\t\t\t// Reintegrate element matches to eliminate the need for sorting\n\t\t\t\t\tif ( matchedCount > 0 ) {\n\t\t\t\t\t\twhile ( i-- ) {\n\t\t\t\t\t\t\tif ( !( unmatched[ i ] || setMatched[ i ] ) ) {\n\t\t\t\t\t\t\t\tsetMatched[ i ] = pop.call( results );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// Discard index placeholder values to get only actual matches\n\t\t\t\t\tsetMatched = condense( setMatched );\n\t\t\t\t}\n\n\t\t\t\t// Add matches to results\n\t\t\t\tpush.apply( results, setMatched );\n\n\t\t\t\t// Seedless set matches succeeding multiple successful matchers stipulate sorting\n\t\t\t\tif ( outermost && !seed && setMatched.length > 0 &&\n\t\t\t\t\t( matchedCount + setMatchers.length ) > 1 ) {\n\n\t\t\t\t\tjQuery.uniqueSort( results );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Override manipulation of globals by nested matchers\n\t\t\tif ( outermost ) {\n\t\t\t\tdirruns = dirrunsUnique;\n\t\t\t\toutermostContext = contextBackup;\n\t\t\t}\n\n\t\t\treturn unmatched;\n\t\t};\n\n\treturn bySet ?\n\t\tmarkFunction( superMatcher ) :\n\t\tsuperMatcher;\n}\n\nfunction compile( selector, match /* Internal Use Only */ ) {\n\tvar i,\n\t\tsetMatchers = [],\n\t\telementMatchers = [],\n\t\tcached = compilerCache[ selector + \" \" ];\n\n\tif ( !cached ) {\n\n\t\t// Generate a function of recursive functions that can be used to check each element\n\t\tif ( !match ) {\n\t\t\tmatch = tokenize( selector );\n\t\t}\n\t\ti = match.length;\n\t\twhile ( i-- ) {\n\t\t\tcached = matcherFromTokens( match[ i ] );\n\t\t\tif ( cached[ expando ] ) {\n\t\t\t\tsetMatchers.push( cached );\n\t\t\t} else {\n\t\t\t\telementMatchers.push( cached );\n\t\t\t}\n\t\t}\n\n\t\t// Cache the compiled function\n\t\tcached = compilerCache( selector,\n\t\t\tmatcherFromGroupMatchers( elementMatchers, setMatchers ) );\n\n\t\t// Save selector and tokenization\n\t\tcached.selector = selector;\n\t}\n\treturn cached;\n}\n\n/**\n * A low-level selection function that works with jQuery's compiled\n *  selector functions\n * @param {String|Function} selector A selector or a pre-compiled\n *  selector function built with jQuery selector compile\n * @param {Element} context\n * @param {Array} [results]\n * @param {Array} [seed] A set of elements to match against\n */\nfunction select( selector, context, results, seed ) {\n\tvar i, tokens, token, type, find,\n\t\tcompiled = typeof selector === \"function\" && selector,\n\t\tmatch = !seed && tokenize( ( selector = compiled.selector || selector ) );\n\n\tresults = results || [];\n\n\t// Try to minimize operations if there is only one selector in the list and no seed\n\t// (the latter of which guarantees us context)\n\tif ( match.length === 1 ) {\n\n\t\t// Reduce context if the leading compound selector is an ID\n\t\ttokens = match[ 0 ] = match[ 0 ].slice( 0 );\n\t\tif ( tokens.length > 2 && ( token = tokens[ 0 ] ).type === \"ID\" &&\n\t\t\t\tcontext.nodeType === 9 && documentIsHTML && Expr.relative[ tokens[ 1 ].type ] ) {\n\n\t\t\tcontext = ( Expr.find.ID(\n\t\t\t\ttoken.matches[ 0 ].replace( runescape, funescape ),\n\t\t\t\tcontext\n\t\t\t) || [] )[ 0 ];\n\t\t\tif ( !context ) {\n\t\t\t\treturn results;\n\n\t\t\t// Precompiled matchers will still verify ancestry, so step up a level\n\t\t\t} else if ( compiled ) {\n\t\t\t\tcontext = context.parentNode;\n\t\t\t}\n\n\t\t\tselector = selector.slice( tokens.shift().value.length );\n\t\t}\n\n\t\t// Fetch a seed set for right-to-left matching\n\t\ti = matchExpr.needsContext.test( selector ) ? 0 : tokens.length;\n\t\twhile ( i-- ) {\n\t\t\ttoken = tokens[ i ];\n\n\t\t\t// Abort if we hit a combinator\n\t\t\tif ( Expr.relative[ ( type = token.type ) ] ) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif ( ( find = Expr.find[ type ] ) ) {\n\n\t\t\t\t// Search, expanding context for leading sibling combinators\n\t\t\t\tif ( ( seed = find(\n\t\t\t\t\ttoken.matches[ 0 ].replace( runescape, funescape ),\n\t\t\t\t\trsibling.test( tokens[ 0 ].type ) &&\n\t\t\t\t\t\ttestContext( context.parentNode ) || context\n\t\t\t\t) ) ) {\n\n\t\t\t\t\t// If seed is empty or no tokens remain, we can return early\n\t\t\t\t\ttokens.splice( i, 1 );\n\t\t\t\t\tselector = seed.length && toSelector( tokens );\n\t\t\t\t\tif ( !selector ) {\n\t\t\t\t\t\tpush.apply( results, seed );\n\t\t\t\t\t\treturn results;\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Compile and execute a filtering function if one is not provided\n\t// Provide `match` to avoid retokenization if we modified the selector above\n\t( compiled || compile( selector, match ) )(\n\t\tseed,\n\t\tcontext,\n\t\t!documentIsHTML,\n\t\tresults,\n\t\t!context || rsibling.test( selector ) && testContext( context.parentNode ) || context\n\t);\n\treturn results;\n}\n\n// One-time assignments\n\n// Support: Android <=4.0 - 4.1+\n// Sort stability\nsupport.sortStable = expando.split( \"\" ).sort( sortOrder ).join( \"\" ) === expando;\n\n// Initialize against the default document\nsetDocument();\n\n// Support: Android <=4.0 - 4.1+\n// Detached nodes confoundingly follow *each other*\nsupport.sortDetached = assert( function( el ) {\n\n\t// Should return 1, but returns 4 (following)\n\treturn el.compareDocumentPosition( document.createElement( \"fieldset\" ) ) & 1;\n} );\n\njQuery.find = find;\n\n// Deprecated\njQuery.expr[ \":\" ] = jQuery.expr.pseudos;\njQuery.unique = jQuery.uniqueSort;\n\n// These have always been private, but they used to be documented as part of\n// Sizzle so let's maintain them for now for backwards compatibility purposes.\nfind.compile = compile;\nfind.select = select;\nfind.setDocument = setDocument;\nfind.tokenize = tokenize;\n\nfind.escape = jQuery.escapeSelector;\nfind.getText = jQuery.text;\nfind.isXML = jQuery.isXMLDoc;\nfind.selectors = jQuery.expr;\nfind.support = jQuery.support;\nfind.uniqueSort = jQuery.uniqueSort;\n\n\t/* eslint-enable */\n\n} )();\n\n\nvar dir = function( elem, dir, until ) {\n\tvar matched = [],\n\t\ttruncate = until !== undefined;\n\n\twhile ( ( elem = elem[ dir ] ) && elem.nodeType !== 9 ) {\n\t\tif ( elem.nodeType === 1 ) {\n\t\t\tif ( truncate && jQuery( elem ).is( until ) ) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tmatched.push( elem );\n\t\t}\n\t}\n\treturn matched;\n};\n\n\nvar siblings = function( n, elem ) {\n\tvar matched = [];\n\n\tfor ( ; n; n = n.nextSibling ) {\n\t\tif ( n.nodeType === 1 && n !== elem ) {\n\t\t\tmatched.push( n );\n\t\t}\n\t}\n\n\treturn matched;\n};\n\n\nvar rneedsContext = jQuery.expr.match.needsContext;\n\nvar rsingleTag = ( /^<([a-z][^\\/\\0>:\\x20\\t\\r\\n\\f]*)[\\x20\\t\\r\\n\\f]*\\/?>(?:<\\/\\1>|)$/i );\n\n\n\n// Implement the identical functionality for filter and not\nfunction winnow( elements, qualifier, not ) {\n\tif ( isFunction( qualifier ) ) {\n\t\treturn jQuery.grep( elements, function( elem, i ) {\n\t\t\treturn !!qualifier.call( elem, i, elem ) !== not;\n\t\t} );\n\t}\n\n\t// Single element\n\tif ( qualifier.nodeType ) {\n\t\treturn jQuery.grep( elements, function( elem ) {\n\t\t\treturn ( elem === qualifier ) !== not;\n\t\t} );\n\t}\n\n\t// Arraylike of elements (jQuery, arguments, Array)\n\tif ( typeof qualifier !== \"string\" ) {\n\t\treturn jQuery.grep( elements, function( elem ) {\n\t\t\treturn ( indexOf.call( qualifier, elem ) > -1 ) !== not;\n\t\t} );\n\t}\n\n\t// Filtered directly for both simple and complex selectors\n\treturn jQuery.filter( qualifier, elements, not );\n}\n\njQuery.filter = function( expr, elems, not ) {\n\tvar elem = elems[ 0 ];\n\n\tif ( not ) {\n\t\texpr = \":not(\" + expr + \")\";\n\t}\n\n\tif ( elems.length === 1 && elem.nodeType === 1 ) {\n\t\treturn jQuery.find.matchesSelector( elem, expr ) ? [ elem ] : [];\n\t}\n\n\treturn jQuery.find.matches( expr, jQuery.grep( elems, function( elem ) {\n\t\treturn elem.nodeType === 1;\n\t} ) );\n};\n\njQuery.fn.extend( {\n\tfind: function( selector ) {\n\t\tvar i, ret,\n\t\t\tlen = this.length,\n\t\t\tself = this;\n\n\t\tif ( typeof selector !== \"string\" ) {\n\t\t\treturn this.pushStack( jQuery( selector ).filter( function() {\n\t\t\t\tfor ( i = 0; i < len; i++ ) {\n\t\t\t\t\tif ( jQuery.contains( self[ i ], this ) ) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} ) );\n\t\t}\n\n\t\tret = this.pushStack( [] );\n\n\t\tfor ( i = 0; i < len; i++ ) {\n\t\t\tjQuery.find( selector, self[ i ], ret );\n\t\t}\n\n\t\treturn len > 1 ? jQuery.uniqueSort( ret ) : ret;\n\t},\n\tfilter: function( selector ) {\n\t\treturn this.pushStack( winnow( this, selector || [], false ) );\n\t},\n\tnot: function( selector ) {\n\t\treturn this.pushStack( winnow( this, selector || [], true ) );\n\t},\n\tis: function( selector ) {\n\t\treturn !!winnow(\n\t\t\tthis,\n\n\t\t\t// If this is a positional/relative selector, check membership in the returned set\n\t\t\t// so $(\"p:first\").is(\"p:last\") won't return true for a doc with two \"p\".\n\t\t\ttypeof selector === \"string\" && rneedsContext.test( selector ) ?\n\t\t\t\tjQuery( selector ) :\n\t\t\t\tselector || [],\n\t\t\tfalse\n\t\t).length;\n\t}\n} );\n\n\n// Initialize a jQuery object\n\n\n// A central reference to the root jQuery(document)\nvar rootjQuery,\n\n\t// A simple way to check for HTML strings\n\t// Prioritize #id over <tag> to avoid XSS via location.hash (trac-9521)\n\t// Strict HTML recognition (trac-11290: must start with <)\n\t// Shortcut simple #id case for speed\n\trquickExpr = /^(?:\\s*(<[\\w\\W]+>)[^>]*|#([\\w-]+))$/,\n\n\tinit = jQuery.fn.init = function( selector, context, root ) {\n\t\tvar match, elem;\n\n\t\t// HANDLE: $(\"\"), $(null), $(undefined), $(false)\n\t\tif ( !selector ) {\n\t\t\treturn this;\n\t\t}\n\n\t\t// Method init() accepts an alternate rootjQuery\n\t\t// so migrate can support jQuery.sub (gh-2101)\n\t\troot = root || rootjQuery;\n\n\t\t// Handle HTML strings\n\t\tif ( typeof selector === \"string\" ) {\n\t\t\tif ( selector[ 0 ] === \"<\" &&\n\t\t\t\tselector[ selector.length - 1 ] === \">\" &&\n\t\t\t\tselector.length >= 3 ) {\n\n\t\t\t\t// Assume that strings that start and end with <> are HTML and skip the regex check\n\t\t\t\tmatch = [ null, selector, null ];\n\n\t\t\t} else {\n\t\t\t\tmatch = rquickExpr.exec( selector );\n\t\t\t}\n\n\t\t\t// Match html or make sure no context is specified for #id\n\t\t\tif ( match && ( match[ 1 ] || !context ) ) {\n\n\t\t\t\t// HANDLE: $(html) -> $(array)\n\t\t\t\tif ( match[ 1 ] ) {\n\t\t\t\t\tcontext = context instanceof jQuery ? context[ 0 ] : context;\n\n\t\t\t\t\t// Option to run scripts is true for back-compat\n\t\t\t\t\t// Intentionally let the error be thrown if parseHTML is not present\n\t\t\t\t\tjQuery.merge( this, jQuery.parseHTML(\n\t\t\t\t\t\tmatch[ 1 ],\n\t\t\t\t\t\tcontext && context.nodeType ? context.ownerDocument || context : document,\n\t\t\t\t\t\ttrue\n\t\t\t\t\t) );\n\n\t\t\t\t\t// HANDLE: $(html, props)\n\t\t\t\t\tif ( rsingleTag.test( match[ 1 ] ) && jQuery.isPlainObject( context ) ) {\n\t\t\t\t\t\tfor ( match in context ) {\n\n\t\t\t\t\t\t\t// Properties of context are called as methods if possible\n\t\t\t\t\t\t\tif ( isFunction( this[ match ] ) ) {\n\t\t\t\t\t\t\t\tthis[ match ]( context[ match ] );\n\n\t\t\t\t\t\t\t// ...and otherwise set as attributes\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.attr( match, context[ match ] );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn this;\n\n\t\t\t\t// HANDLE: $(#id)\n\t\t\t\t} else {\n\t\t\t\t\telem = document.getElementById( match[ 2 ] );\n\n\t\t\t\t\tif ( elem ) {\n\n\t\t\t\t\t\t// Inject the element directly into the jQuery object\n\t\t\t\t\t\tthis[ 0 ] = elem;\n\t\t\t\t\t\tthis.length = 1;\n\t\t\t\t\t}\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\n\t\t\t// HANDLE: $(expr, $(...))\n\t\t\t} else if ( !context || context.jquery ) {\n\t\t\t\treturn ( context || root ).find( selector );\n\n\t\t\t// HANDLE: $(expr, context)\n\t\t\t// (which is just equivalent to: $(context).find(expr)\n\t\t\t} else {\n\t\t\t\treturn this.constructor( context ).find( selector );\n\t\t\t}\n\n\t\t// HANDLE: $(DOMElement)\n\t\t} else if ( selector.nodeType ) {\n\t\t\tthis[ 0 ] = selector;\n\t\t\tthis.length = 1;\n\t\t\treturn this;\n\n\t\t// HANDLE: $(function)\n\t\t// Shortcut for document ready\n\t\t} else if ( isFunction( selector ) ) {\n\t\t\treturn root.ready !== undefined ?\n\t\t\t\troot.ready( selector ) :\n\n\t\t\t\t// Execute immediately if ready is not present\n\t\t\t\tselector( jQuery );\n\t\t}\n\n\t\treturn jQuery.makeArray( selector, this );\n\t};\n\n// Give the init function the jQuery prototype for later instantiation\ninit.prototype = jQuery.fn;\n\n// Initialize central reference\nrootjQuery = jQuery( document );\n\n\nvar rparentsprev = /^(?:parents|prev(?:Until|All))/,\n\n\t// Methods guaranteed to produce a unique set when starting from a unique set\n\tguaranteedUnique = {\n\t\tchildren: true,\n\t\tcontents: true,\n\t\tnext: true,\n\t\tprev: true\n\t};\n\njQuery.fn.extend( {\n\thas: function( target ) {\n\t\tvar targets = jQuery( target, this ),\n\t\t\tl = targets.length;\n\n\t\treturn this.filter( function() {\n\t\t\tvar i = 0;\n\t\t\tfor ( ; i < l; i++ ) {\n\t\t\t\tif ( jQuery.contains( this, targets[ i ] ) ) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t} );\n\t},\n\n\tclosest: function( selectors, context ) {\n\t\tvar cur,\n\t\t\ti = 0,\n\t\t\tl = this.length,\n\t\t\tmatched = [],\n\t\t\ttargets = typeof selectors !== \"string\" && jQuery( selectors );\n\n\t\t// Positional selectors never match, since there's no _selection_ context\n\t\tif ( !rneedsContext.test( selectors ) ) {\n\t\t\tfor ( ; i < l; i++ ) {\n\t\t\t\tfor ( cur = this[ i ]; cur && cur !== context; cur = cur.parentNode ) {\n\n\t\t\t\t\t// Always skip document fragments\n\t\t\t\t\tif ( cur.nodeType < 11 && ( targets ?\n\t\t\t\t\t\ttargets.index( cur ) > -1 :\n\n\t\t\t\t\t\t// Don't pass non-elements to jQuery#find\n\t\t\t\t\t\tcur.nodeType === 1 &&\n\t\t\t\t\t\t\tjQuery.find.matchesSelector( cur, selectors ) ) ) {\n\n\t\t\t\t\t\tmatched.push( cur );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn this.pushStack( matched.length > 1 ? jQuery.uniqueSort( matched ) : matched );\n\t},\n\n\t// Determine the position of an element within the set\n\tindex: function( elem ) {\n\n\t\t// No argument, return index in parent\n\t\tif ( !elem ) {\n\t\t\treturn ( this[ 0 ] && this[ 0 ].parentNode ) ? this.first().prevAll().length : -1;\n\t\t}\n\n\t\t// Index in selector\n\t\tif ( typeof elem === \"string\" ) {\n\t\t\treturn indexOf.call( jQuery( elem ), this[ 0 ] );\n\t\t}\n\n\t\t// Locate the position of the desired element\n\t\treturn indexOf.call( this,\n\n\t\t\t// If it receives a jQuery object, the first element is used\n\t\t\telem.jquery ? elem[ 0 ] : elem\n\t\t);\n\t},\n\n\tadd: function( selector, context ) {\n\t\treturn this.pushStack(\n\t\t\tjQuery.uniqueSort(\n\t\t\t\tjQuery.merge( this.get(), jQuery( selector, context ) )\n\t\t\t)\n\t\t);\n\t},\n\n\taddBack: function( selector ) {\n\t\treturn this.add( selector == null ?\n\t\t\tthis.prevObject : this.prevObject.filter( selector )\n\t\t);\n\t}\n} );\n\nfunction sibling( cur, dir ) {\n\twhile ( ( cur = cur[ dir ] ) && cur.nodeType !== 1 ) {}\n\treturn cur;\n}\n\njQuery.each( {\n\tparent: function( elem ) {\n\t\tvar parent = elem.parentNode;\n\t\treturn parent && parent.nodeType !== 11 ? parent : null;\n\t},\n\tparents: function( elem ) {\n\t\treturn dir( elem, \"parentNode\" );\n\t},\n\tparentsUntil: function( elem, _i, until ) {\n\t\treturn dir( elem, \"parentNode\", until );\n\t},\n\tnext: function( elem ) {\n\t\treturn sibling( elem, \"nextSibling\" );\n\t},\n\tprev: function( elem ) {\n\t\treturn sibling( elem, \"previousSibling\" );\n\t},\n\tnextAll: function( elem ) {\n\t\treturn dir( elem, \"nextSibling\" );\n\t},\n\tprevAll: function( elem ) {\n\t\treturn dir( elem, \"previousSibling\" );\n\t},\n\tnextUntil: function( elem, _i, until ) {\n\t\treturn dir( elem, \"nextSibling\", until );\n\t},\n\tprevUntil: function( elem, _i, until ) {\n\t\treturn dir( elem, \"previousSibling\", until );\n\t},\n\tsiblings: function( elem ) {\n\t\treturn siblings( ( elem.parentNode || {} ).firstChild, elem );\n\t},\n\tchildren: function( elem ) {\n\t\treturn siblings( elem.firstChild );\n\t},\n\tcontents: function( elem ) {\n\t\tif ( elem.contentDocument != null &&\n\n\t\t\t// Support: IE 11+\n\t\t\t// <object> elements with no `data` attribute has an object\n\t\t\t// `contentDocument` with a `null` prototype.\n\t\t\tgetProto( elem.contentDocument ) ) {\n\n\t\t\treturn elem.contentDocument;\n\t\t}\n\n\t\t// Support: IE 9 - 11 only, iOS 7 only, Android Browser <=4.3 only\n\t\t// Treat the template element as a regular one in browsers that\n\t\t// don't support it.\n\t\tif ( nodeName( elem, \"template\" ) ) {\n\t\t\telem = elem.content || elem;\n\t\t}\n\n\t\treturn jQuery.merge( [], elem.childNodes );\n\t}\n}, function( name, fn ) {\n\tjQuery.fn[ name ] = function( until, selector ) {\n\t\tvar matched = jQuery.map( this, fn, until );\n\n\t\tif ( name.slice( -5 ) !== \"Until\" ) {\n\t\t\tselector = until;\n\t\t}\n\n\t\tif ( selector && typeof selector === \"string\" ) {\n\t\t\tmatched = jQuery.filter( selector, matched );\n\t\t}\n\n\t\tif ( this.length > 1 ) {\n\n\t\t\t// Remove duplicates\n\t\t\tif ( !guaranteedUnique[ name ] ) {\n\t\t\t\tjQuery.uniqueSort( matched );\n\t\t\t}\n\n\t\t\t// Reverse order for parents* and prev-derivatives\n\t\t\tif ( rparentsprev.test( name ) ) {\n\t\t\t\tmatched.reverse();\n\t\t\t}\n\t\t}\n\n\t\treturn this.pushStack( matched );\n\t};\n} );\nvar rnothtmlwhite = ( /[^\\x20\\t\\r\\n\\f]+/g );\n\n\n\n// Convert String-formatted options into Object-formatted ones\nfunction createOptions( options ) {\n\tvar object = {};\n\tjQuery.each( options.match( rnothtmlwhite ) || [], function( _, flag ) {\n\t\tobject[ flag ] = true;\n\t} );\n\treturn object;\n}\n\n/*\n * Create a callback list using the following parameters:\n *\n *\toptions: an optional list of space-separated options that will change how\n *\t\t\tthe callback list behaves or a more traditional option object\n *\n * By default a callback list will act like an event callback list and can be\n * \"fired\" multiple times.\n *\n * Possible options:\n *\n *\tonce:\t\t\twill ensure the callback list can only be fired once (like a Deferred)\n *\n *\tmemory:\t\t\twill keep track of previous values and will call any callback added\n *\t\t\t\t\tafter the list has been fired right away with the latest \"memorized\"\n *\t\t\t\t\tvalues (like a Deferred)\n *\n *\tunique:\t\t\twill ensure a callback can only be added once (no duplicate in the list)\n *\n *\tstopOnFalse:\tinterrupt callings when a callback returns false\n *\n */\njQuery.Callbacks = function( options ) {\n\n\t// Convert options from String-formatted to Object-formatted if needed\n\t// (we check in cache first)\n\toptions = typeof options === \"string\" ?\n\t\tcreateOptions( options ) :\n\t\tjQuery.extend( {}, options );\n\n\tvar // Flag to know if list is currently firing\n\t\tfiring,\n\n\t\t// Last fire value for non-forgettable lists\n\t\tmemory,\n\n\t\t// Flag to know if list was already fired\n\t\tfired,\n\n\t\t// Flag to prevent firing\n\t\tlocked,\n\n\t\t// Actual callback list\n\t\tlist = [],\n\n\t\t// Queue of execution data for repeatable lists\n\t\tqueue = [],\n\n\t\t// Index of currently firing callback (modified by add/remove as needed)\n\t\tfiringIndex = -1,\n\n\t\t// Fire callbacks\n\t\tfire = function() {\n\n\t\t\t// Enforce single-firing\n\t\t\tlocked = locked || options.once;\n\n\t\t\t// Execute callbacks for all pending executions,\n\t\t\t// respecting firingIndex overrides and runtime changes\n\t\t\tfired = firing = true;\n\t\t\tfor ( ; queue.length; firingIndex = -1 ) {\n\t\t\t\tmemory = queue.shift();\n\t\t\t\twhile ( ++firingIndex < list.length ) {\n\n\t\t\t\t\t// Run callback and check for early termination\n\t\t\t\t\tif ( list[ firingIndex ].apply( memory[ 0 ], memory[ 1 ] ) === false &&\n\t\t\t\t\t\toptions.stopOnFalse ) {\n\n\t\t\t\t\t\t// Jump to end and forget the data so .add doesn't re-fire\n\t\t\t\t\t\tfiringIndex = list.length;\n\t\t\t\t\t\tmemory = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Forget the data if we're done with it\n\t\t\tif ( !options.memory ) {\n\t\t\t\tmemory = false;\n\t\t\t}\n\n\t\t\tfiring = false;\n\n\t\t\t// Clean up if we're done firing for good\n\t\t\tif ( locked ) {\n\n\t\t\t\t// Keep an empty list if we have data for future add calls\n\t\t\t\tif ( memory ) {\n\t\t\t\t\tlist = [];\n\n\t\t\t\t// Otherwise, this object is spent\n\t\t\t\t} else {\n\t\t\t\t\tlist = \"\";\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t// Actual Callbacks object\n\t\tself = {\n\n\t\t\t// Add a callback or a collection of callbacks to the list\n\t\t\tadd: function() {\n\t\t\t\tif ( list ) {\n\n\t\t\t\t\t// If we have memory from a past run, we should fire after adding\n\t\t\t\t\tif ( memory && !firing ) {\n\t\t\t\t\t\tfiringIndex = list.length - 1;\n\t\t\t\t\t\tqueue.push( memory );\n\t\t\t\t\t}\n\n\t\t\t\t\t( function add( args ) {\n\t\t\t\t\t\tjQuery.each( args, function( _, arg ) {\n\t\t\t\t\t\t\tif ( isFunction( arg ) ) {\n\t\t\t\t\t\t\t\tif ( !options.unique || !self.has( arg ) ) {\n\t\t\t\t\t\t\t\t\tlist.push( arg );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else if ( arg && arg.length && toType( arg ) !== \"string\" ) {\n\n\t\t\t\t\t\t\t\t// Inspect recursively\n\t\t\t\t\t\t\t\tadd( arg );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} );\n\t\t\t\t\t} )( arguments );\n\n\t\t\t\t\tif ( memory && !firing ) {\n\t\t\t\t\t\tfire();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t},\n\n\t\t\t// Remove a callback from the list\n\t\t\tremove: function() {\n\t\t\t\tjQuery.each( arguments, function( _, arg ) {\n\t\t\t\t\tvar index;\n\t\t\t\t\twhile ( ( index = jQuery.inArray( arg, list, index ) ) > -1 ) {\n\t\t\t\t\t\tlist.splice( index, 1 );\n\n\t\t\t\t\t\t// Handle firing indexes\n\t\t\t\t\t\tif ( index <= firingIndex ) {\n\t\t\t\t\t\t\tfiringIndex--;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} );\n\t\t\t\treturn this;\n\t\t\t},\n\n\t\t\t// Check if a given callback is in the list.\n\t\t\t// If no argument is given, return whether or not list has callbacks attached.\n\t\t\thas: function( fn ) {\n\t\t\t\treturn fn ?\n\t\t\t\t\tjQuery.inArray( fn, list ) > -1 :\n\t\t\t\t\tlist.length > 0;\n\t\t\t},\n\n\t\t\t// Remove all callbacks from the list\n\t\t\tempty: function() {\n\t\t\t\tif ( list ) {\n\t\t\t\t\tlist = [];\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t},\n\n\t\t\t// Disable .fire and .add\n\t\t\t// Abort any current/pending executions\n\t\t\t// Clear all callbacks and values\n\t\t\tdisable: function() {\n\t\t\t\tlocked = queue = [];\n\t\t\t\tlist = memory = \"\";\n\t\t\t\treturn this;\n\t\t\t},\n\t\t\tdisabled: function() {\n\t\t\t\treturn !list;\n\t\t\t},\n\n\t\t\t// Disable .fire\n\t\t\t// Also disable .add unless we have memory (since it would have no effect)\n\t\t\t// Abort any pending executions\n\t\t\tlock: function() {\n\t\t\t\tlocked = queue = [];\n\t\t\t\tif ( !memory && !firing ) {\n\t\t\t\t\tlist = memory = \"\";\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t},\n\t\t\tlocked: function() {\n\t\t\t\treturn !!locked;\n\t\t\t},\n\n\t\t\t// Call all callbacks with the given context and arguments\n\t\t\tfireWith: function( context, args ) {\n\t\t\t\tif ( !locked ) {\n\t\t\t\t\targs = args || [];\n\t\t\t\t\targs = [ context, args.slice ? args.slice() : args ];\n\t\t\t\t\tqueue.push( args );\n\t\t\t\t\tif ( !firing ) {\n\t\t\t\t\t\tfire();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t},\n\n\t\t\t// Call all the callbacks with the given arguments\n\t\t\tfire: function() {\n\t\t\t\tself.fireWith( this, arguments );\n\t\t\t\treturn this;\n\t\t\t},\n\n\t\t\t// To know if the callbacks have already been called at least once\n\t\t\tfired: function() {\n\t\t\t\treturn !!fired;\n\t\t\t}\n\t\t};\n\n\treturn self;\n};\n\n\nfunction Identity( v ) {\n\treturn v;\n}\nfunction Thrower( ex ) {\n\tthrow ex;\n}\n\nfunction adoptValue( value, resolve, reject, noValue ) {\n\tvar method;\n\n\ttry {\n\n\t\t// Check for promise aspect first to privilege synchronous behavior\n\t\tif ( value && isFunction( ( method = value.promise ) ) ) {\n\t\t\tmethod.call( value ).done( resolve ).fail( reject );\n\n\t\t// Other thenables\n\t\t} else if ( value && isFunction( ( method = value.then ) ) ) {\n\t\t\tmethod.call( value, resolve, reject );\n\n\t\t// Other non-thenables\n\t\t} else {\n\n\t\t\t// Control `resolve` arguments by letting Array#slice cast boolean `noValue` to integer:\n\t\t\t// * false: [ value ].slice( 0 ) => resolve( value )\n\t\t\t// * true: [ value ].slice( 1 ) => resolve()\n\t\t\tresolve.apply( undefined, [ value ].slice( noValue ) );\n\t\t}\n\n\t// For Promises/A+, convert exceptions into rejections\n\t// Since jQuery.when doesn't unwrap thenables, we can skip the extra checks appearing in\n\t// Deferred#then to conditionally suppress rejection.\n\t} catch ( value ) {\n\n\t\t// Support: Android 4.0 only\n\t\t// Strict mode functions invoked without .call/.apply get global-object context\n\t\treject.apply( undefined, [ value ] );\n\t}\n}\n\njQuery.extend( {\n\n\tDeferred: function( func ) {\n\t\tvar tuples = [\n\n\t\t\t\t// action, add listener, callbacks,\n\t\t\t\t// ... .then handlers, argument index, [final state]\n\t\t\t\t[ \"notify\", \"progress\", jQuery.Callbacks( \"memory\" ),\n\t\t\t\t\tjQuery.Callbacks( \"memory\" ), 2 ],\n\t\t\t\t[ \"resolve\", \"done\", jQuery.Callbacks( \"once memory\" ),\n\t\t\t\t\tjQuery.Callbacks( \"once memory\" ), 0, \"resolved\" ],\n\t\t\t\t[ \"reject\", \"fail\", jQuery.Callbacks( \"once memory\" ),\n\t\t\t\t\tjQuery.Callbacks( \"once memory\" ), 1, \"rejected\" ]\n\t\t\t],\n\t\t\tstate = \"pending\",\n\t\t\tpromise = {\n\t\t\t\tstate: function() {\n\t\t\t\t\treturn state;\n\t\t\t\t},\n\t\t\t\talways: function() {\n\t\t\t\t\tdeferred.done( arguments ).fail( arguments );\n\t\t\t\t\treturn this;\n\t\t\t\t},\n\t\t\t\t\"catch\": function( fn ) {\n\t\t\t\t\treturn promise.then( null, fn );\n\t\t\t\t},\n\n\t\t\t\t// Keep pipe for back-compat\n\t\t\t\tpipe: function( /* fnDone, fnFail, fnProgress */ ) {\n\t\t\t\t\tvar fns = arguments;\n\n\t\t\t\t\treturn jQuery.Deferred( function( newDefer ) {\n\t\t\t\t\t\tjQuery.each( tuples, function( _i, tuple ) {\n\n\t\t\t\t\t\t\t// Map tuples (progress, done, fail) to arguments (done, fail, progress)\n\t\t\t\t\t\t\tvar fn = isFunction( fns[ tuple[ 4 ] ] ) && fns[ tuple[ 4 ] ];\n\n\t\t\t\t\t\t\t// deferred.progress(function() { bind to newDefer or newDefer.notify })\n\t\t\t\t\t\t\t// deferred.done(function() { bind to newDefer or newDefer.resolve })\n\t\t\t\t\t\t\t// deferred.fail(function() { bind to newDefer or newDefer.reject })\n\t\t\t\t\t\t\tdeferred[ tuple[ 1 ] ]( function() {\n\t\t\t\t\t\t\t\tvar returned = fn && fn.apply( this, arguments );\n\t\t\t\t\t\t\t\tif ( returned && isFunction( returned.promise ) ) {\n\t\t\t\t\t\t\t\t\treturned.promise()\n\t\t\t\t\t\t\t\t\t\t.progress( newDefer.notify )\n\t\t\t\t\t\t\t\t\t\t.done( newDefer.resolve )\n\t\t\t\t\t\t\t\t\t\t.fail( newDefer.reject );\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tnewDefer[ tuple[ 0 ] + \"With\" ](\n\t\t\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t\t\tfn ? [ returned ] : arguments\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} );\n\t\t\t\t\t\t} );\n\t\t\t\t\t\tfns = null;\n\t\t\t\t\t} ).promise();\n\t\t\t\t},\n\t\t\t\tthen: function( onFulfilled, onRejected, onProgress ) {\n\t\t\t\t\tvar maxDepth = 0;\n\t\t\t\t\tfunction resolve( depth, deferred, handler, special ) {\n\t\t\t\t\t\treturn function() {\n\t\t\t\t\t\t\tvar that = this,\n\t\t\t\t\t\t\t\targs = arguments,\n\t\t\t\t\t\t\t\tmightThrow = function() {\n\t\t\t\t\t\t\t\t\tvar returned, then;\n\n\t\t\t\t\t\t\t\t\t// Support: Promises/A+ section *******.3\n\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-59\n\t\t\t\t\t\t\t\t\t// Ignore double-resolution attempts\n\t\t\t\t\t\t\t\t\tif ( depth < maxDepth ) {\n\t\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\treturned = handler.apply( that, args );\n\n\t\t\t\t\t\t\t\t\t// Support: Promises/A+ section 2.3.1\n\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-48\n\t\t\t\t\t\t\t\t\tif ( returned === deferred.promise() ) {\n\t\t\t\t\t\t\t\t\t\tthrow new TypeError( \"Thenable self-resolution\" );\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t// Support: Promises/A+ sections 2.3.3.1, 3.5\n\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-54\n\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-75\n\t\t\t\t\t\t\t\t\t// Retrieve `then` only once\n\t\t\t\t\t\t\t\t\tthen = returned &&\n\n\t\t\t\t\t\t\t\t\t\t// Support: Promises/A+ section 2.3.4\n\t\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-64\n\t\t\t\t\t\t\t\t\t\t// Only check objects and functions for thenability\n\t\t\t\t\t\t\t\t\t\t( typeof returned === \"object\" ||\n\t\t\t\t\t\t\t\t\t\t\ttypeof returned === \"function\" ) &&\n\t\t\t\t\t\t\t\t\t\treturned.then;\n\n\t\t\t\t\t\t\t\t\t// Handle a returned thenable\n\t\t\t\t\t\t\t\t\tif ( isFunction( then ) ) {\n\n\t\t\t\t\t\t\t\t\t\t// Special processors (notify) just wait for resolution\n\t\t\t\t\t\t\t\t\t\tif ( special ) {\n\t\t\t\t\t\t\t\t\t\t\tthen.call(\n\t\t\t\t\t\t\t\t\t\t\t\treturned,\n\t\t\t\t\t\t\t\t\t\t\t\tresolve( maxDepth, deferred, Identity, special ),\n\t\t\t\t\t\t\t\t\t\t\t\tresolve( maxDepth, deferred, Thrower, special )\n\t\t\t\t\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\t\t\t\t// Normal processors (resolve) also hook into progress\n\t\t\t\t\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t\t\t\t\t// ...and disregard older resolution values\n\t\t\t\t\t\t\t\t\t\t\tmaxDepth++;\n\n\t\t\t\t\t\t\t\t\t\t\tthen.call(\n\t\t\t\t\t\t\t\t\t\t\t\treturned,\n\t\t\t\t\t\t\t\t\t\t\t\tresolve( maxDepth, deferred, Identity, special ),\n\t\t\t\t\t\t\t\t\t\t\t\tresolve( maxDepth, deferred, Thrower, special ),\n\t\t\t\t\t\t\t\t\t\t\t\tresolve( maxDepth, deferred, Identity,\n\t\t\t\t\t\t\t\t\t\t\t\t\tdeferred.notifyWith )\n\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t// Handle all other returned values\n\t\t\t\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t\t\t\t// Only substitute handlers pass on context\n\t\t\t\t\t\t\t\t\t\t// and multiple values (non-spec behavior)\n\t\t\t\t\t\t\t\t\t\tif ( handler !== Identity ) {\n\t\t\t\t\t\t\t\t\t\t\tthat = undefined;\n\t\t\t\t\t\t\t\t\t\t\targs = [ returned ];\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t// Process the value(s)\n\t\t\t\t\t\t\t\t\t\t// Default process is resolve\n\t\t\t\t\t\t\t\t\t\t( special || deferred.resolveWith )( that, args );\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t},\n\n\t\t\t\t\t\t\t\t// Only normal processors (resolve) catch and reject exceptions\n\t\t\t\t\t\t\t\tprocess = special ?\n\t\t\t\t\t\t\t\t\tmightThrow :\n\t\t\t\t\t\t\t\t\tfunction() {\n\t\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t\tmightThrow();\n\t\t\t\t\t\t\t\t\t\t} catch ( e ) {\n\n\t\t\t\t\t\t\t\t\t\t\tif ( jQuery.Deferred.exceptionHook ) {\n\t\t\t\t\t\t\t\t\t\t\t\tjQuery.Deferred.exceptionHook( e,\n\t\t\t\t\t\t\t\t\t\t\t\t\tprocess.error );\n\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t\t// Support: Promises/A+ section *******.4.1\n\t\t\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-61\n\t\t\t\t\t\t\t\t\t\t\t// Ignore post-resolution exceptions\n\t\t\t\t\t\t\t\t\t\t\tif ( depth + 1 >= maxDepth ) {\n\n\t\t\t\t\t\t\t\t\t\t\t\t// Only substitute handlers pass on context\n\t\t\t\t\t\t\t\t\t\t\t\t// and multiple values (non-spec behavior)\n\t\t\t\t\t\t\t\t\t\t\t\tif ( handler !== Thrower ) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tthat = undefined;\n\t\t\t\t\t\t\t\t\t\t\t\t\targs = [ e ];\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t\t\tdeferred.rejectWith( that, args );\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t};\n\n\t\t\t\t\t\t\t// Support: Promises/A+ section *******.1\n\t\t\t\t\t\t\t// https://promisesaplus.com/#point-57\n\t\t\t\t\t\t\t// Re-resolve promises immediately to dodge false rejection from\n\t\t\t\t\t\t\t// subsequent errors\n\t\t\t\t\t\t\tif ( depth ) {\n\t\t\t\t\t\t\t\tprocess();\n\t\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t\t// Call an optional hook to record the error, in case of exception\n\t\t\t\t\t\t\t\t// since it's otherwise lost when execution goes async\n\t\t\t\t\t\t\t\tif ( jQuery.Deferred.getErrorHook ) {\n\t\t\t\t\t\t\t\t\tprocess.error = jQuery.Deferred.getErrorHook();\n\n\t\t\t\t\t\t\t\t// The deprecated alias of the above. While the name suggests\n\t\t\t\t\t\t\t\t// returning the stack, not an error instance, jQuery just passes\n\t\t\t\t\t\t\t\t// it directly to `console.warn` so both will work; an instance\n\t\t\t\t\t\t\t\t// just better cooperates with source maps.\n\t\t\t\t\t\t\t\t} else if ( jQuery.Deferred.getStackHook ) {\n\t\t\t\t\t\t\t\t\tprocess.error = jQuery.Deferred.getStackHook();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\twindow.setTimeout( process );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\treturn jQuery.Deferred( function( newDefer ) {\n\n\t\t\t\t\t\t// progress_handlers.add( ... )\n\t\t\t\t\t\ttuples[ 0 ][ 3 ].add(\n\t\t\t\t\t\t\tresolve(\n\t\t\t\t\t\t\t\t0,\n\t\t\t\t\t\t\t\tnewDefer,\n\t\t\t\t\t\t\t\tisFunction( onProgress ) ?\n\t\t\t\t\t\t\t\t\tonProgress :\n\t\t\t\t\t\t\t\t\tIdentity,\n\t\t\t\t\t\t\t\tnewDefer.notifyWith\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\t// fulfilled_handlers.add( ... )\n\t\t\t\t\t\ttuples[ 1 ][ 3 ].add(\n\t\t\t\t\t\t\tresolve(\n\t\t\t\t\t\t\t\t0,\n\t\t\t\t\t\t\t\tnewDefer,\n\t\t\t\t\t\t\t\tisFunction( onFulfilled ) ?\n\t\t\t\t\t\t\t\t\tonFulfilled :\n\t\t\t\t\t\t\t\t\tIdentity\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\t// rejected_handlers.add( ... )\n\t\t\t\t\t\ttuples[ 2 ][ 3 ].add(\n\t\t\t\t\t\t\tresolve(\n\t\t\t\t\t\t\t\t0,\n\t\t\t\t\t\t\t\tnewDefer,\n\t\t\t\t\t\t\t\tisFunction( onRejected ) ?\n\t\t\t\t\t\t\t\t\tonRejected :\n\t\t\t\t\t\t\t\t\tThrower\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t);\n\t\t\t\t\t} ).promise();\n\t\t\t\t},\n\n\t\t\t\t// Get a promise for this deferred\n\t\t\t\t// If obj is provided, the promise aspect is added to the object\n\t\t\t\tpromise: function( obj ) {\n\t\t\t\t\treturn obj != null ? jQuery.extend( obj, promise ) : promise;\n\t\t\t\t}\n\t\t\t},\n\t\t\tdeferred = {};\n\n\t\t// Add list-specific methods\n\t\tjQuery.each( tuples, function( i, tuple ) {\n\t\t\tvar list = tuple[ 2 ],\n\t\t\t\tstateString = tuple[ 5 ];\n\n\t\t\t// promise.progress = list.add\n\t\t\t// promise.done = list.add\n\t\t\t// promise.fail = list.add\n\t\t\tpromise[ tuple[ 1 ] ] = list.add;\n\n\t\t\t// Handle state\n\t\t\tif ( stateString ) {\n\t\t\t\tlist.add(\n\t\t\t\t\tfunction() {\n\n\t\t\t\t\t\t// state = \"resolved\" (i.e., fulfilled)\n\t\t\t\t\t\t// state = \"rejected\"\n\t\t\t\t\t\tstate = stateString;\n\t\t\t\t\t},\n\n\t\t\t\t\t// rejected_callbacks.disable\n\t\t\t\t\t// fulfilled_callbacks.disable\n\t\t\t\t\ttuples[ 3 - i ][ 2 ].disable,\n\n\t\t\t\t\t// rejected_handlers.disable\n\t\t\t\t\t// fulfilled_handlers.disable\n\t\t\t\t\ttuples[ 3 - i ][ 3 ].disable,\n\n\t\t\t\t\t// progress_callbacks.lock\n\t\t\t\t\ttuples[ 0 ][ 2 ].lock,\n\n\t\t\t\t\t// progress_handlers.lock\n\t\t\t\t\ttuples[ 0 ][ 3 ].lock\n\t\t\t\t);\n\t\t\t}\n\n\t\t\t// progress_handlers.fire\n\t\t\t// fulfilled_handlers.fire\n\t\t\t// rejected_handlers.fire\n\t\t\tlist.add( tuple[ 3 ].fire );\n\n\t\t\t// deferred.notify = function() { deferred.notifyWith(...) }\n\t\t\t// deferred.resolve = function() { deferred.resolveWith(...) }\n\t\t\t// deferred.reject = function() { deferred.rejectWith(...) }\n\t\t\tdeferred[ tuple[ 0 ] ] = function() {\n\t\t\t\tdeferred[ tuple[ 0 ] + \"With\" ]( this === deferred ? undefined : this, arguments );\n\t\t\t\treturn this;\n\t\t\t};\n\n\t\t\t// deferred.notifyWith = list.fireWith\n\t\t\t// deferred.resolveWith = list.fireWith\n\t\t\t// deferred.rejectWith = list.fireWith\n\t\t\tdeferred[ tuple[ 0 ] + \"With\" ] = list.fireWith;\n\t\t} );\n\n\t\t// Make the deferred a promise\n\t\tpromise.promise( deferred );\n\n\t\t// Call given func if any\n\t\tif ( func ) {\n\t\t\tfunc.call( deferred, deferred );\n\t\t}\n\n\t\t// All done!\n\t\treturn deferred;\n\t},\n\n\t// Deferred helper\n\twhen: function( singleValue ) {\n\t\tvar\n\n\t\t\t// count of uncompleted subordinates\n\t\t\tremaining = arguments.length,\n\n\t\t\t// count of unprocessed arguments\n\t\t\ti = remaining,\n\n\t\t\t// subordinate fulfillment data\n\t\t\tresolveContexts = Array( i ),\n\t\t\tresolveValues = slice.call( arguments ),\n\n\t\t\t// the primary Deferred\n\t\t\tprimary = jQuery.Deferred(),\n\n\t\t\t// subordinate callback factory\n\t\t\tupdateFunc = function( i ) {\n\t\t\t\treturn function( value ) {\n\t\t\t\t\tresolveContexts[ i ] = this;\n\t\t\t\t\tresolveValues[ i ] = arguments.length > 1 ? slice.call( arguments ) : value;\n\t\t\t\t\tif ( !( --remaining ) ) {\n\t\t\t\t\t\tprimary.resolveWith( resolveContexts, resolveValues );\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t};\n\n\t\t// Single- and empty arguments are adopted like Promise.resolve\n\t\tif ( remaining <= 1 ) {\n\t\t\tadoptValue( singleValue, primary.done( updateFunc( i ) ).resolve, primary.reject,\n\t\t\t\t!remaining );\n\n\t\t\t// Use .then() to unwrap secondary thenables (cf. gh-3000)\n\t\t\tif ( primary.state() === \"pending\" ||\n\t\t\t\tisFunction( resolveValues[ i ] && resolveValues[ i ].then ) ) {\n\n\t\t\t\treturn primary.then();\n\t\t\t}\n\t\t}\n\n\t\t// Multiple arguments are aggregated like Promise.all array elements\n\t\twhile ( i-- ) {\n\t\t\tadoptValue( resolveValues[ i ], updateFunc( i ), primary.reject );\n\t\t}\n\n\t\treturn primary.promise();\n\t}\n} );\n\n\n// These usually indicate a programmer mistake during development,\n// warn about them ASAP rather than swallowing them by default.\nvar rerrorNames = /^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;\n\n// If `jQuery.Deferred.getErrorHook` is defined, `asyncError` is an error\n// captured before the async barrier to get the original error cause\n// which may otherwise be hidden.\njQuery.Deferred.exceptionHook = function( error, asyncError ) {\n\n\t// Support: IE 8 - 9 only\n\t// Console exists when dev tools are open, which can happen at any time\n\tif ( window.console && window.console.warn && error && rerrorNames.test( error.name ) ) {\n\t\twindow.console.warn( \"jQuery.Deferred exception: \" + error.message,\n\t\t\terror.stack, asyncError );\n\t}\n};\n\n\n\n\njQuery.readyException = function( error ) {\n\twindow.setTimeout( function() {\n\t\tthrow error;\n\t} );\n};\n\n\n\n\n// The deferred used on DOM ready\nvar readyList = jQuery.Deferred();\n\njQuery.fn.ready = function( fn ) {\n\n\treadyList\n\t\t.then( fn )\n\n\t\t// Wrap jQuery.readyException in a function so that the lookup\n\t\t// happens at the time of error handling instead of callback\n\t\t// registration.\n\t\t.catch( function( error ) {\n\t\t\tjQuery.readyException( error );\n\t\t} );\n\n\treturn this;\n};\n\njQuery.extend( {\n\n\t// Is the DOM ready to be used? Set to true once it occurs.\n\tisReady: false,\n\n\t// A counter to track how many items to wait for before\n\t// the ready event fires. See trac-6781\n\treadyWait: 1,\n\n\t// Handle when the DOM is ready\n\tready: function( wait ) {\n\n\t\t// Abort if there are pending holds or we're already ready\n\t\tif ( wait === true ? --jQuery.readyWait : jQuery.isReady ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Remember that the DOM is ready\n\t\tjQuery.isReady = true;\n\n\t\t// If a normal DOM Ready event fired, decrement, and wait if need be\n\t\tif ( wait !== true && --jQuery.readyWait > 0 ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// If there are functions bound, to execute\n\t\treadyList.resolveWith( document, [ jQuery ] );\n\t}\n} );\n\njQuery.ready.then = readyList.then;\n\n// The ready event handler and self cleanup method\nfunction completed() {\n\tdocument.removeEventListener( \"DOMContentLoaded\", completed );\n\twindow.removeEventListener( \"load\", completed );\n\tjQuery.ready();\n}\n\n// Catch cases where $(document).ready() is called\n// after the browser event has already occurred.\n// Support: IE <=9 - 10 only\n// Older IE sometimes signals \"interactive\" too soon\nif ( document.readyState === \"complete\" ||\n\t( document.readyState !== \"loading\" && !document.documentElement.doScroll ) ) {\n\n\t// Handle it asynchronously to allow scripts the opportunity to delay ready\n\twindow.setTimeout( jQuery.ready );\n\n} else {\n\n\t// Use the handy event callback\n\tdocument.addEventListener( \"DOMContentLoaded\", completed );\n\n\t// A fallback to window.onload, that will always work\n\twindow.addEventListener( \"load\", completed );\n}\n\n\n\n\n// Multifunctional method to get and set values of a collection\n// The value/s can optionally be executed if it's a function\nvar access = function( elems, fn, key, value, chainable, emptyGet, raw ) {\n\tvar i = 0,\n\t\tlen = elems.length,\n\t\tbulk = key == null;\n\n\t// Sets many values\n\tif ( toType( key ) === \"object\" ) {\n\t\tchainable = true;\n\t\tfor ( i in key ) {\n\t\t\taccess( elems, fn, i, key[ i ], true, emptyGet, raw );\n\t\t}\n\n\t// Sets one value\n\t} else if ( value !== undefined ) {\n\t\tchainable = true;\n\n\t\tif ( !isFunction( value ) ) {\n\t\t\traw = true;\n\t\t}\n\n\t\tif ( bulk ) {\n\n\t\t\t// Bulk operations run against the entire set\n\t\t\tif ( raw ) {\n\t\t\t\tfn.call( elems, value );\n\t\t\t\tfn = null;\n\n\t\t\t// ...except when executing function values\n\t\t\t} else {\n\t\t\t\tbulk = fn;\n\t\t\t\tfn = function( elem, _key, value ) {\n\t\t\t\t\treturn bulk.call( jQuery( elem ), value );\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\tif ( fn ) {\n\t\t\tfor ( ; i < len; i++ ) {\n\t\t\t\tfn(\n\t\t\t\t\telems[ i ], key, raw ?\n\t\t\t\t\t\tvalue :\n\t\t\t\t\t\tvalue.call( elems[ i ], i, fn( elems[ i ], key ) )\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tif ( chainable ) {\n\t\treturn elems;\n\t}\n\n\t// Gets\n\tif ( bulk ) {\n\t\treturn fn.call( elems );\n\t}\n\n\treturn len ? fn( elems[ 0 ], key ) : emptyGet;\n};\n\n\n// Matches dashed string for camelizing\nvar rmsPrefix = /^-ms-/,\n\trdashAlpha = /-([a-z])/g;\n\n// Used by camelCase as callback to replace()\nfunction fcamelCase( _all, letter ) {\n\treturn letter.toUpperCase();\n}\n\n// Convert dashed to camelCase; used by the css and data modules\n// Support: IE <=9 - 11, Edge 12 - 15\n// Microsoft forgot to hump their vendor prefix (trac-9572)\nfunction camelCase( string ) {\n\treturn string.replace( rmsPrefix, \"ms-\" ).replace( rdashAlpha, fcamelCase );\n}\nvar acceptData = function( owner ) {\n\n\t// Accepts only:\n\t//  - Node\n\t//    - Node.ELEMENT_NODE\n\t//    - Node.DOCUMENT_NODE\n\t//  - Object\n\t//    - Any\n\treturn owner.nodeType === 1 || owner.nodeType === 9 || !( +owner.nodeType );\n};\n\n\n\n\nfunction Data() {\n\tthis.expando = jQuery.expando + Data.uid++;\n}\n\nData.uid = 1;\n\nData.prototype = {\n\n\tcache: function( owner ) {\n\n\t\t// Check if the owner object already has a cache\n\t\tvar value = owner[ this.expando ];\n\n\t\t// If not, create one\n\t\tif ( !value ) {\n\t\t\tvalue = {};\n\n\t\t\t// We can accept data for non-element nodes in modern browsers,\n\t\t\t// but we should not, see trac-8335.\n\t\t\t// Always return an empty object.\n\t\t\tif ( acceptData( owner ) ) {\n\n\t\t\t\t// If it is a node unlikely to be stringify-ed or looped over\n\t\t\t\t// use plain assignment\n\t\t\t\tif ( owner.nodeType ) {\n\t\t\t\t\towner[ this.expando ] = value;\n\n\t\t\t\t// Otherwise secure it in a non-enumerable property\n\t\t\t\t// configurable must be true to allow the property to be\n\t\t\t\t// deleted when data is removed\n\t\t\t\t} else {\n\t\t\t\t\tObject.defineProperty( owner, this.expando, {\n\t\t\t\t\t\tvalue: value,\n\t\t\t\t\t\tconfigurable: true\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn value;\n\t},\n\tset: function( owner, data, value ) {\n\t\tvar prop,\n\t\t\tcache = this.cache( owner );\n\n\t\t// Handle: [ owner, key, value ] args\n\t\t// Always use camelCase key (gh-2257)\n\t\tif ( typeof data === \"string\" ) {\n\t\t\tcache[ camelCase( data ) ] = value;\n\n\t\t// Handle: [ owner, { properties } ] args\n\t\t} else {\n\n\t\t\t// Copy the properties one-by-one to the cache object\n\t\t\tfor ( prop in data ) {\n\t\t\t\tcache[ camelCase( prop ) ] = data[ prop ];\n\t\t\t}\n\t\t}\n\t\treturn cache;\n\t},\n\tget: function( owner, key ) {\n\t\treturn key === undefined ?\n\t\t\tthis.cache( owner ) :\n\n\t\t\t// Always use camelCase key (gh-2257)\n\t\t\towner[ this.expando ] && owner[ this.expando ][ camelCase( key ) ];\n\t},\n\taccess: function( owner, key, value ) {\n\n\t\t// In cases where either:\n\t\t//\n\t\t//   1. No key was specified\n\t\t//   2. A string key was specified, but no value provided\n\t\t//\n\t\t// Take the \"read\" path and allow the get method to determine\n\t\t// which value to return, respectively either:\n\t\t//\n\t\t//   1. The entire cache object\n\t\t//   2. The data stored at the key\n\t\t//\n\t\tif ( key === undefined ||\n\t\t\t\t( ( key && typeof key === \"string\" ) && value === undefined ) ) {\n\n\t\t\treturn this.get( owner, key );\n\t\t}\n\n\t\t// When the key is not a string, or both a key and value\n\t\t// are specified, set or extend (existing objects) with either:\n\t\t//\n\t\t//   1. An object of properties\n\t\t//   2. A key and value\n\t\t//\n\t\tthis.set( owner, key, value );\n\n\t\t// Since the \"set\" path can have two possible entry points\n\t\t// return the expected data based on which path was taken[*]\n\t\treturn value !== undefined ? value : key;\n\t},\n\tremove: function( owner, key ) {\n\t\tvar i,\n\t\t\tcache = owner[ this.expando ];\n\n\t\tif ( cache === undefined ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( key !== undefined ) {\n\n\t\t\t// Support array or space separated string of keys\n\t\t\tif ( Array.isArray( key ) ) {\n\n\t\t\t\t// If key is an array of keys...\n\t\t\t\t// We always set camelCase keys, so remove that.\n\t\t\t\tkey = key.map( camelCase );\n\t\t\t} else {\n\t\t\t\tkey = camelCase( key );\n\n\t\t\t\t// If a key with the spaces exists, use it.\n\t\t\t\t// Otherwise, create an array by matching non-whitespace\n\t\t\t\tkey = key in cache ?\n\t\t\t\t\t[ key ] :\n\t\t\t\t\t( key.match( rnothtmlwhite ) || [] );\n\t\t\t}\n\n\t\t\ti = key.length;\n\n\t\t\twhile ( i-- ) {\n\t\t\t\tdelete cache[ key[ i ] ];\n\t\t\t}\n\t\t}\n\n\t\t// Remove the expando if there's no more data\n\t\tif ( key === undefined || jQuery.isEmptyObject( cache ) ) {\n\n\t\t\t// Support: Chrome <=35 - 45\n\t\t\t// Webkit & Blink performance suffers when deleting properties\n\t\t\t// from DOM nodes, so set to undefined instead\n\t\t\t// https://bugs.chromium.org/p/chromium/issues/detail?id=378607 (bug restricted)\n\t\t\tif ( owner.nodeType ) {\n\t\t\t\towner[ this.expando ] = undefined;\n\t\t\t} else {\n\t\t\t\tdelete owner[ this.expando ];\n\t\t\t}\n\t\t}\n\t},\n\thasData: function( owner ) {\n\t\tvar cache = owner[ this.expando ];\n\t\treturn cache !== undefined && !jQuery.isEmptyObject( cache );\n\t}\n};\nvar dataPriv = new Data();\n\nvar dataUser = new Data();\n\n\n\n//\tImplementation Summary\n//\n//\t1. Enforce API surface and semantic compatibility with 1.9.x branch\n//\t2. Improve the module's maintainability by reducing the storage\n//\t\tpaths to a single mechanism.\n//\t3. Use the same single mechanism to support \"private\" and \"user\" data.\n//\t4. _Never_ expose \"private\" data to user code (TODO: Drop _data, _removeData)\n//\t5. Avoid exposing implementation details on user objects (eg. expando properties)\n//\t6. Provide a clear path for implementation upgrade to WeakMap in 2014\n\nvar rbrace = /^(?:\\{[\\w\\W]*\\}|\\[[\\w\\W]*\\])$/,\n\trmultiDash = /[A-Z]/g;\n\nfunction getData( data ) {\n\tif ( data === \"true\" ) {\n\t\treturn true;\n\t}\n\n\tif ( data === \"false\" ) {\n\t\treturn false;\n\t}\n\n\tif ( data === \"null\" ) {\n\t\treturn null;\n\t}\n\n\t// Only convert to a number if it doesn't change the string\n\tif ( data === +data + \"\" ) {\n\t\treturn +data;\n\t}\n\n\tif ( rbrace.test( data ) ) {\n\t\treturn JSON.parse( data );\n\t}\n\n\treturn data;\n}\n\nfunction dataAttr( elem, key, data ) {\n\tvar name;\n\n\t// If nothing was found internally, try to fetch any\n\t// data from the HTML5 data-* attribute\n\tif ( data === undefined && elem.nodeType === 1 ) {\n\t\tname = \"data-\" + key.replace( rmultiDash, \"-$&\" ).toLowerCase();\n\t\tdata = elem.getAttribute( name );\n\n\t\tif ( typeof data === \"string\" ) {\n\t\t\ttry {\n\t\t\t\tdata = getData( data );\n\t\t\t} catch ( e ) {}\n\n\t\t\t// Make sure we set the data so it isn't changed later\n\t\t\tdataUser.set( elem, key, data );\n\t\t} else {\n\t\t\tdata = undefined;\n\t\t}\n\t}\n\treturn data;\n}\n\njQuery.extend( {\n\thasData: function( elem ) {\n\t\treturn dataUser.hasData( elem ) || dataPriv.hasData( elem );\n\t},\n\n\tdata: function( elem, name, data ) {\n\t\treturn dataUser.access( elem, name, data );\n\t},\n\n\tremoveData: function( elem, name ) {\n\t\tdataUser.remove( elem, name );\n\t},\n\n\t// TODO: Now that all calls to _data and _removeData have been replaced\n\t// with direct calls to dataPriv methods, these can be deprecated.\n\t_data: function( elem, name, data ) {\n\t\treturn dataPriv.access( elem, name, data );\n\t},\n\n\t_removeData: function( elem, name ) {\n\t\tdataPriv.remove( elem, name );\n\t}\n} );\n\njQuery.fn.extend( {\n\tdata: function( key, value ) {\n\t\tvar i, name, data,\n\t\t\telem = this[ 0 ],\n\t\t\tattrs = elem && elem.attributes;\n\n\t\t// Gets all values\n\t\tif ( key === undefined ) {\n\t\t\tif ( this.length ) {\n\t\t\t\tdata = dataUser.get( elem );\n\n\t\t\t\tif ( elem.nodeType === 1 && !dataPriv.get( elem, \"hasDataAttrs\" ) ) {\n\t\t\t\t\ti = attrs.length;\n\t\t\t\t\twhile ( i-- ) {\n\n\t\t\t\t\t\t// Support: IE 11 only\n\t\t\t\t\t\t// The attrs elements can be null (trac-14894)\n\t\t\t\t\t\tif ( attrs[ i ] ) {\n\t\t\t\t\t\t\tname = attrs[ i ].name;\n\t\t\t\t\t\t\tif ( name.indexOf( \"data-\" ) === 0 ) {\n\t\t\t\t\t\t\t\tname = camelCase( name.slice( 5 ) );\n\t\t\t\t\t\t\t\tdataAttr( elem, name, data[ name ] );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tdataPriv.set( elem, \"hasDataAttrs\", true );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn data;\n\t\t}\n\n\t\t// Sets multiple values\n\t\tif ( typeof key === \"object\" ) {\n\t\t\treturn this.each( function() {\n\t\t\t\tdataUser.set( this, key );\n\t\t\t} );\n\t\t}\n\n\t\treturn access( this, function( value ) {\n\t\t\tvar data;\n\n\t\t\t// The calling jQuery object (element matches) is not empty\n\t\t\t// (and therefore has an element appears at this[ 0 ]) and the\n\t\t\t// `value` parameter was not undefined. An empty jQuery object\n\t\t\t// will result in `undefined` for elem = this[ 0 ] which will\n\t\t\t// throw an exception if an attempt to read a data cache is made.\n\t\t\tif ( elem && value === undefined ) {\n\n\t\t\t\t// Attempt to get data from the cache\n\t\t\t\t// The key will always be camelCased in Data\n\t\t\t\tdata = dataUser.get( elem, key );\n\t\t\t\tif ( data !== undefined ) {\n\t\t\t\t\treturn data;\n\t\t\t\t}\n\n\t\t\t\t// Attempt to \"discover\" the data in\n\t\t\t\t// HTML5 custom data-* attrs\n\t\t\t\tdata = dataAttr( elem, key );\n\t\t\t\tif ( data !== undefined ) {\n\t\t\t\t\treturn data;\n\t\t\t\t}\n\n\t\t\t\t// We tried really hard, but the data doesn't exist.\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Set the data...\n\t\t\tthis.each( function() {\n\n\t\t\t\t// We always store the camelCased key\n\t\t\t\tdataUser.set( this, key, value );\n\t\t\t} );\n\t\t}, null, value, arguments.length > 1, null, true );\n\t},\n\n\tremoveData: function( key ) {\n\t\treturn this.each( function() {\n\t\t\tdataUser.remove( this, key );\n\t\t} );\n\t}\n} );\n\n\njQuery.extend( {\n\tqueue: function( elem, type, data ) {\n\t\tvar queue;\n\n\t\tif ( elem ) {\n\t\t\ttype = ( type || \"fx\" ) + \"queue\";\n\t\t\tqueue = dataPriv.get( elem, type );\n\n\t\t\t// Speed up dequeue by getting out quickly if this is just a lookup\n\t\t\tif ( data ) {\n\t\t\t\tif ( !queue || Array.isArray( data ) ) {\n\t\t\t\t\tqueue = dataPriv.access( elem, type, jQuery.makeArray( data ) );\n\t\t\t\t} else {\n\t\t\t\t\tqueue.push( data );\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn queue || [];\n\t\t}\n\t},\n\n\tdequeue: function( elem, type ) {\n\t\ttype = type || \"fx\";\n\n\t\tvar queue = jQuery.queue( elem, type ),\n\t\t\tstartLength = queue.length,\n\t\t\tfn = queue.shift(),\n\t\t\thooks = jQuery._queueHooks( elem, type ),\n\t\t\tnext = function() {\n\t\t\t\tjQuery.dequeue( elem, type );\n\t\t\t};\n\n\t\t// If the fx queue is dequeued, always remove the progress sentinel\n\t\tif ( fn === \"inprogress\" ) {\n\t\t\tfn = queue.shift();\n\t\t\tstartLength--;\n\t\t}\n\n\t\tif ( fn ) {\n\n\t\t\t// Add a progress sentinel to prevent the fx queue from being\n\t\t\t// automatically dequeued\n\t\t\tif ( type === \"fx\" ) {\n\t\t\t\tqueue.unshift( \"inprogress\" );\n\t\t\t}\n\n\t\t\t// Clear up the last queue stop function\n\t\t\tdelete hooks.stop;\n\t\t\tfn.call( elem, next, hooks );\n\t\t}\n\n\t\tif ( !startLength && hooks ) {\n\t\t\thooks.empty.fire();\n\t\t}\n\t},\n\n\t// Not public - generate a queueHooks object, or return the current one\n\t_queueHooks: function( elem, type ) {\n\t\tvar key = type + \"queueHooks\";\n\t\treturn dataPriv.get( elem, key ) || dataPriv.access( elem, key, {\n\t\t\tempty: jQuery.Callbacks( \"once memory\" ).add( function() {\n\t\t\t\tdataPriv.remove( elem, [ type + \"queue\", key ] );\n\t\t\t} )\n\t\t} );\n\t}\n} );\n\njQuery.fn.extend( {\n\tqueue: function( type, data ) {\n\t\tvar setter = 2;\n\n\t\tif ( typeof type !== \"string\" ) {\n\t\t\tdata = type;\n\t\t\ttype = \"fx\";\n\t\t\tsetter--;\n\t\t}\n\n\t\tif ( arguments.length < setter ) {\n\t\t\treturn jQuery.queue( this[ 0 ], type );\n\t\t}\n\n\t\treturn data === undefined ?\n\t\t\tthis :\n\t\t\tthis.each( function() {\n\t\t\t\tvar queue = jQuery.queue( this, type, data );\n\n\t\t\t\t// Ensure a hooks for this queue\n\t\t\t\tjQuery._queueHooks( this, type );\n\n\t\t\t\tif ( type === \"fx\" && queue[ 0 ] !== \"inprogress\" ) {\n\t\t\t\t\tjQuery.dequeue( this, type );\n\t\t\t\t}\n\t\t\t} );\n\t},\n\tdequeue: function( type ) {\n\t\treturn this.each( function() {\n\t\t\tjQuery.dequeue( this, type );\n\t\t} );\n\t},\n\tclearQueue: function( type ) {\n\t\treturn this.queue( type || \"fx\", [] );\n\t},\n\n\t// Get a promise resolved when queues of a certain type\n\t// are emptied (fx is the type by default)\n\tpromise: function( type, obj ) {\n\t\tvar tmp,\n\t\t\tcount = 1,\n\t\t\tdefer = jQuery.Deferred(),\n\t\t\telements = this,\n\t\t\ti = this.length,\n\t\t\tresolve = function() {\n\t\t\t\tif ( !( --count ) ) {\n\t\t\t\t\tdefer.resolveWith( elements, [ elements ] );\n\t\t\t\t}\n\t\t\t};\n\n\t\tif ( typeof type !== \"string\" ) {\n\t\t\tobj = type;\n\t\t\ttype = undefined;\n\t\t}\n\t\ttype = type || \"fx\";\n\n\t\twhile ( i-- ) {\n\t\t\ttmp = dataPriv.get( elements[ i ], type + \"queueHooks\" );\n\t\t\tif ( tmp && tmp.empty ) {\n\t\t\t\tcount++;\n\t\t\t\ttmp.empty.add( resolve );\n\t\t\t}\n\t\t}\n\t\tresolve();\n\t\treturn defer.promise( obj );\n\t}\n} );\nvar pnum = ( /[+-]?(?:\\d*\\.|)\\d+(?:[eE][+-]?\\d+|)/ ).source;\n\nvar rcssNum = new RegExp( \"^(?:([+-])=|)(\" + pnum + \")([a-z%]*)$\", \"i\" );\n\n\nvar cssExpand = [ \"Top\", \"Right\", \"Bottom\", \"Left\" ];\n\nvar documentElement = document.documentElement;\n\n\n\n\tvar isAttached = function( elem ) {\n\t\t\treturn jQuery.contains( elem.ownerDocument, elem );\n\t\t},\n\t\tcomposed = { composed: true };\n\n\t// Support: IE 9 - 11+, Edge 12 - 18+, iOS 10.0 - 10.2 only\n\t// Check attachment across shadow DOM boundaries when possible (gh-3504)\n\t// Support: iOS 10.0-10.2 only\n\t// Early iOS 10 versions support `attachShadow` but not `getRootNode`,\n\t// leading to errors. We need to check for `getRootNode`.\n\tif ( documentElement.getRootNode ) {\n\t\tisAttached = function( elem ) {\n\t\t\treturn jQuery.contains( elem.ownerDocument, elem ) ||\n\t\t\t\telem.getRootNode( composed ) === elem.ownerDocument;\n\t\t};\n\t}\nvar isHiddenWithinTree = function( elem, el ) {\n\n\t\t// isHiddenWithinTree might be called from jQuery#filter function;\n\t\t// in that case, element will be second argument\n\t\telem = el || elem;\n\n\t\t// Inline style trumps all\n\t\treturn elem.style.display === \"none\" ||\n\t\t\telem.style.display === \"\" &&\n\n\t\t\t// Otherwise, check computed style\n\t\t\t// Support: Firefox <=43 - 45\n\t\t\t// Disconnected elements can have computed display: none, so first confirm that elem is\n\t\t\t// in the document.\n\t\t\tisAttached( elem ) &&\n\n\t\t\tjQuery.css( elem, \"display\" ) === \"none\";\n\t};\n\n\n\nfunction adjustCSS( elem, prop, valueParts, tween ) {\n\tvar adjusted, scale,\n\t\tmaxIterations = 20,\n\t\tcurrentValue = tween ?\n\t\t\tfunction() {\n\t\t\t\treturn tween.cur();\n\t\t\t} :\n\t\t\tfunction() {\n\t\t\t\treturn jQuery.css( elem, prop, \"\" );\n\t\t\t},\n\t\tinitial = currentValue(),\n\t\tunit = valueParts && valueParts[ 3 ] || ( jQuery.cssNumber[ prop ] ? \"\" : \"px\" ),\n\n\t\t// Starting value computation is required for potential unit mismatches\n\t\tinitialInUnit = elem.nodeType &&\n\t\t\t( jQuery.cssNumber[ prop ] || unit !== \"px\" && +initial ) &&\n\t\t\trcssNum.exec( jQuery.css( elem, prop ) );\n\n\tif ( initialInUnit && initialInUnit[ 3 ] !== unit ) {\n\n\t\t// Support: Firefox <=54\n\t\t// Halve the iteration target value to prevent interference from CSS upper bounds (gh-2144)\n\t\tinitial = initial / 2;\n\n\t\t// Trust units reported by jQuery.css\n\t\tunit = unit || initialInUnit[ 3 ];\n\n\t\t// Iteratively approximate from a nonzero starting point\n\t\tinitialInUnit = +initial || 1;\n\n\t\twhile ( maxIterations-- ) {\n\n\t\t\t// Evaluate and update our best guess (doubling guesses that zero out).\n\t\t\t// Finish if the scale equals or crosses 1 (making the old*new product non-positive).\n\t\t\tjQuery.style( elem, prop, initialInUnit + unit );\n\t\t\tif ( ( 1 - scale ) * ( 1 - ( scale = currentValue() / initial || 0.5 ) ) <= 0 ) {\n\t\t\t\tmaxIterations = 0;\n\t\t\t}\n\t\t\tinitialInUnit = initialInUnit / scale;\n\n\t\t}\n\n\t\tinitialInUnit = initialInUnit * 2;\n\t\tjQuery.style( elem, prop, initialInUnit + unit );\n\n\t\t// Make sure we update the tween properties later on\n\t\tvalueParts = valueParts || [];\n\t}\n\n\tif ( valueParts ) {\n\t\tinitialInUnit = +initialInUnit || +initial || 0;\n\n\t\t// Apply relative offset (+=/-=) if specified\n\t\tadjusted = valueParts[ 1 ] ?\n\t\t\tinitialInUnit + ( valueParts[ 1 ] + 1 ) * valueParts[ 2 ] :\n\t\t\t+valueParts[ 2 ];\n\t\tif ( tween ) {\n\t\t\ttween.unit = unit;\n\t\t\ttween.start = initialInUnit;\n\t\t\ttween.end = adjusted;\n\t\t}\n\t}\n\treturn adjusted;\n}\n\n\nvar defaultDisplayMap = {};\n\nfunction getDefaultDisplay( elem ) {\n\tvar temp,\n\t\tdoc = elem.ownerDocument,\n\t\tnodeName = elem.nodeName,\n\t\tdisplay = defaultDisplayMap[ nodeName ];\n\n\tif ( display ) {\n\t\treturn display;\n\t}\n\n\ttemp = doc.body.appendChild( doc.createElement( nodeName ) );\n\tdisplay = jQuery.css( temp, \"display\" );\n\n\ttemp.parentNode.removeChild( temp );\n\n\tif ( display === \"none\" ) {\n\t\tdisplay = \"block\";\n\t}\n\tdefaultDisplayMap[ nodeName ] = display;\n\n\treturn display;\n}\n\nfunction showHide( elements, show ) {\n\tvar display, elem,\n\t\tvalues = [],\n\t\tindex = 0,\n\t\tlength = elements.length;\n\n\t// Determine new display value for elements that need to change\n\tfor ( ; index < length; index++ ) {\n\t\telem = elements[ index ];\n\t\tif ( !elem.style ) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tdisplay = elem.style.display;\n\t\tif ( show ) {\n\n\t\t\t// Since we force visibility upon cascade-hidden elements, an immediate (and slow)\n\t\t\t// check is required in this first loop unless we have a nonempty display value (either\n\t\t\t// inline or about-to-be-restored)\n\t\t\tif ( display === \"none\" ) {\n\t\t\t\tvalues[ index ] = dataPriv.get( elem, \"display\" ) || null;\n\t\t\t\tif ( !values[ index ] ) {\n\t\t\t\t\telem.style.display = \"\";\n\t\t\t\t}\n\t\t\t}\n\t\t\tif ( elem.style.display === \"\" && isHiddenWithinTree( elem ) ) {\n\t\t\t\tvalues[ index ] = getDefaultDisplay( elem );\n\t\t\t}\n\t\t} else {\n\t\t\tif ( display !== \"none\" ) {\n\t\t\t\tvalues[ index ] = \"none\";\n\n\t\t\t\t// Remember what we're overwriting\n\t\t\t\tdataPriv.set( elem, \"display\", display );\n\t\t\t}\n\t\t}\n\t}\n\n\t// Set the display of the elements in a second loop to avoid constant reflow\n\tfor ( index = 0; index < length; index++ ) {\n\t\tif ( values[ index ] != null ) {\n\t\t\telements[ index ].style.display = values[ index ];\n\t\t}\n\t}\n\n\treturn elements;\n}\n\njQuery.fn.extend( {\n\tshow: function() {\n\t\treturn showHide( this, true );\n\t},\n\thide: function() {\n\t\treturn showHide( this );\n\t},\n\ttoggle: function( state ) {\n\t\tif ( typeof state === \"boolean\" ) {\n\t\t\treturn state ? this.show() : this.hide();\n\t\t}\n\n\t\treturn this.each( function() {\n\t\t\tif ( isHiddenWithinTree( this ) ) {\n\t\t\t\tjQuery( this ).show();\n\t\t\t} else {\n\t\t\t\tjQuery( this ).hide();\n\t\t\t}\n\t\t} );\n\t}\n} );\nvar rcheckableType = ( /^(?:checkbox|radio)$/i );\n\nvar rtagName = ( /<([a-z][^\\/\\0>\\x20\\t\\r\\n\\f]*)/i );\n\nvar rscriptType = ( /^$|^module$|\\/(?:java|ecma)script/i );\n\n\n\n( function() {\n\tvar fragment = document.createDocumentFragment(),\n\t\tdiv = fragment.appendChild( document.createElement( \"div\" ) ),\n\t\tinput = document.createElement( \"input\" );\n\n\t// Support: Android 4.0 - 4.3 only\n\t// Check state lost if the name is set (trac-11217)\n\t// Support: Windows Web Apps (WWA)\n\t// `name` and `type` must use .setAttribute for WWA (trac-14901)\n\tinput.setAttribute( \"type\", \"radio\" );\n\tinput.setAttribute( \"checked\", \"checked\" );\n\tinput.setAttribute( \"name\", \"t\" );\n\n\tdiv.appendChild( input );\n\n\t// Support: Android <=4.1 only\n\t// Older WebKit doesn't clone checked state correctly in fragments\n\tsupport.checkClone = div.cloneNode( true ).cloneNode( true ).lastChild.checked;\n\n\t// Support: IE <=11 only\n\t// Make sure textarea (and checkbox) defaultValue is properly cloned\n\tdiv.innerHTML = \"<textarea>x</textarea>\";\n\tsupport.noCloneChecked = !!div.cloneNode( true ).lastChild.defaultValue;\n\n\t// Support: IE <=9 only\n\t// IE <=9 replaces <option> tags with their contents when inserted outside of\n\t// the select element.\n\tdiv.innerHTML = \"<option></option>\";\n\tsupport.option = !!div.lastChild;\n} )();\n\n\n// We have to close these tags to support XHTML (trac-13200)\nvar wrapMap = {\n\n\t// XHTML parsers do not magically insert elements in the\n\t// same way that tag soup parsers do. So we cannot shorten\n\t// this by omitting <tbody> or other required elements.\n\tthead: [ 1, \"<table>\", \"</table>\" ],\n\tcol: [ 2, \"<table><colgroup>\", \"</colgroup></table>\" ],\n\ttr: [ 2, \"<table><tbody>\", \"</tbody></table>\" ],\n\ttd: [ 3, \"<table><tbody><tr>\", \"</tr></tbody></table>\" ],\n\n\t_default: [ 0, \"\", \"\" ]\n};\n\nwrapMap.tbody = wrapMap.tfoot = wrapMap.colgroup = wrapMap.caption = wrapMap.thead;\nwrapMap.th = wrapMap.td;\n\n// Support: IE <=9 only\nif ( !support.option ) {\n\twrapMap.optgroup = wrapMap.option = [ 1, \"<select multiple='multiple'>\", \"</select>\" ];\n}\n\n\nfunction getAll( context, tag ) {\n\n\t// Support: IE <=9 - 11 only\n\t// Use typeof to avoid zero-argument method invocation on host objects (trac-15151)\n\tvar ret;\n\n\tif ( typeof context.getElementsByTagName !== \"undefined\" ) {\n\t\tret = context.getElementsByTagName( tag || \"*\" );\n\n\t} else if ( typeof context.querySelectorAll !== \"undefined\" ) {\n\t\tret = context.querySelectorAll( tag || \"*\" );\n\n\t} else {\n\t\tret = [];\n\t}\n\n\tif ( tag === undefined || tag && nodeName( context, tag ) ) {\n\t\treturn jQuery.merge( [ context ], ret );\n\t}\n\n\treturn ret;\n}\n\n\n// Mark scripts as having already been evaluated\nfunction setGlobalEval( elems, refElements ) {\n\tvar i = 0,\n\t\tl = elems.length;\n\n\tfor ( ; i < l; i++ ) {\n\t\tdataPriv.set(\n\t\t\telems[ i ],\n\t\t\t\"globalEval\",\n\t\t\t!refElements || dataPriv.get( refElements[ i ], \"globalEval\" )\n\t\t);\n\t}\n}\n\n\nvar rhtml = /<|&#?\\w+;/;\n\nfunction buildFragment( elems, context, scripts, selection, ignored ) {\n\tvar elem, tmp, tag, wrap, attached, j,\n\t\tfragment = context.createDocumentFragment(),\n\t\tnodes = [],\n\t\ti = 0,\n\t\tl = elems.length;\n\n\tfor ( ; i < l; i++ ) {\n\t\telem = elems[ i ];\n\n\t\tif ( elem || elem === 0 ) {\n\n\t\t\t// Add nodes directly\n\t\t\tif ( toType( elem ) === \"object\" ) {\n\n\t\t\t\t// Support: Android <=4.0 only, PhantomJS 1 only\n\t\t\t\t// push.apply(_, arraylike) throws on ancient WebKit\n\t\t\t\tjQuery.merge( nodes, elem.nodeType ? [ elem ] : elem );\n\n\t\t\t// Convert non-html into a text node\n\t\t\t} else if ( !rhtml.test( elem ) ) {\n\t\t\t\tnodes.push( context.createTextNode( elem ) );\n\n\t\t\t// Convert html into DOM nodes\n\t\t\t} else {\n\t\t\t\ttmp = tmp || fragment.appendChild( context.createElement( \"div\" ) );\n\n\t\t\t\t// Deserialize a standard representation\n\t\t\t\ttag = ( rtagName.exec( elem ) || [ \"\", \"\" ] )[ 1 ].toLowerCase();\n\t\t\t\twrap = wrapMap[ tag ] || wrapMap._default;\n\t\t\t\ttmp.innerHTML = wrap[ 1 ] + jQuery.htmlPrefilter( elem ) + wrap[ 2 ];\n\n\t\t\t\t// Descend through wrappers to the right content\n\t\t\t\tj = wrap[ 0 ];\n\t\t\t\twhile ( j-- ) {\n\t\t\t\t\ttmp = tmp.lastChild;\n\t\t\t\t}\n\n\t\t\t\t// Support: Android <=4.0 only, PhantomJS 1 only\n\t\t\t\t// push.apply(_, arraylike) throws on ancient WebKit\n\t\t\t\tjQuery.merge( nodes, tmp.childNodes );\n\n\t\t\t\t// Remember the top-level container\n\t\t\t\ttmp = fragment.firstChild;\n\n\t\t\t\t// Ensure the created nodes are orphaned (trac-12392)\n\t\t\t\ttmp.textContent = \"\";\n\t\t\t}\n\t\t}\n\t}\n\n\t// Remove wrapper from fragment\n\tfragment.textContent = \"\";\n\n\ti = 0;\n\twhile ( ( elem = nodes[ i++ ] ) ) {\n\n\t\t// Skip elements already in the context collection (trac-4087)\n\t\tif ( selection && jQuery.inArray( elem, selection ) > -1 ) {\n\t\t\tif ( ignored ) {\n\t\t\t\tignored.push( elem );\n\t\t\t}\n\t\t\tcontinue;\n\t\t}\n\n\t\tattached = isAttached( elem );\n\n\t\t// Append to fragment\n\t\ttmp = getAll( fragment.appendChild( elem ), \"script\" );\n\n\t\t// Preserve script evaluation history\n\t\tif ( attached ) {\n\t\t\tsetGlobalEval( tmp );\n\t\t}\n\n\t\t// Capture executables\n\t\tif ( scripts ) {\n\t\t\tj = 0;\n\t\t\twhile ( ( elem = tmp[ j++ ] ) ) {\n\t\t\t\tif ( rscriptType.test( elem.type || \"\" ) ) {\n\t\t\t\t\tscripts.push( elem );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn fragment;\n}\n\n\nvar rtypenamespace = /^([^.]*)(?:\\.(.+)|)/;\n\nfunction returnTrue() {\n\treturn true;\n}\n\nfunction returnFalse() {\n\treturn false;\n}\n\nfunction on( elem, types, selector, data, fn, one ) {\n\tvar origFn, type;\n\n\t// Types can be a map of types/handlers\n\tif ( typeof types === \"object\" ) {\n\n\t\t// ( types-Object, selector, data )\n\t\tif ( typeof selector !== \"string\" ) {\n\n\t\t\t// ( types-Object, data )\n\t\t\tdata = data || selector;\n\t\t\tselector = undefined;\n\t\t}\n\t\tfor ( type in types ) {\n\t\t\ton( elem, type, selector, data, types[ type ], one );\n\t\t}\n\t\treturn elem;\n\t}\n\n\tif ( data == null && fn == null ) {\n\n\t\t// ( types, fn )\n\t\tfn = selector;\n\t\tdata = selector = undefined;\n\t} else if ( fn == null ) {\n\t\tif ( typeof selector === \"string\" ) {\n\n\t\t\t// ( types, selector, fn )\n\t\t\tfn = data;\n\t\t\tdata = undefined;\n\t\t} else {\n\n\t\t\t// ( types, data, fn )\n\t\t\tfn = data;\n\t\t\tdata = selector;\n\t\t\tselector = undefined;\n\t\t}\n\t}\n\tif ( fn === false ) {\n\t\tfn = returnFalse;\n\t} else if ( !fn ) {\n\t\treturn elem;\n\t}\n\n\tif ( one === 1 ) {\n\t\torigFn = fn;\n\t\tfn = function( event ) {\n\n\t\t\t// Can use an empty set, since event contains the info\n\t\t\tjQuery().off( event );\n\t\t\treturn origFn.apply( this, arguments );\n\t\t};\n\n\t\t// Use same guid so caller can remove using origFn\n\t\tfn.guid = origFn.guid || ( origFn.guid = jQuery.guid++ );\n\t}\n\treturn elem.each( function() {\n\t\tjQuery.event.add( this, types, fn, data, selector );\n\t} );\n}\n\n/*\n * Helper functions for managing events -- not part of the public interface.\n * Props to Dean Edwards' addEvent library for many of the ideas.\n */\njQuery.event = {\n\n\tglobal: {},\n\n\tadd: function( elem, types, handler, data, selector ) {\n\n\t\tvar handleObjIn, eventHandle, tmp,\n\t\t\tevents, t, handleObj,\n\t\t\tspecial, handlers, type, namespaces, origType,\n\t\t\telemData = dataPriv.get( elem );\n\n\t\t// Only attach events to objects that accept data\n\t\tif ( !acceptData( elem ) ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Caller can pass in an object of custom data in lieu of the handler\n\t\tif ( handler.handler ) {\n\t\t\thandleObjIn = handler;\n\t\t\thandler = handleObjIn.handler;\n\t\t\tselector = handleObjIn.selector;\n\t\t}\n\n\t\t// Ensure that invalid selectors throw exceptions at attach time\n\t\t// Evaluate against documentElement in case elem is a non-element node (e.g., document)\n\t\tif ( selector ) {\n\t\t\tjQuery.find.matchesSelector( documentElement, selector );\n\t\t}\n\n\t\t// Make sure that the handler has a unique ID, used to find/remove it later\n\t\tif ( !handler.guid ) {\n\t\t\thandler.guid = jQuery.guid++;\n\t\t}\n\n\t\t// Init the element's event structure and main handler, if this is the first\n\t\tif ( !( events = elemData.events ) ) {\n\t\t\tevents = elemData.events = Object.create( null );\n\t\t}\n\t\tif ( !( eventHandle = elemData.handle ) ) {\n\t\t\teventHandle = elemData.handle = function( e ) {\n\n\t\t\t\t// Discard the second event of a jQuery.event.trigger() and\n\t\t\t\t// when an event is called after a page has unloaded\n\t\t\t\treturn typeof jQuery !== \"undefined\" && jQuery.event.triggered !== e.type ?\n\t\t\t\t\tjQuery.event.dispatch.apply( elem, arguments ) : undefined;\n\t\t\t};\n\t\t}\n\n\t\t// Handle multiple events separated by a space\n\t\ttypes = ( types || \"\" ).match( rnothtmlwhite ) || [ \"\" ];\n\t\tt = types.length;\n\t\twhile ( t-- ) {\n\t\t\ttmp = rtypenamespace.exec( types[ t ] ) || [];\n\t\t\ttype = origType = tmp[ 1 ];\n\t\t\tnamespaces = ( tmp[ 2 ] || \"\" ).split( \".\" ).sort();\n\n\t\t\t// There *must* be a type, no attaching namespace-only handlers\n\t\t\tif ( !type ) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// If event changes its type, use the special event handlers for the changed type\n\t\t\tspecial = jQuery.event.special[ type ] || {};\n\n\t\t\t// If selector defined, determine special event api type, otherwise given type\n\t\t\ttype = ( selector ? special.delegateType : special.bindType ) || type;\n\n\t\t\t// Update special based on newly reset type\n\t\t\tspecial = jQuery.event.special[ type ] || {};\n\n\t\t\t// handleObj is passed to all event handlers\n\t\t\thandleObj = jQuery.extend( {\n\t\t\t\ttype: type,\n\t\t\t\torigType: origType,\n\t\t\t\tdata: data,\n\t\t\t\thandler: handler,\n\t\t\t\tguid: handler.guid,\n\t\t\t\tselector: selector,\n\t\t\t\tneedsContext: selector && jQuery.expr.match.needsContext.test( selector ),\n\t\t\t\tnamespace: namespaces.join( \".\" )\n\t\t\t}, handleObjIn );\n\n\t\t\t// Init the event handler queue if we're the first\n\t\t\tif ( !( handlers = events[ type ] ) ) {\n\t\t\t\thandlers = events[ type ] = [];\n\t\t\t\thandlers.delegateCount = 0;\n\n\t\t\t\t// Only use addEventListener if the special events handler returns false\n\t\t\t\tif ( !special.setup ||\n\t\t\t\t\tspecial.setup.call( elem, data, namespaces, eventHandle ) === false ) {\n\n\t\t\t\t\tif ( elem.addEventListener ) {\n\t\t\t\t\t\telem.addEventListener( type, eventHandle );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif ( special.add ) {\n\t\t\t\tspecial.add.call( elem, handleObj );\n\n\t\t\t\tif ( !handleObj.handler.guid ) {\n\t\t\t\t\thandleObj.handler.guid = handler.guid;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Add to the element's handler list, delegates in front\n\t\t\tif ( selector ) {\n\t\t\t\thandlers.splice( handlers.delegateCount++, 0, handleObj );\n\t\t\t} else {\n\t\t\t\thandlers.push( handleObj );\n\t\t\t}\n\n\t\t\t// Keep track of which events have ever been used, for event optimization\n\t\t\tjQuery.event.global[ type ] = true;\n\t\t}\n\n\t},\n\n\t// Detach an event or set of events from an element\n\tremove: function( elem, types, handler, selector, mappedTypes ) {\n\n\t\tvar j, origCount, tmp,\n\t\t\tevents, t, handleObj,\n\t\t\tspecial, handlers, type, namespaces, origType,\n\t\t\telemData = dataPriv.hasData( elem ) && dataPriv.get( elem );\n\n\t\tif ( !elemData || !( events = elemData.events ) ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Once for each type.namespace in types; type may be omitted\n\t\ttypes = ( types || \"\" ).match( rnothtmlwhite ) || [ \"\" ];\n\t\tt = types.length;\n\t\twhile ( t-- ) {\n\t\t\ttmp = rtypenamespace.exec( types[ t ] ) || [];\n\t\t\ttype = origType = tmp[ 1 ];\n\t\t\tnamespaces = ( tmp[ 2 ] || \"\" ).split( \".\" ).sort();\n\n\t\t\t// Unbind all events (on this namespace, if provided) for the element\n\t\t\tif ( !type ) {\n\t\t\t\tfor ( type in events ) {\n\t\t\t\t\tjQuery.event.remove( elem, type + types[ t ], handler, selector, true );\n\t\t\t\t}\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tspecial = jQuery.event.special[ type ] || {};\n\t\t\ttype = ( selector ? special.delegateType : special.bindType ) || type;\n\t\t\thandlers = events[ type ] || [];\n\t\t\ttmp = tmp[ 2 ] &&\n\t\t\t\tnew RegExp( \"(^|\\\\.)\" + namespaces.join( \"\\\\.(?:.*\\\\.|)\" ) + \"(\\\\.|$)\" );\n\n\t\t\t// Remove matching events\n\t\t\torigCount = j = handlers.length;\n\t\t\twhile ( j-- ) {\n\t\t\t\thandleObj = handlers[ j ];\n\n\t\t\t\tif ( ( mappedTypes || origType === handleObj.origType ) &&\n\t\t\t\t\t( !handler || handler.guid === handleObj.guid ) &&\n\t\t\t\t\t( !tmp || tmp.test( handleObj.namespace ) ) &&\n\t\t\t\t\t( !selector || selector === handleObj.selector ||\n\t\t\t\t\t\tselector === \"**\" && handleObj.selector ) ) {\n\t\t\t\t\thandlers.splice( j, 1 );\n\n\t\t\t\t\tif ( handleObj.selector ) {\n\t\t\t\t\t\thandlers.delegateCount--;\n\t\t\t\t\t}\n\t\t\t\t\tif ( special.remove ) {\n\t\t\t\t\t\tspecial.remove.call( elem, handleObj );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Remove generic event handler if we removed something and no more handlers exist\n\t\t\t// (avoids potential for endless recursion during removal of special event handlers)\n\t\t\tif ( origCount && !handlers.length ) {\n\t\t\t\tif ( !special.teardown ||\n\t\t\t\t\tspecial.teardown.call( elem, namespaces, elemData.handle ) === false ) {\n\n\t\t\t\t\tjQuery.removeEvent( elem, type, elemData.handle );\n\t\t\t\t}\n\n\t\t\t\tdelete events[ type ];\n\t\t\t}\n\t\t}\n\n\t\t// Remove data and the expando if it's no longer used\n\t\tif ( jQuery.isEmptyObject( events ) ) {\n\t\t\tdataPriv.remove( elem, \"handle events\" );\n\t\t}\n\t},\n\n\tdispatch: function( nativeEvent ) {\n\n\t\tvar i, j, ret, matched, handleObj, handlerQueue,\n\t\t\targs = new Array( arguments.length ),\n\n\t\t\t// Make a writable jQuery.Event from the native event object\n\t\t\tevent = jQuery.event.fix( nativeEvent ),\n\n\t\t\thandlers = (\n\t\t\t\tdataPriv.get( this, \"events\" ) || Object.create( null )\n\t\t\t)[ event.type ] || [],\n\t\t\tspecial = jQuery.event.special[ event.type ] || {};\n\n\t\t// Use the fix-ed jQuery.Event rather than the (read-only) native event\n\t\targs[ 0 ] = event;\n\n\t\tfor ( i = 1; i < arguments.length; i++ ) {\n\t\t\targs[ i ] = arguments[ i ];\n\t\t}\n\n\t\tevent.delegateTarget = this;\n\n\t\t// Call the preDispatch hook for the mapped type, and let it bail if desired\n\t\tif ( special.preDispatch && special.preDispatch.call( this, event ) === false ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Determine handlers\n\t\thandlerQueue = jQuery.event.handlers.call( this, event, handlers );\n\n\t\t// Run delegates first; they may want to stop propagation beneath us\n\t\ti = 0;\n\t\twhile ( ( matched = handlerQueue[ i++ ] ) && !event.isPropagationStopped() ) {\n\t\t\tevent.currentTarget = matched.elem;\n\n\t\t\tj = 0;\n\t\t\twhile ( ( handleObj = matched.handlers[ j++ ] ) &&\n\t\t\t\t!event.isImmediatePropagationStopped() ) {\n\n\t\t\t\t// If the event is namespaced, then each handler is only invoked if it is\n\t\t\t\t// specially universal or its namespaces are a superset of the event's.\n\t\t\t\tif ( !event.rnamespace || handleObj.namespace === false ||\n\t\t\t\t\tevent.rnamespace.test( handleObj.namespace ) ) {\n\n\t\t\t\t\tevent.handleObj = handleObj;\n\t\t\t\t\tevent.data = handleObj.data;\n\n\t\t\t\t\tret = ( ( jQuery.event.special[ handleObj.origType ] || {} ).handle ||\n\t\t\t\t\t\thandleObj.handler ).apply( matched.elem, args );\n\n\t\t\t\t\tif ( ret !== undefined ) {\n\t\t\t\t\t\tif ( ( event.result = ret ) === false ) {\n\t\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Call the postDispatch hook for the mapped type\n\t\tif ( special.postDispatch ) {\n\t\t\tspecial.postDispatch.call( this, event );\n\t\t}\n\n\t\treturn event.result;\n\t},\n\n\thandlers: function( event, handlers ) {\n\t\tvar i, handleObj, sel, matchedHandlers, matchedSelectors,\n\t\t\thandlerQueue = [],\n\t\t\tdelegateCount = handlers.delegateCount,\n\t\t\tcur = event.target;\n\n\t\t// Find delegate handlers\n\t\tif ( delegateCount &&\n\n\t\t\t// Support: IE <=9\n\t\t\t// Black-hole SVG <use> instance trees (trac-13180)\n\t\t\tcur.nodeType &&\n\n\t\t\t// Support: Firefox <=42\n\t\t\t// Suppress spec-violating clicks indicating a non-primary pointer button (trac-3861)\n\t\t\t// https://www.w3.org/TR/DOM-Level-3-Events/#event-type-click\n\t\t\t// Support: IE 11 only\n\t\t\t// ...but not arrow key \"clicks\" of radio inputs, which can have `button` -1 (gh-2343)\n\t\t\t!( event.type === \"click\" && event.button >= 1 ) ) {\n\n\t\t\tfor ( ; cur !== this; cur = cur.parentNode || this ) {\n\n\t\t\t\t// Don't check non-elements (trac-13208)\n\t\t\t\t// Don't process clicks on disabled elements (trac-6911, trac-8165, trac-11382, trac-11764)\n\t\t\t\tif ( cur.nodeType === 1 && !( event.type === \"click\" && cur.disabled === true ) ) {\n\t\t\t\t\tmatchedHandlers = [];\n\t\t\t\t\tmatchedSelectors = {};\n\t\t\t\t\tfor ( i = 0; i < delegateCount; i++ ) {\n\t\t\t\t\t\thandleObj = handlers[ i ];\n\n\t\t\t\t\t\t// Don't conflict with Object.prototype properties (trac-13203)\n\t\t\t\t\t\tsel = handleObj.selector + \" \";\n\n\t\t\t\t\t\tif ( matchedSelectors[ sel ] === undefined ) {\n\t\t\t\t\t\t\tmatchedSelectors[ sel ] = handleObj.needsContext ?\n\t\t\t\t\t\t\t\tjQuery( sel, this ).index( cur ) > -1 :\n\t\t\t\t\t\t\t\tjQuery.find( sel, this, null, [ cur ] ).length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif ( matchedSelectors[ sel ] ) {\n\t\t\t\t\t\t\tmatchedHandlers.push( handleObj );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif ( matchedHandlers.length ) {\n\t\t\t\t\t\thandlerQueue.push( { elem: cur, handlers: matchedHandlers } );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Add the remaining (directly-bound) handlers\n\t\tcur = this;\n\t\tif ( delegateCount < handlers.length ) {\n\t\t\thandlerQueue.push( { elem: cur, handlers: handlers.slice( delegateCount ) } );\n\t\t}\n\n\t\treturn handlerQueue;\n\t},\n\n\taddProp: function( name, hook ) {\n\t\tObject.defineProperty( jQuery.Event.prototype, name, {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\n\t\t\tget: isFunction( hook ) ?\n\t\t\t\tfunction() {\n\t\t\t\t\tif ( this.originalEvent ) {\n\t\t\t\t\t\treturn hook( this.originalEvent );\n\t\t\t\t\t}\n\t\t\t\t} :\n\t\t\t\tfunction() {\n\t\t\t\t\tif ( this.originalEvent ) {\n\t\t\t\t\t\treturn this.originalEvent[ name ];\n\t\t\t\t\t}\n\t\t\t\t},\n\n\t\t\tset: function( value ) {\n\t\t\t\tObject.defineProperty( this, name, {\n\t\t\t\t\tenumerable: true,\n\t\t\t\t\tconfigurable: true,\n\t\t\t\t\twritable: true,\n\t\t\t\t\tvalue: value\n\t\t\t\t} );\n\t\t\t}\n\t\t} );\n\t},\n\n\tfix: function( originalEvent ) {\n\t\treturn originalEvent[ jQuery.expando ] ?\n\t\t\toriginalEvent :\n\t\t\tnew jQuery.Event( originalEvent );\n\t},\n\n\tspecial: {\n\t\tload: {\n\n\t\t\t// Prevent triggered image.load events from bubbling to window.load\n\t\t\tnoBubble: true\n\t\t},\n\t\tclick: {\n\n\t\t\t// Utilize native event to ensure correct state for checkable inputs\n\t\t\tsetup: function( data ) {\n\n\t\t\t\t// For mutual compressibility with _default, replace `this` access with a local var.\n\t\t\t\t// `|| data` is dead code meant only to preserve the variable through minification.\n\t\t\t\tvar el = this || data;\n\n\t\t\t\t// Claim the first handler\n\t\t\t\tif ( rcheckableType.test( el.type ) &&\n\t\t\t\t\tel.click && nodeName( el, \"input\" ) ) {\n\n\t\t\t\t\t// dataPriv.set( el, \"click\", ... )\n\t\t\t\t\tleverageNative( el, \"click\", true );\n\t\t\t\t}\n\n\t\t\t\t// Return false to allow normal processing in the caller\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\ttrigger: function( data ) {\n\n\t\t\t\t// For mutual compressibility with _default, replace `this` access with a local var.\n\t\t\t\t// `|| data` is dead code meant only to preserve the variable through minification.\n\t\t\t\tvar el = this || data;\n\n\t\t\t\t// Force setup before triggering a click\n\t\t\t\tif ( rcheckableType.test( el.type ) &&\n\t\t\t\t\tel.click && nodeName( el, \"input\" ) ) {\n\n\t\t\t\t\tleverageNative( el, \"click\" );\n\t\t\t\t}\n\n\t\t\t\t// Return non-false to allow normal event-path propagation\n\t\t\t\treturn true;\n\t\t\t},\n\n\t\t\t// For cross-browser consistency, suppress native .click() on links\n\t\t\t// Also prevent it if we're currently inside a leveraged native-event stack\n\t\t\t_default: function( event ) {\n\t\t\t\tvar target = event.target;\n\t\t\t\treturn rcheckableType.test( target.type ) &&\n\t\t\t\t\ttarget.click && nodeName( target, \"input\" ) &&\n\t\t\t\t\tdataPriv.get( target, \"click\" ) ||\n\t\t\t\t\tnodeName( target, \"a\" );\n\t\t\t}\n\t\t},\n\n\t\tbeforeunload: {\n\t\t\tpostDispatch: function( event ) {\n\n\t\t\t\t// Support: Firefox 20+\n\t\t\t\t// Firefox doesn't alert if the returnValue field is not set.\n\t\t\t\tif ( event.result !== undefined && event.originalEvent ) {\n\t\t\t\t\tevent.originalEvent.returnValue = event.result;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n};\n\n// Ensure the presence of an event listener that handles manually-triggered\n// synthetic events by interrupting progress until reinvoked in response to\n// *native* events that it fires directly, ensuring that state changes have\n// already occurred before other listeners are invoked.\nfunction leverageNative( el, type, isSetup ) {\n\n\t// Missing `isSetup` indicates a trigger call, which must force setup through jQuery.event.add\n\tif ( !isSetup ) {\n\t\tif ( dataPriv.get( el, type ) === undefined ) {\n\t\t\tjQuery.event.add( el, type, returnTrue );\n\t\t}\n\t\treturn;\n\t}\n\n\t// Register the controller as a special universal handler for all event namespaces\n\tdataPriv.set( el, type, false );\n\tjQuery.event.add( el, type, {\n\t\tnamespace: false,\n\t\thandler: function( event ) {\n\t\t\tvar result,\n\t\t\t\tsaved = dataPriv.get( this, type );\n\n\t\t\tif ( ( event.isTrigger & 1 ) && this[ type ] ) {\n\n\t\t\t\t// Interrupt processing of the outer synthetic .trigger()ed event\n\t\t\t\tif ( !saved ) {\n\n\t\t\t\t\t// Store arguments for use when handling the inner native event\n\t\t\t\t\t// There will always be at least one argument (an event object), so this array\n\t\t\t\t\t// will not be confused with a leftover capture object.\n\t\t\t\t\tsaved = slice.call( arguments );\n\t\t\t\t\tdataPriv.set( this, type, saved );\n\n\t\t\t\t\t// Trigger the native event and capture its result\n\t\t\t\t\tthis[ type ]();\n\t\t\t\t\tresult = dataPriv.get( this, type );\n\t\t\t\t\tdataPriv.set( this, type, false );\n\n\t\t\t\t\tif ( saved !== result ) {\n\n\t\t\t\t\t\t// Cancel the outer synthetic event\n\t\t\t\t\t\tevent.stopImmediatePropagation();\n\t\t\t\t\t\tevent.preventDefault();\n\n\t\t\t\t\t\treturn result;\n\t\t\t\t\t}\n\n\t\t\t\t// If this is an inner synthetic event for an event with a bubbling surrogate\n\t\t\t\t// (focus or blur), assume that the surrogate already propagated from triggering\n\t\t\t\t// the native event and prevent that from happening again here.\n\t\t\t\t// This technically gets the ordering wrong w.r.t. to `.trigger()` (in which the\n\t\t\t\t// bubbling surrogate propagates *after* the non-bubbling base), but that seems\n\t\t\t\t// less bad than duplication.\n\t\t\t\t} else if ( ( jQuery.event.special[ type ] || {} ).delegateType ) {\n\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t}\n\n\t\t\t// If this is a native event triggered above, everything is now in order\n\t\t\t// Fire an inner synthetic event with the original arguments\n\t\t\t} else if ( saved ) {\n\n\t\t\t\t// ...and capture the result\n\t\t\t\tdataPriv.set( this, type, jQuery.event.trigger(\n\t\t\t\t\tsaved[ 0 ],\n\t\t\t\t\tsaved.slice( 1 ),\n\t\t\t\t\tthis\n\t\t\t\t) );\n\n\t\t\t\t// Abort handling of the native event by all jQuery handlers while allowing\n\t\t\t\t// native handlers on the same element to run. On target, this is achieved\n\t\t\t\t// by stopping immediate propagation just on the jQuery event. However,\n\t\t\t\t// the native event is re-wrapped by a jQuery one on each level of the\n\t\t\t\t// propagation so the only way to stop it for jQuery is to stop it for\n\t\t\t\t// everyone via native `stopPropagation()`. This is not a problem for\n\t\t\t\t// focus/blur which don't bubble, but it does also stop click on checkboxes\n\t\t\t\t// and radios. We accept this limitation.\n\t\t\t\tevent.stopPropagation();\n\t\t\t\tevent.isImmediatePropagationStopped = returnTrue;\n\t\t\t}\n\t\t}\n\t} );\n}\n\njQuery.removeEvent = function( elem, type, handle ) {\n\n\t// This \"if\" is needed for plain objects\n\tif ( elem.removeEventListener ) {\n\t\telem.removeEventListener( type, handle );\n\t}\n};\n\njQuery.Event = function( src, props ) {\n\n\t// Allow instantiation without the 'new' keyword\n\tif ( !( this instanceof jQuery.Event ) ) {\n\t\treturn new jQuery.Event( src, props );\n\t}\n\n\t// Event object\n\tif ( src && src.type ) {\n\t\tthis.originalEvent = src;\n\t\tthis.type = src.type;\n\n\t\t// Events bubbling up the document may have been marked as prevented\n\t\t// by a handler lower down the tree; reflect the correct value.\n\t\tthis.isDefaultPrevented = src.defaultPrevented ||\n\t\t\t\tsrc.defaultPrevented === undefined &&\n\n\t\t\t\t// Support: Android <=2.3 only\n\t\t\t\tsrc.returnValue === false ?\n\t\t\treturnTrue :\n\t\t\treturnFalse;\n\n\t\t// Create target properties\n\t\t// Support: Safari <=6 - 7 only\n\t\t// Target should not be a text node (trac-504, trac-13143)\n\t\tthis.target = ( src.target && src.target.nodeType === 3 ) ?\n\t\t\tsrc.target.parentNode :\n\t\t\tsrc.target;\n\n\t\tthis.currentTarget = src.currentTarget;\n\t\tthis.relatedTarget = src.relatedTarget;\n\n\t// Event type\n\t} else {\n\t\tthis.type = src;\n\t}\n\n\t// Put explicitly provided properties onto the event object\n\tif ( props ) {\n\t\tjQuery.extend( this, props );\n\t}\n\n\t// Create a timestamp if incoming event doesn't have one\n\tthis.timeStamp = src && src.timeStamp || Date.now();\n\n\t// Mark it as fixed\n\tthis[ jQuery.expando ] = true;\n};\n\n// jQuery.Event is based on DOM3 Events as specified by the ECMAScript Language Binding\n// https://www.w3.org/TR/2003/WD-DOM-Level-3-Events-20030331/ecma-script-binding.html\njQuery.Event.prototype = {\n\tconstructor: jQuery.Event,\n\tisDefaultPrevented: returnFalse,\n\tisPropagationStopped: returnFalse,\n\tisImmediatePropagationStopped: returnFalse,\n\tisSimulated: false,\n\n\tpreventDefault: function() {\n\t\tvar e = this.originalEvent;\n\n\t\tthis.isDefaultPrevented = returnTrue;\n\n\t\tif ( e && !this.isSimulated ) {\n\t\t\te.preventDefault();\n\t\t}\n\t},\n\tstopPropagation: function() {\n\t\tvar e = this.originalEvent;\n\n\t\tthis.isPropagationStopped = returnTrue;\n\n\t\tif ( e && !this.isSimulated ) {\n\t\t\te.stopPropagation();\n\t\t}\n\t},\n\tstopImmediatePropagation: function() {\n\t\tvar e = this.originalEvent;\n\n\t\tthis.isImmediatePropagationStopped = returnTrue;\n\n\t\tif ( e && !this.isSimulated ) {\n\t\t\te.stopImmediatePropagation();\n\t\t}\n\n\t\tthis.stopPropagation();\n\t}\n};\n\n// Includes all common event props including KeyEvent and MouseEvent specific props\njQuery.each( {\n\taltKey: true,\n\tbubbles: true,\n\tcancelable: true,\n\tchangedTouches: true,\n\tctrlKey: true,\n\tdetail: true,\n\teventPhase: true,\n\tmetaKey: true,\n\tpageX: true,\n\tpageY: true,\n\tshiftKey: true,\n\tview: true,\n\t\"char\": true,\n\tcode: true,\n\tcharCode: true,\n\tkey: true,\n\tkeyCode: true,\n\tbutton: true,\n\tbuttons: true,\n\tclientX: true,\n\tclientY: true,\n\toffsetX: true,\n\toffsetY: true,\n\tpointerId: true,\n\tpointerType: true,\n\tscreenX: true,\n\tscreenY: true,\n\ttargetTouches: true,\n\ttoElement: true,\n\ttouches: true,\n\twhich: true\n}, jQuery.event.addProp );\n\njQuery.each( { focus: \"focusin\", blur: \"focusout\" }, function( type, delegateType ) {\n\n\tfunction focusMappedHandler( nativeEvent ) {\n\t\tif ( document.documentMode ) {\n\n\t\t\t// Support: IE 11+\n\t\t\t// Attach a single focusin/focusout handler on the document while someone wants\n\t\t\t// focus/blur. This is because the former are synchronous in IE while the latter\n\t\t\t// are async. In other browsers, all those handlers are invoked synchronously.\n\n\t\t\t// `handle` from private data would already wrap the event, but we need\n\t\t\t// to change the `type` here.\n\t\t\tvar handle = dataPriv.get( this, \"handle\" ),\n\t\t\t\tevent = jQuery.event.fix( nativeEvent );\n\t\t\tevent.type = nativeEvent.type === \"focusin\" ? \"focus\" : \"blur\";\n\t\t\tevent.isSimulated = true;\n\n\t\t\t// First, handle focusin/focusout\n\t\t\thandle( nativeEvent );\n\n\t\t\t// ...then, handle focus/blur\n\t\t\t//\n\t\t\t// focus/blur don't bubble while focusin/focusout do; simulate the former by only\n\t\t\t// invoking the handler at the lower level.\n\t\t\tif ( event.target === event.currentTarget ) {\n\n\t\t\t\t// The setup part calls `leverageNative`, which, in turn, calls\n\t\t\t\t// `jQuery.event.add`, so event handle will already have been set\n\t\t\t\t// by this point.\n\t\t\t\thandle( event );\n\t\t\t}\n\t\t} else {\n\n\t\t\t// For non-IE browsers, attach a single capturing handler on the document\n\t\t\t// while someone wants focusin/focusout.\n\t\t\tjQuery.event.simulate( delegateType, nativeEvent.target,\n\t\t\t\tjQuery.event.fix( nativeEvent ) );\n\t\t}\n\t}\n\n\tjQuery.event.special[ type ] = {\n\n\t\t// Utilize native event if possible so blur/focus sequence is correct\n\t\tsetup: function() {\n\n\t\t\tvar attaches;\n\n\t\t\t// Claim the first handler\n\t\t\t// dataPriv.set( this, \"focus\", ... )\n\t\t\t// dataPriv.set( this, \"blur\", ... )\n\t\t\tleverageNative( this, type, true );\n\n\t\t\tif ( document.documentMode ) {\n\n\t\t\t\t// Support: IE 9 - 11+\n\t\t\t\t// We use the same native handler for focusin & focus (and focusout & blur)\n\t\t\t\t// so we need to coordinate setup & teardown parts between those events.\n\t\t\t\t// Use `delegateType` as the key as `type` is already used by `leverageNative`.\n\t\t\t\tattaches = dataPriv.get( this, delegateType );\n\t\t\t\tif ( !attaches ) {\n\t\t\t\t\tthis.addEventListener( delegateType, focusMappedHandler );\n\t\t\t\t}\n\t\t\t\tdataPriv.set( this, delegateType, ( attaches || 0 ) + 1 );\n\t\t\t} else {\n\n\t\t\t\t// Return false to allow normal processing in the caller\n\t\t\t\treturn false;\n\t\t\t}\n\t\t},\n\t\ttrigger: function() {\n\n\t\t\t// Force setup before trigger\n\t\t\tleverageNative( this, type );\n\n\t\t\t// Return non-false to allow normal event-path propagation\n\t\t\treturn true;\n\t\t},\n\n\t\tteardown: function() {\n\t\t\tvar attaches;\n\n\t\t\tif ( document.documentMode ) {\n\t\t\t\tattaches = dataPriv.get( this, delegateType ) - 1;\n\t\t\t\tif ( !attaches ) {\n\t\t\t\t\tthis.removeEventListener( delegateType, focusMappedHandler );\n\t\t\t\t\tdataPriv.remove( this, delegateType );\n\t\t\t\t} else {\n\t\t\t\t\tdataPriv.set( this, delegateType, attaches );\n\t\t\t\t}\n\t\t\t} else {\n\n\t\t\t\t// Return false to indicate standard teardown should be applied\n\t\t\t\treturn false;\n\t\t\t}\n\t\t},\n\n\t\t// Suppress native focus or blur if we're currently inside\n\t\t// a leveraged native-event stack\n\t\t_default: function( event ) {\n\t\t\treturn dataPriv.get( event.target, type );\n\t\t},\n\n\t\tdelegateType: delegateType\n\t};\n\n\t// Support: Firefox <=44\n\t// Firefox doesn't have focus(in | out) events\n\t// Related ticket - https://bugzilla.mozilla.org/show_bug.cgi?id=687787\n\t//\n\t// Support: Chrome <=48 - 49, Safari <=9.0 - 9.1\n\t// focus(in | out) events fire after focus & blur events,\n\t// which is spec violation - http://www.w3.org/TR/DOM-Level-3-Events/#events-focusevent-event-order\n\t// Related ticket - https://bugs.chromium.org/p/chromium/issues/detail?id=449857\n\t//\n\t// Support: IE 9 - 11+\n\t// To preserve relative focusin/focus & focusout/blur event order guaranteed on the 3.x branch,\n\t// attach a single handler for both events in IE.\n\tjQuery.event.special[ delegateType ] = {\n\t\tsetup: function() {\n\n\t\t\t// Handle: regular nodes (via `this.ownerDocument`), window\n\t\t\t// (via `this.document`) & document (via `this`).\n\t\t\tvar doc = this.ownerDocument || this.document || this,\n\t\t\t\tdataHolder = document.documentMode ? this : doc,\n\t\t\t\tattaches = dataPriv.get( dataHolder, delegateType );\n\n\t\t\t// Support: IE 9 - 11+\n\t\t\t// We use the same native handler for focusin & focus (and focusout & blur)\n\t\t\t// so we need to coordinate setup & teardown parts between those events.\n\t\t\t// Use `delegateType` as the key as `type` is already used by `leverageNative`.\n\t\t\tif ( !attaches ) {\n\t\t\t\tif ( document.documentMode ) {\n\t\t\t\t\tthis.addEventListener( delegateType, focusMappedHandler );\n\t\t\t\t} else {\n\t\t\t\t\tdoc.addEventListener( type, focusMappedHandler, true );\n\t\t\t\t}\n\t\t\t}\n\t\t\tdataPriv.set( dataHolder, delegateType, ( attaches || 0 ) + 1 );\n\t\t},\n\t\tteardown: function() {\n\t\t\tvar doc = this.ownerDocument || this.document || this,\n\t\t\t\tdataHolder = document.documentMode ? this : doc,\n\t\t\t\tattaches = dataPriv.get( dataHolder, delegateType ) - 1;\n\n\t\t\tif ( !attaches ) {\n\t\t\t\tif ( document.documentMode ) {\n\t\t\t\t\tthis.removeEventListener( delegateType, focusMappedHandler );\n\t\t\t\t} else {\n\t\t\t\t\tdoc.removeEventListener( type, focusMappedHandler, true );\n\t\t\t\t}\n\t\t\t\tdataPriv.remove( dataHolder, delegateType );\n\t\t\t} else {\n\t\t\t\tdataPriv.set( dataHolder, delegateType, attaches );\n\t\t\t}\n\t\t}\n\t};\n} );\n\n// Create mouseenter/leave events using mouseover/out and event-time checks\n// so that event delegation works in jQuery.\n// Do the same for pointerenter/pointerleave and pointerover/pointerout\n//\n// Support: Safari 7 only\n// Safari sends mouseenter too often; see:\n// https://bugs.chromium.org/p/chromium/issues/detail?id=470258\n// for the description of the bug (it existed in older Chrome versions as well).\njQuery.each( {\n\tmouseenter: \"mouseover\",\n\tmouseleave: \"mouseout\",\n\tpointerenter: \"pointerover\",\n\tpointerleave: \"pointerout\"\n}, function( orig, fix ) {\n\tjQuery.event.special[ orig ] = {\n\t\tdelegateType: fix,\n\t\tbindType: fix,\n\n\t\thandle: function( event ) {\n\t\t\tvar ret,\n\t\t\t\ttarget = this,\n\t\t\t\trelated = event.relatedTarget,\n\t\t\t\thandleObj = event.handleObj;\n\n\t\t\t// For mouseenter/leave call the handler if related is outside the target.\n\t\t\t// NB: No relatedTarget if the mouse left/entered the browser window\n\t\t\tif ( !related || ( related !== target && !jQuery.contains( target, related ) ) ) {\n\t\t\t\tevent.type = handleObj.origType;\n\t\t\t\tret = handleObj.handler.apply( this, arguments );\n\t\t\t\tevent.type = fix;\n\t\t\t}\n\t\t\treturn ret;\n\t\t}\n\t};\n} );\n\njQuery.fn.extend( {\n\n\ton: function( types, selector, data, fn ) {\n\t\treturn on( this, types, selector, data, fn );\n\t},\n\tone: function( types, selector, data, fn ) {\n\t\treturn on( this, types, selector, data, fn, 1 );\n\t},\n\toff: function( types, selector, fn ) {\n\t\tvar handleObj, type;\n\t\tif ( types && types.preventDefault && types.handleObj ) {\n\n\t\t\t// ( event )  dispatched jQuery.Event\n\t\t\thandleObj = types.handleObj;\n\t\t\tjQuery( types.delegateTarget ).off(\n\t\t\t\thandleObj.namespace ?\n\t\t\t\t\thandleObj.origType + \".\" + handleObj.namespace :\n\t\t\t\t\thandleObj.origType,\n\t\t\t\thandleObj.selector,\n\t\t\t\thandleObj.handler\n\t\t\t);\n\t\t\treturn this;\n\t\t}\n\t\tif ( typeof types === \"object\" ) {\n\n\t\t\t// ( types-object [, selector] )\n\t\t\tfor ( type in types ) {\n\t\t\t\tthis.off( type, selector, types[ type ] );\n\t\t\t}\n\t\t\treturn this;\n\t\t}\n\t\tif ( selector === false || typeof selector === \"function\" ) {\n\n\t\t\t// ( types [, fn] )\n\t\t\tfn = selector;\n\t\t\tselector = undefined;\n\t\t}\n\t\tif ( fn === false ) {\n\t\t\tfn = returnFalse;\n\t\t}\n\t\treturn this.each( function() {\n\t\t\tjQuery.event.remove( this, types, fn, selector );\n\t\t} );\n\t}\n} );\n\n\nvar\n\n\t// Support: IE <=10 - 11, Edge 12 - 13 only\n\t// In IE/Edge using regex groups here causes severe slowdowns.\n\t// See https://connect.microsoft.com/IE/feedback/details/1736512/\n\trnoInnerhtml = /<script|<style|<link/i,\n\n\t// checked=\"checked\" or checked\n\trchecked = /checked\\s*(?:[^=]|=\\s*.checked.)/i,\n\n\trcleanScript = /^\\s*<!\\[CDATA\\[|\\]\\]>\\s*$/g;\n\n// Prefer a tbody over its parent table for containing new rows\nfunction manipulationTarget( elem, content ) {\n\tif ( nodeName( elem, \"table\" ) &&\n\t\tnodeName( content.nodeType !== 11 ? content : content.firstChild, \"tr\" ) ) {\n\n\t\treturn jQuery( elem ).children( \"tbody\" )[ 0 ] || elem;\n\t}\n\n\treturn elem;\n}\n\n// Replace/restore the type attribute of script elements for safe DOM manipulation\nfunction disableScript( elem ) {\n\telem.type = ( elem.getAttribute( \"type\" ) !== null ) + \"/\" + elem.type;\n\treturn elem;\n}\nfunction restoreScript( elem ) {\n\tif ( ( elem.type || \"\" ).slice( 0, 5 ) === \"true/\" ) {\n\t\telem.type = elem.type.slice( 5 );\n\t} else {\n\t\telem.removeAttribute( \"type\" );\n\t}\n\n\treturn elem;\n}\n\nfunction cloneCopyEvent( src, dest ) {\n\tvar i, l, type, pdataOld, udataOld, udataCur, events;\n\n\tif ( dest.nodeType !== 1 ) {\n\t\treturn;\n\t}\n\n\t// 1. Copy private data: events, handlers, etc.\n\tif ( dataPriv.hasData( src ) ) {\n\t\tpdataOld = dataPriv.get( src );\n\t\tevents = pdataOld.events;\n\n\t\tif ( events ) {\n\t\t\tdataPriv.remove( dest, \"handle events\" );\n\n\t\t\tfor ( type in events ) {\n\t\t\t\tfor ( i = 0, l = events[ type ].length; i < l; i++ ) {\n\t\t\t\t\tjQuery.event.add( dest, type, events[ type ][ i ] );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// 2. Copy user data\n\tif ( dataUser.hasData( src ) ) {\n\t\tudataOld = dataUser.access( src );\n\t\tudataCur = jQuery.extend( {}, udataOld );\n\n\t\tdataUser.set( dest, udataCur );\n\t}\n}\n\n// Fix IE bugs, see support tests\nfunction fixInput( src, dest ) {\n\tvar nodeName = dest.nodeName.toLowerCase();\n\n\t// Fails to persist the checked state of a cloned checkbox or radio button.\n\tif ( nodeName === \"input\" && rcheckableType.test( src.type ) ) {\n\t\tdest.checked = src.checked;\n\n\t// Fails to return the selected option to the default selected state when cloning options\n\t} else if ( nodeName === \"input\" || nodeName === \"textarea\" ) {\n\t\tdest.defaultValue = src.defaultValue;\n\t}\n}\n\nfunction domManip( collection, args, callback, ignored ) {\n\n\t// Flatten any nested arrays\n\targs = flat( args );\n\n\tvar fragment, first, scripts, hasScripts, node, doc,\n\t\ti = 0,\n\t\tl = collection.length,\n\t\tiNoClone = l - 1,\n\t\tvalue = args[ 0 ],\n\t\tvalueIsFunction = isFunction( value );\n\n\t// We can't cloneNode fragments that contain checked, in WebKit\n\tif ( valueIsFunction ||\n\t\t\t( l > 1 && typeof value === \"string\" &&\n\t\t\t\t!support.checkClone && rchecked.test( value ) ) ) {\n\t\treturn collection.each( function( index ) {\n\t\t\tvar self = collection.eq( index );\n\t\t\tif ( valueIsFunction ) {\n\t\t\t\targs[ 0 ] = value.call( this, index, self.html() );\n\t\t\t}\n\t\t\tdomManip( self, args, callback, ignored );\n\t\t} );\n\t}\n\n\tif ( l ) {\n\t\tfragment = buildFragment( args, collection[ 0 ].ownerDocument, false, collection, ignored );\n\t\tfirst = fragment.firstChild;\n\n\t\tif ( fragment.childNodes.length === 1 ) {\n\t\t\tfragment = first;\n\t\t}\n\n\t\t// Require either new content or an interest in ignored elements to invoke the callback\n\t\tif ( first || ignored ) {\n\t\t\tscripts = jQuery.map( getAll( fragment, \"script\" ), disableScript );\n\t\t\thasScripts = scripts.length;\n\n\t\t\t// Use the original fragment for the last item\n\t\t\t// instead of the first because it can end up\n\t\t\t// being emptied incorrectly in certain situations (trac-8070).\n\t\t\tfor ( ; i < l; i++ ) {\n\t\t\t\tnode = fragment;\n\n\t\t\t\tif ( i !== iNoClone ) {\n\t\t\t\t\tnode = jQuery.clone( node, true, true );\n\n\t\t\t\t\t// Keep references to cloned scripts for later restoration\n\t\t\t\t\tif ( hasScripts ) {\n\n\t\t\t\t\t\t// Support: Android <=4.0 only, PhantomJS 1 only\n\t\t\t\t\t\t// push.apply(_, arraylike) throws on ancient WebKit\n\t\t\t\t\t\tjQuery.merge( scripts, getAll( node, \"script\" ) );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tcallback.call( collection[ i ], node, i );\n\t\t\t}\n\n\t\t\tif ( hasScripts ) {\n\t\t\t\tdoc = scripts[ scripts.length - 1 ].ownerDocument;\n\n\t\t\t\t// Re-enable scripts\n\t\t\t\tjQuery.map( scripts, restoreScript );\n\n\t\t\t\t// Evaluate executable scripts on first document insertion\n\t\t\t\tfor ( i = 0; i < hasScripts; i++ ) {\n\t\t\t\t\tnode = scripts[ i ];\n\t\t\t\t\tif ( rscriptType.test( node.type || \"\" ) &&\n\t\t\t\t\t\t!dataPriv.access( node, \"globalEval\" ) &&\n\t\t\t\t\t\tjQuery.contains( doc, node ) ) {\n\n\t\t\t\t\t\tif ( node.src && ( node.type || \"\" ).toLowerCase()  !== \"module\" ) {\n\n\t\t\t\t\t\t\t// Optional AJAX dependency, but won't run scripts if not present\n\t\t\t\t\t\t\tif ( jQuery._evalUrl && !node.noModule ) {\n\t\t\t\t\t\t\t\tjQuery._evalUrl( node.src, {\n\t\t\t\t\t\t\t\t\tnonce: node.nonce || node.getAttribute( \"nonce\" )\n\t\t\t\t\t\t\t\t}, doc );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t// Unwrap a CDATA section containing script contents. This shouldn't be\n\t\t\t\t\t\t\t// needed as in XML documents they're already not visible when\n\t\t\t\t\t\t\t// inspecting element contents and in HTML documents they have no\n\t\t\t\t\t\t\t// meaning but we're preserving that logic for backwards compatibility.\n\t\t\t\t\t\t\t// This will be removed completely in 4.0. See gh-4904.\n\t\t\t\t\t\t\tDOMEval( node.textContent.replace( rcleanScript, \"\" ), node, doc );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn collection;\n}\n\nfunction remove( elem, selector, keepData ) {\n\tvar node,\n\t\tnodes = selector ? jQuery.filter( selector, elem ) : elem,\n\t\ti = 0;\n\n\tfor ( ; ( node = nodes[ i ] ) != null; i++ ) {\n\t\tif ( !keepData && node.nodeType === 1 ) {\n\t\t\tjQuery.cleanData( getAll( node ) );\n\t\t}\n\n\t\tif ( node.parentNode ) {\n\t\t\tif ( keepData && isAttached( node ) ) {\n\t\t\t\tsetGlobalEval( getAll( node, \"script\" ) );\n\t\t\t}\n\t\t\tnode.parentNode.removeChild( node );\n\t\t}\n\t}\n\n\treturn elem;\n}\n\njQuery.extend( {\n\thtmlPrefilter: function( html ) {\n\t\treturn html;\n\t},\n\n\tclone: function( elem, dataAndEvents, deepDataAndEvents ) {\n\t\tvar i, l, srcElements, destElements,\n\t\t\tclone = elem.cloneNode( true ),\n\t\t\tinPage = isAttached( elem );\n\n\t\t// Fix IE cloning issues\n\t\tif ( !support.noCloneChecked && ( elem.nodeType === 1 || elem.nodeType === 11 ) &&\n\t\t\t\t!jQuery.isXMLDoc( elem ) ) {\n\n\t\t\t// We eschew jQuery#find here for performance reasons:\n\t\t\t// https://jsperf.com/getall-vs-sizzle/2\n\t\t\tdestElements = getAll( clone );\n\t\t\tsrcElements = getAll( elem );\n\n\t\t\tfor ( i = 0, l = srcElements.length; i < l; i++ ) {\n\t\t\t\tfixInput( srcElements[ i ], destElements[ i ] );\n\t\t\t}\n\t\t}\n\n\t\t// Copy the events from the original to the clone\n\t\tif ( dataAndEvents ) {\n\t\t\tif ( deepDataAndEvents ) {\n\t\t\t\tsrcElements = srcElements || getAll( elem );\n\t\t\t\tdestElements = destElements || getAll( clone );\n\n\t\t\t\tfor ( i = 0, l = srcElements.length; i < l; i++ ) {\n\t\t\t\t\tcloneCopyEvent( srcElements[ i ], destElements[ i ] );\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcloneCopyEvent( elem, clone );\n\t\t\t}\n\t\t}\n\n\t\t// Preserve script evaluation history\n\t\tdestElements = getAll( clone, \"script\" );\n\t\tif ( destElements.length > 0 ) {\n\t\t\tsetGlobalEval( destElements, !inPage && getAll( elem, \"script\" ) );\n\t\t}\n\n\t\t// Return the cloned set\n\t\treturn clone;\n\t},\n\n\tcleanData: function( elems ) {\n\t\tvar data, elem, type,\n\t\t\tspecial = jQuery.event.special,\n\t\t\ti = 0;\n\n\t\tfor ( ; ( elem = elems[ i ] ) !== undefined; i++ ) {\n\t\t\tif ( acceptData( elem ) ) {\n\t\t\t\tif ( ( data = elem[ dataPriv.expando ] ) ) {\n\t\t\t\t\tif ( data.events ) {\n\t\t\t\t\t\tfor ( type in data.events ) {\n\t\t\t\t\t\t\tif ( special[ type ] ) {\n\t\t\t\t\t\t\t\tjQuery.event.remove( elem, type );\n\n\t\t\t\t\t\t\t// This is a shortcut to avoid jQuery.event.remove's overhead\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tjQuery.removeEvent( elem, type, data.handle );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// Support: Chrome <=35 - 45+\n\t\t\t\t\t// Assign undefined instead of using delete, see Data#remove\n\t\t\t\t\telem[ dataPriv.expando ] = undefined;\n\t\t\t\t}\n\t\t\t\tif ( elem[ dataUser.expando ] ) {\n\n\t\t\t\t\t// Support: Chrome <=35 - 45+\n\t\t\t\t\t// Assign undefined instead of using delete, see Data#remove\n\t\t\t\t\telem[ dataUser.expando ] = undefined;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n} );\n\njQuery.fn.extend( {\n\tdetach: function( selector ) {\n\t\treturn remove( this, selector, true );\n\t},\n\n\tremove: function( selector ) {\n\t\treturn remove( this, selector );\n\t},\n\n\ttext: function( value ) {\n\t\treturn access( this, function( value ) {\n\t\t\treturn value === undefined ?\n\t\t\t\tjQuery.text( this ) :\n\t\t\t\tthis.empty().each( function() {\n\t\t\t\t\tif ( this.nodeType === 1 || this.nodeType === 11 || this.nodeType === 9 ) {\n\t\t\t\t\t\tthis.textContent = value;\n\t\t\t\t\t}\n\t\t\t\t} );\n\t\t}, null, value, arguments.length );\n\t},\n\n\tappend: function() {\n\t\treturn domManip( this, arguments, function( elem ) {\n\t\t\tif ( this.nodeType === 1 || this.nodeType === 11 || this.nodeType === 9 ) {\n\t\t\t\tvar target = manipulationTarget( this, elem );\n\t\t\t\ttarget.appendChild( elem );\n\t\t\t}\n\t\t} );\n\t},\n\n\tprepend: function() {\n\t\treturn domManip( this, arguments, function( elem ) {\n\t\t\tif ( this.nodeType === 1 || this.nodeType === 11 || this.nodeType === 9 ) {\n\t\t\t\tvar target = manipulationTarget( this, elem );\n\t\t\t\ttarget.insertBefore( elem, target.firstChild );\n\t\t\t}\n\t\t} );\n\t},\n\n\tbefore: function() {\n\t\treturn domManip( this, arguments, function( elem ) {\n\t\t\tif ( this.parentNode ) {\n\t\t\t\tthis.parentNode.insertBefore( elem, this );\n\t\t\t}\n\t\t} );\n\t},\n\n\tafter: function() {\n\t\treturn domManip( this, arguments, function( elem ) {\n\t\t\tif ( this.parentNode ) {\n\t\t\t\tthis.parentNode.insertBefore( elem, this.nextSibling );\n\t\t\t}\n\t\t} );\n\t},\n\n\tempty: function() {\n\t\tvar elem,\n\t\t\ti = 0;\n\n\t\tfor ( ; ( elem = this[ i ] ) != null; i++ ) {\n\t\t\tif ( elem.nodeType === 1 ) {\n\n\t\t\t\t// Prevent memory leaks\n\t\t\t\tjQuery.cleanData( getAll( elem, false ) );\n\n\t\t\t\t// Remove any remaining nodes\n\t\t\t\telem.textContent = \"\";\n\t\t\t}\n\t\t}\n\n\t\treturn this;\n\t},\n\n\tclone: function( dataAndEvents, deepDataAndEvents ) {\n\t\tdataAndEvents = dataAndEvents == null ? false : dataAndEvents;\n\t\tdeepDataAndEvents = deepDataAndEvents == null ? dataAndEvents : deepDataAndEvents;\n\n\t\treturn this.map( function() {\n\t\t\treturn jQuery.clone( this, dataAndEvents, deepDataAndEvents );\n\t\t} );\n\t},\n\n\thtml: function( value ) {\n\t\treturn access( this, function( value ) {\n\t\t\tvar elem = this[ 0 ] || {},\n\t\t\t\ti = 0,\n\t\t\t\tl = this.length;\n\n\t\t\tif ( value === undefined && elem.nodeType === 1 ) {\n\t\t\t\treturn elem.innerHTML;\n\t\t\t}\n\n\t\t\t// See if we can take a shortcut and just use innerHTML\n\t\t\tif ( typeof value === \"string\" && !rnoInnerhtml.test( value ) &&\n\t\t\t\t!wrapMap[ ( rtagName.exec( value ) || [ \"\", \"\" ] )[ 1 ].toLowerCase() ] ) {\n\n\t\t\t\tvalue = jQuery.htmlPrefilter( value );\n\n\t\t\t\ttry {\n\t\t\t\t\tfor ( ; i < l; i++ ) {\n\t\t\t\t\t\telem = this[ i ] || {};\n\n\t\t\t\t\t\t// Remove element nodes and prevent memory leaks\n\t\t\t\t\t\tif ( elem.nodeType === 1 ) {\n\t\t\t\t\t\t\tjQuery.cleanData( getAll( elem, false ) );\n\t\t\t\t\t\t\telem.innerHTML = value;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\telem = 0;\n\n\t\t\t\t// If using innerHTML throws an exception, use the fallback method\n\t\t\t\t} catch ( e ) {}\n\t\t\t}\n\n\t\t\tif ( elem ) {\n\t\t\t\tthis.empty().append( value );\n\t\t\t}\n\t\t}, null, value, arguments.length );\n\t},\n\n\treplaceWith: function() {\n\t\tvar ignored = [];\n\n\t\t// Make the changes, replacing each non-ignored context element with the new content\n\t\treturn domManip( this, arguments, function( elem ) {\n\t\t\tvar parent = this.parentNode;\n\n\t\t\tif ( jQuery.inArray( this, ignored ) < 0 ) {\n\t\t\t\tjQuery.cleanData( getAll( this ) );\n\t\t\t\tif ( parent ) {\n\t\t\t\t\tparent.replaceChild( elem, this );\n\t\t\t\t}\n\t\t\t}\n\n\t\t// Force callback invocation\n\t\t}, ignored );\n\t}\n} );\n\njQuery.each( {\n\tappendTo: \"append\",\n\tprependTo: \"prepend\",\n\tinsertBefore: \"before\",\n\tinsertAfter: \"after\",\n\treplaceAll: \"replaceWith\"\n}, function( name, original ) {\n\tjQuery.fn[ name ] = function( selector ) {\n\t\tvar elems,\n\t\t\tret = [],\n\t\t\tinsert = jQuery( selector ),\n\t\t\tlast = insert.length - 1,\n\t\t\ti = 0;\n\n\t\tfor ( ; i <= last; i++ ) {\n\t\t\telems = i === last ? this : this.clone( true );\n\t\t\tjQuery( insert[ i ] )[ original ]( elems );\n\n\t\t\t// Support: Android <=4.0 only, PhantomJS 1 only\n\t\t\t// .get() because push.apply(_, arraylike) throws on ancient WebKit\n\t\t\tpush.apply( ret, elems.get() );\n\t\t}\n\n\t\treturn this.pushStack( ret );\n\t};\n} );\nvar rnumnonpx = new RegExp( \"^(\" + pnum + \")(?!px)[a-z%]+$\", \"i\" );\n\nvar rcustomProp = /^--/;\n\n\nvar getStyles = function( elem ) {\n\n\t\t// Support: IE <=11 only, Firefox <=30 (trac-15098, trac-14150)\n\t\t// IE throws on elements created in popups\n\t\t// FF meanwhile throws on frame elements through \"defaultView.getComputedStyle\"\n\t\tvar view = elem.ownerDocument.defaultView;\n\n\t\tif ( !view || !view.opener ) {\n\t\t\tview = window;\n\t\t}\n\n\t\treturn view.getComputedStyle( elem );\n\t};\n\nvar swap = function( elem, options, callback ) {\n\tvar ret, name,\n\t\told = {};\n\n\t// Remember the old values, and insert the new ones\n\tfor ( name in options ) {\n\t\told[ name ] = elem.style[ name ];\n\t\telem.style[ name ] = options[ name ];\n\t}\n\n\tret = callback.call( elem );\n\n\t// Revert the old values\n\tfor ( name in options ) {\n\t\telem.style[ name ] = old[ name ];\n\t}\n\n\treturn ret;\n};\n\n\nvar rboxStyle = new RegExp( cssExpand.join( \"|\" ), \"i\" );\n\n\n\n( function() {\n\n\t// Executing both pixelPosition & boxSizingReliable tests require only one layout\n\t// so they're executed at the same time to save the second computation.\n\tfunction computeStyleTests() {\n\n\t\t// This is a singleton, we need to execute it only once\n\t\tif ( !div ) {\n\t\t\treturn;\n\t\t}\n\n\t\tcontainer.style.cssText = \"position:absolute;left:-11111px;width:60px;\" +\n\t\t\t\"margin-top:1px;padding:0;border:0\";\n\t\tdiv.style.cssText =\n\t\t\t\"position:relative;display:block;box-sizing:border-box;overflow:scroll;\" +\n\t\t\t\"margin:auto;border:1px;padding:1px;\" +\n\t\t\t\"width:60%;top:1%\";\n\t\tdocumentElement.appendChild( container ).appendChild( div );\n\n\t\tvar divStyle = window.getComputedStyle( div );\n\t\tpixelPositionVal = divStyle.top !== \"1%\";\n\n\t\t// Support: Android 4.0 - 4.3 only, Firefox <=3 - 44\n\t\treliableMarginLeftVal = roundPixelMeasures( divStyle.marginLeft ) === 12;\n\n\t\t// Support: Android 4.0 - 4.3 only, Safari <=9.1 - 10.1, iOS <=7.0 - 9.3\n\t\t// Some styles come back with percentage values, even though they shouldn't\n\t\tdiv.style.right = \"60%\";\n\t\tpixelBoxStylesVal = roundPixelMeasures( divStyle.right ) === 36;\n\n\t\t// Support: IE 9 - 11 only\n\t\t// Detect misreporting of content dimensions for box-sizing:border-box elements\n\t\tboxSizingReliableVal = roundPixelMeasures( divStyle.width ) === 36;\n\n\t\t// Support: IE 9 only\n\t\t// Detect overflow:scroll screwiness (gh-3699)\n\t\t// Support: Chrome <=64\n\t\t// Don't get tricked when zoom affects offsetWidth (gh-4029)\n\t\tdiv.style.position = \"absolute\";\n\t\tscrollboxSizeVal = roundPixelMeasures( div.offsetWidth / 3 ) === 12;\n\n\t\tdocumentElement.removeChild( container );\n\n\t\t// Nullify the div so it wouldn't be stored in the memory and\n\t\t// it will also be a sign that checks already performed\n\t\tdiv = null;\n\t}\n\n\tfunction roundPixelMeasures( measure ) {\n\t\treturn Math.round( parseFloat( measure ) );\n\t}\n\n\tvar pixelPositionVal, boxSizingReliableVal, scrollboxSizeVal, pixelBoxStylesVal,\n\t\treliableTrDimensionsVal, reliableMarginLeftVal,\n\t\tcontainer = document.createElement( \"div\" ),\n\t\tdiv = document.createElement( \"div\" );\n\n\t// Finish early in limited (non-browser) environments\n\tif ( !div.style ) {\n\t\treturn;\n\t}\n\n\t// Support: IE <=9 - 11 only\n\t// Style of cloned element affects source element cloned (trac-8908)\n\tdiv.style.backgroundClip = \"content-box\";\n\tdiv.cloneNode( true ).style.backgroundClip = \"\";\n\tsupport.clearCloneStyle = div.style.backgroundClip === \"content-box\";\n\n\tjQuery.extend( support, {\n\t\tboxSizingReliable: function() {\n\t\t\tcomputeStyleTests();\n\t\t\treturn boxSizingReliableVal;\n\t\t},\n\t\tpixelBoxStyles: function() {\n\t\t\tcomputeStyleTests();\n\t\t\treturn pixelBoxStylesVal;\n\t\t},\n\t\tpixelPosition: function() {\n\t\t\tcomputeStyleTests();\n\t\t\treturn pixelPositionVal;\n\t\t},\n\t\treliableMarginLeft: function() {\n\t\t\tcomputeStyleTests();\n\t\t\treturn reliableMarginLeftVal;\n\t\t},\n\t\tscrollboxSize: function() {\n\t\t\tcomputeStyleTests();\n\t\t\treturn scrollboxSizeVal;\n\t\t},\n\n\t\t// Support: IE 9 - 11+, Edge 15 - 18+\n\t\t// IE/Edge misreport `getComputedStyle` of table rows with width/height\n\t\t// set in CSS while `offset*` properties report correct values.\n\t\t// Behavior in IE 9 is more subtle than in newer versions & it passes\n\t\t// some versions of this test; make sure not to make it pass there!\n\t\t//\n\t\t// Support: Firefox 70+\n\t\t// Only Firefox includes border widths\n\t\t// in computed dimensions. (gh-4529)\n\t\treliableTrDimensions: function() {\n\t\t\tvar table, tr, trChild, trStyle;\n\t\t\tif ( reliableTrDimensionsVal == null ) {\n\t\t\t\ttable = document.createElement( \"table\" );\n\t\t\t\ttr = document.createElement( \"tr\" );\n\t\t\t\ttrChild = document.createElement( \"div\" );\n\n\t\t\t\ttable.style.cssText = \"position:absolute;left:-11111px;border-collapse:separate\";\n\t\t\t\ttr.style.cssText = \"box-sizing:content-box;border:1px solid\";\n\n\t\t\t\t// Support: Chrome 86+\n\t\t\t\t// Height set through cssText does not get applied.\n\t\t\t\t// Computed height then comes back as 0.\n\t\t\t\ttr.style.height = \"1px\";\n\t\t\t\ttrChild.style.height = \"9px\";\n\n\t\t\t\t// Support: Android 8 Chrome 86+\n\t\t\t\t// In our bodyBackground.html iframe,\n\t\t\t\t// display for all div elements is set to \"inline\",\n\t\t\t\t// which causes a problem only in Android 8 Chrome 86.\n\t\t\t\t// Ensuring the div is `display: block`\n\t\t\t\t// gets around this issue.\n\t\t\t\ttrChild.style.display = \"block\";\n\n\t\t\t\tdocumentElement\n\t\t\t\t\t.appendChild( table )\n\t\t\t\t\t.appendChild( tr )\n\t\t\t\t\t.appendChild( trChild );\n\n\t\t\t\ttrStyle = window.getComputedStyle( tr );\n\t\t\t\treliableTrDimensionsVal = ( parseInt( trStyle.height, 10 ) +\n\t\t\t\t\tparseInt( trStyle.borderTopWidth, 10 ) +\n\t\t\t\t\tparseInt( trStyle.borderBottomWidth, 10 ) ) === tr.offsetHeight;\n\n\t\t\t\tdocumentElement.removeChild( table );\n\t\t\t}\n\t\t\treturn reliableTrDimensionsVal;\n\t\t}\n\t} );\n} )();\n\n\nfunction curCSS( elem, name, computed ) {\n\tvar width, minWidth, maxWidth, ret,\n\t\tisCustomProp = rcustomProp.test( name ),\n\n\t\t// Support: Firefox 51+\n\t\t// Retrieving style before computed somehow\n\t\t// fixes an issue with getting wrong values\n\t\t// on detached elements\n\t\tstyle = elem.style;\n\n\tcomputed = computed || getStyles( elem );\n\n\t// getPropertyValue is needed for:\n\t//   .css('filter') (IE 9 only, trac-12537)\n\t//   .css('--customProperty) (gh-3144)\n\tif ( computed ) {\n\n\t\t// Support: IE <=9 - 11+\n\t\t// IE only supports `\"float\"` in `getPropertyValue`; in computed styles\n\t\t// it's only available as `\"cssFloat\"`. We no longer modify properties\n\t\t// sent to `.css()` apart from camelCasing, so we need to check both.\n\t\t// Normally, this would create difference in behavior: if\n\t\t// `getPropertyValue` returns an empty string, the value returned\n\t\t// by `.css()` would be `undefined`. This is usually the case for\n\t\t// disconnected elements. However, in IE even disconnected elements\n\t\t// with no styles return `\"none\"` for `getPropertyValue( \"float\" )`\n\t\tret = computed.getPropertyValue( name ) || computed[ name ];\n\n\t\tif ( isCustomProp && ret ) {\n\n\t\t\t// Support: Firefox 105+, Chrome <=105+\n\t\t\t// Spec requires trimming whitespace for custom properties (gh-4926).\n\t\t\t// Firefox only trims leading whitespace. Chrome just collapses\n\t\t\t// both leading & trailing whitespace to a single space.\n\t\t\t//\n\t\t\t// Fall back to `undefined` if empty string returned.\n\t\t\t// This collapses a missing definition with property defined\n\t\t\t// and set to an empty string but there's no standard API\n\t\t\t// allowing us to differentiate them without a performance penalty\n\t\t\t// and returning `undefined` aligns with older jQuery.\n\t\t\t//\n\t\t\t// rtrimCSS treats U+000D CARRIAGE RETURN and U+000C FORM FEED\n\t\t\t// as whitespace while CSS does not, but this is not a problem\n\t\t\t// because CSS preprocessing replaces them with U+000A LINE FEED\n\t\t\t// (which *is* CSS whitespace)\n\t\t\t// https://www.w3.org/TR/css-syntax-3/#input-preprocessing\n\t\t\tret = ret.replace( rtrimCSS, \"$1\" ) || undefined;\n\t\t}\n\n\t\tif ( ret === \"\" && !isAttached( elem ) ) {\n\t\t\tret = jQuery.style( elem, name );\n\t\t}\n\n\t\t// A tribute to the \"awesome hack by Dean Edwards\"\n\t\t// Android Browser returns percentage for some values,\n\t\t// but width seems to be reliably pixels.\n\t\t// This is against the CSSOM draft spec:\n\t\t// https://drafts.csswg.org/cssom/#resolved-values\n\t\tif ( !support.pixelBoxStyles() && rnumnonpx.test( ret ) && rboxStyle.test( name ) ) {\n\n\t\t\t// Remember the original values\n\t\t\twidth = style.width;\n\t\t\tminWidth = style.minWidth;\n\t\t\tmaxWidth = style.maxWidth;\n\n\t\t\t// Put in the new values to get a computed value out\n\t\t\tstyle.minWidth = style.maxWidth = style.width = ret;\n\t\t\tret = computed.width;\n\n\t\t\t// Revert the changed values\n\t\t\tstyle.width = width;\n\t\t\tstyle.minWidth = minWidth;\n\t\t\tstyle.maxWidth = maxWidth;\n\t\t}\n\t}\n\n\treturn ret !== undefined ?\n\n\t\t// Support: IE <=9 - 11 only\n\t\t// IE returns zIndex value as an integer.\n\t\tret + \"\" :\n\t\tret;\n}\n\n\nfunction addGetHookIf( conditionFn, hookFn ) {\n\n\t// Define the hook, we'll check on the first run if it's really needed.\n\treturn {\n\t\tget: function() {\n\t\t\tif ( conditionFn() ) {\n\n\t\t\t\t// Hook not needed (or it's not possible to use it due\n\t\t\t\t// to missing dependency), remove it.\n\t\t\t\tdelete this.get;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Hook needed; redefine it so that the support test is not executed again.\n\t\t\treturn ( this.get = hookFn ).apply( this, arguments );\n\t\t}\n\t};\n}\n\n\nvar cssPrefixes = [ \"Webkit\", \"Moz\", \"ms\" ],\n\temptyStyle = document.createElement( \"div\" ).style,\n\tvendorProps = {};\n\n// Return a vendor-prefixed property or undefined\nfunction vendorPropName( name ) {\n\n\t// Check for vendor prefixed names\n\tvar capName = name[ 0 ].toUpperCase() + name.slice( 1 ),\n\t\ti = cssPrefixes.length;\n\n\twhile ( i-- ) {\n\t\tname = cssPrefixes[ i ] + capName;\n\t\tif ( name in emptyStyle ) {\n\t\t\treturn name;\n\t\t}\n\t}\n}\n\n// Return a potentially-mapped jQuery.cssProps or vendor prefixed property\nfunction finalPropName( name ) {\n\tvar final = jQuery.cssProps[ name ] || vendorProps[ name ];\n\n\tif ( final ) {\n\t\treturn final;\n\t}\n\tif ( name in emptyStyle ) {\n\t\treturn name;\n\t}\n\treturn vendorProps[ name ] = vendorPropName( name ) || name;\n}\n\n\nvar\n\n\t// Swappable if display is none or starts with table\n\t// except \"table\", \"table-cell\", or \"table-caption\"\n\t// See here for display values: https://developer.mozilla.org/en-US/docs/CSS/display\n\trdisplayswap = /^(none|table(?!-c[ea]).+)/,\n\tcssShow = { position: \"absolute\", visibility: \"hidden\", display: \"block\" },\n\tcssNormalTransform = {\n\t\tletterSpacing: \"0\",\n\t\tfontWeight: \"400\"\n\t};\n\nfunction setPositiveNumber( _elem, value, subtract ) {\n\n\t// Any relative (+/-) values have already been\n\t// normalized at this point\n\tvar matches = rcssNum.exec( value );\n\treturn matches ?\n\n\t\t// Guard against undefined \"subtract\", e.g., when used as in cssHooks\n\t\tMath.max( 0, matches[ 2 ] - ( subtract || 0 ) ) + ( matches[ 3 ] || \"px\" ) :\n\t\tvalue;\n}\n\nfunction boxModelAdjustment( elem, dimension, box, isBorderBox, styles, computedVal ) {\n\tvar i = dimension === \"width\" ? 1 : 0,\n\t\textra = 0,\n\t\tdelta = 0,\n\t\tmarginDelta = 0;\n\n\t// Adjustment may not be necessary\n\tif ( box === ( isBorderBox ? \"border\" : \"content\" ) ) {\n\t\treturn 0;\n\t}\n\n\tfor ( ; i < 4; i += 2 ) {\n\n\t\t// Both box models exclude margin\n\t\t// Count margin delta separately to only add it after scroll gutter adjustment.\n\t\t// This is needed to make negative margins work with `outerHeight( true )` (gh-3982).\n\t\tif ( box === \"margin\" ) {\n\t\t\tmarginDelta += jQuery.css( elem, box + cssExpand[ i ], true, styles );\n\t\t}\n\n\t\t// If we get here with a content-box, we're seeking \"padding\" or \"border\" or \"margin\"\n\t\tif ( !isBorderBox ) {\n\n\t\t\t// Add padding\n\t\t\tdelta += jQuery.css( elem, \"padding\" + cssExpand[ i ], true, styles );\n\n\t\t\t// For \"border\" or \"margin\", add border\n\t\t\tif ( box !== \"padding\" ) {\n\t\t\t\tdelta += jQuery.css( elem, \"border\" + cssExpand[ i ] + \"Width\", true, styles );\n\n\t\t\t// But still keep track of it otherwise\n\t\t\t} else {\n\t\t\t\textra += jQuery.css( elem, \"border\" + cssExpand[ i ] + \"Width\", true, styles );\n\t\t\t}\n\n\t\t// If we get here with a border-box (content + padding + border), we're seeking \"content\" or\n\t\t// \"padding\" or \"margin\"\n\t\t} else {\n\n\t\t\t// For \"content\", subtract padding\n\t\t\tif ( box === \"content\" ) {\n\t\t\t\tdelta -= jQuery.css( elem, \"padding\" + cssExpand[ i ], true, styles );\n\t\t\t}\n\n\t\t\t// For \"content\" or \"padding\", subtract border\n\t\t\tif ( box !== \"margin\" ) {\n\t\t\t\tdelta -= jQuery.css( elem, \"border\" + cssExpand[ i ] + \"Width\", true, styles );\n\t\t\t}\n\t\t}\n\t}\n\n\t// Account for positive content-box scroll gutter when requested by providing computedVal\n\tif ( !isBorderBox && computedVal >= 0 ) {\n\n\t\t// offsetWidth/offsetHeight is a rounded sum of content, padding, scroll gutter, and border\n\t\t// Assuming integer scroll gutter, subtract the rest and round down\n\t\tdelta += Math.max( 0, Math.ceil(\n\t\t\telem[ \"offset\" + dimension[ 0 ].toUpperCase() + dimension.slice( 1 ) ] -\n\t\t\tcomputedVal -\n\t\t\tdelta -\n\t\t\textra -\n\t\t\t0.5\n\n\t\t// If offsetWidth/offsetHeight is unknown, then we can't determine content-box scroll gutter\n\t\t// Use an explicit zero to avoid NaN (gh-3964)\n\t\t) ) || 0;\n\t}\n\n\treturn delta + marginDelta;\n}\n\nfunction getWidthOrHeight( elem, dimension, extra ) {\n\n\t// Start with computed style\n\tvar styles = getStyles( elem ),\n\n\t\t// To avoid forcing a reflow, only fetch boxSizing if we need it (gh-4322).\n\t\t// Fake content-box until we know it's needed to know the true value.\n\t\tboxSizingNeeded = !support.boxSizingReliable() || extra,\n\t\tisBorderBox = boxSizingNeeded &&\n\t\t\tjQuery.css( elem, \"boxSizing\", false, styles ) === \"border-box\",\n\t\tvalueIsBorderBox = isBorderBox,\n\n\t\tval = curCSS( elem, dimension, styles ),\n\t\toffsetProp = \"offset\" + dimension[ 0 ].toUpperCase() + dimension.slice( 1 );\n\n\t// Support: Firefox <=54\n\t// Return a confounding non-pixel value or feign ignorance, as appropriate.\n\tif ( rnumnonpx.test( val ) ) {\n\t\tif ( !extra ) {\n\t\t\treturn val;\n\t\t}\n\t\tval = \"auto\";\n\t}\n\n\n\t// Support: IE 9 - 11 only\n\t// Use offsetWidth/offsetHeight for when box sizing is unreliable.\n\t// In those cases, the computed value can be trusted to be border-box.\n\tif ( ( !support.boxSizingReliable() && isBorderBox ||\n\n\t\t// Support: IE 10 - 11+, Edge 15 - 18+\n\t\t// IE/Edge misreport `getComputedStyle` of table rows with width/height\n\t\t// set in CSS while `offset*` properties report correct values.\n\t\t// Interestingly, in some cases IE 9 doesn't suffer from this issue.\n\t\t!support.reliableTrDimensions() && nodeName( elem, \"tr\" ) ||\n\n\t\t// Fall back to offsetWidth/offsetHeight when value is \"auto\"\n\t\t// This happens for inline elements with no explicit setting (gh-3571)\n\t\tval === \"auto\" ||\n\n\t\t// Support: Android <=4.1 - 4.3 only\n\t\t// Also use offsetWidth/offsetHeight for misreported inline dimensions (gh-3602)\n\t\t!parseFloat( val ) && jQuery.css( elem, \"display\", false, styles ) === \"inline\" ) &&\n\n\t\t// Make sure the element is visible & connected\n\t\telem.getClientRects().length ) {\n\n\t\tisBorderBox = jQuery.css( elem, \"boxSizing\", false, styles ) === \"border-box\";\n\n\t\t// Where available, offsetWidth/offsetHeight approximate border box dimensions.\n\t\t// Where not available (e.g., SVG), assume unreliable box-sizing and interpret the\n\t\t// retrieved value as a content box dimension.\n\t\tvalueIsBorderBox = offsetProp in elem;\n\t\tif ( valueIsBorderBox ) {\n\t\t\tval = elem[ offsetProp ];\n\t\t}\n\t}\n\n\t// Normalize \"\" and auto\n\tval = parseFloat( val ) || 0;\n\n\t// Adjust for the element's box model\n\treturn ( val +\n\t\tboxModelAdjustment(\n\t\t\telem,\n\t\t\tdimension,\n\t\t\textra || ( isBorderBox ? \"border\" : \"content\" ),\n\t\t\tvalueIsBorderBox,\n\t\t\tstyles,\n\n\t\t\t// Provide the current computed size to request scroll gutter calculation (gh-3589)\n\t\t\tval\n\t\t)\n\t) + \"px\";\n}\n\njQuery.extend( {\n\n\t// Add in style property hooks for overriding the default\n\t// behavior of getting and setting a style property\n\tcssHooks: {\n\t\topacity: {\n\t\t\tget: function( elem, computed ) {\n\t\t\t\tif ( computed ) {\n\n\t\t\t\t\t// We should always get a number back from opacity\n\t\t\t\t\tvar ret = curCSS( elem, \"opacity\" );\n\t\t\t\t\treturn ret === \"\" ? \"1\" : ret;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\n\t// Don't automatically add \"px\" to these possibly-unitless properties\n\tcssNumber: {\n\t\tanimationIterationCount: true,\n\t\taspectRatio: true,\n\t\tborderImageSlice: true,\n\t\tcolumnCount: true,\n\t\tflexGrow: true,\n\t\tflexShrink: true,\n\t\tfontWeight: true,\n\t\tgridArea: true,\n\t\tgridColumn: true,\n\t\tgridColumnEnd: true,\n\t\tgridColumnStart: true,\n\t\tgridRow: true,\n\t\tgridRowEnd: true,\n\t\tgridRowStart: true,\n\t\tlineHeight: true,\n\t\topacity: true,\n\t\torder: true,\n\t\torphans: true,\n\t\tscale: true,\n\t\twidows: true,\n\t\tzIndex: true,\n\t\tzoom: true,\n\n\t\t// SVG-related\n\t\tfillOpacity: true,\n\t\tfloodOpacity: true,\n\t\tstopOpacity: true,\n\t\tstrokeMiterlimit: true,\n\t\tstrokeOpacity: true\n\t},\n\n\t// Add in properties whose names you wish to fix before\n\t// setting or getting the value\n\tcssProps: {},\n\n\t// Get and set the style property on a DOM Node\n\tstyle: function( elem, name, value, extra ) {\n\n\t\t// Don't set styles on text and comment nodes\n\t\tif ( !elem || elem.nodeType === 3 || elem.nodeType === 8 || !elem.style ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Make sure that we're working with the right name\n\t\tvar ret, type, hooks,\n\t\t\torigName = camelCase( name ),\n\t\t\tisCustomProp = rcustomProp.test( name ),\n\t\t\tstyle = elem.style;\n\n\t\t// Make sure that we're working with the right name. We don't\n\t\t// want to query the value if it is a CSS custom property\n\t\t// since they are user-defined.\n\t\tif ( !isCustomProp ) {\n\t\t\tname = finalPropName( origName );\n\t\t}\n\n\t\t// Gets hook for the prefixed version, then unprefixed version\n\t\thooks = jQuery.cssHooks[ name ] || jQuery.cssHooks[ origName ];\n\n\t\t// Check if we're setting a value\n\t\tif ( value !== undefined ) {\n\t\t\ttype = typeof value;\n\n\t\t\t// Convert \"+=\" or \"-=\" to relative numbers (trac-7345)\n\t\t\tif ( type === \"string\" && ( ret = rcssNum.exec( value ) ) && ret[ 1 ] ) {\n\t\t\t\tvalue = adjustCSS( elem, name, ret );\n\n\t\t\t\t// Fixes bug trac-9237\n\t\t\t\ttype = \"number\";\n\t\t\t}\n\n\t\t\t// Make sure that null and NaN values aren't set (trac-7116)\n\t\t\tif ( value == null || value !== value ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// If a number was passed in, add the unit (except for certain CSS properties)\n\t\t\t// The isCustomProp check can be removed in jQuery 4.0 when we only auto-append\n\t\t\t// \"px\" to a few hardcoded values.\n\t\t\tif ( type === \"number\" && !isCustomProp ) {\n\t\t\t\tvalue += ret && ret[ 3 ] || ( jQuery.cssNumber[ origName ] ? \"\" : \"px\" );\n\t\t\t}\n\n\t\t\t// background-* props affect original clone's values\n\t\t\tif ( !support.clearCloneStyle && value === \"\" && name.indexOf( \"background\" ) === 0 ) {\n\t\t\t\tstyle[ name ] = \"inherit\";\n\t\t\t}\n\n\t\t\t// If a hook was provided, use that value, otherwise just set the specified value\n\t\t\tif ( !hooks || !( \"set\" in hooks ) ||\n\t\t\t\t( value = hooks.set( elem, value, extra ) ) !== undefined ) {\n\n\t\t\t\tif ( isCustomProp ) {\n\t\t\t\t\tstyle.setProperty( name, value );\n\t\t\t\t} else {\n\t\t\t\t\tstyle[ name ] = value;\n\t\t\t\t}\n\t\t\t}\n\n\t\t} else {\n\n\t\t\t// If a hook was provided get the non-computed value from there\n\t\t\tif ( hooks && \"get\" in hooks &&\n\t\t\t\t( ret = hooks.get( elem, false, extra ) ) !== undefined ) {\n\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\t// Otherwise just get the value from the style object\n\t\t\treturn style[ name ];\n\t\t}\n\t},\n\n\tcss: function( elem, name, extra, styles ) {\n\t\tvar val, num, hooks,\n\t\t\torigName = camelCase( name ),\n\t\t\tisCustomProp = rcustomProp.test( name );\n\n\t\t// Make sure that we're working with the right name. We don't\n\t\t// want to modify the value if it is a CSS custom property\n\t\t// since they are user-defined.\n\t\tif ( !isCustomProp ) {\n\t\t\tname = finalPropName( origName );\n\t\t}\n\n\t\t// Try prefixed name followed by the unprefixed name\n\t\thooks = jQuery.cssHooks[ name ] || jQuery.cssHooks[ origName ];\n\n\t\t// If a hook was provided get the computed value from there\n\t\tif ( hooks && \"get\" in hooks ) {\n\t\t\tval = hooks.get( elem, true, extra );\n\t\t}\n\n\t\t// Otherwise, if a way to get the computed value exists, use that\n\t\tif ( val === undefined ) {\n\t\t\tval = curCSS( elem, name, styles );\n\t\t}\n\n\t\t// Convert \"normal\" to computed value\n\t\tif ( val === \"normal\" && name in cssNormalTransform ) {\n\t\t\tval = cssNormalTransform[ name ];\n\t\t}\n\n\t\t// Make numeric if forced or a qualifier was provided and val looks numeric\n\t\tif ( extra === \"\" || extra ) {\n\t\t\tnum = parseFloat( val );\n\t\t\treturn extra === true || isFinite( num ) ? num || 0 : val;\n\t\t}\n\n\t\treturn val;\n\t}\n} );\n\njQuery.each( [ \"height\", \"width\" ], function( _i, dimension ) {\n\tjQuery.cssHooks[ dimension ] = {\n\t\tget: function( elem, computed, extra ) {\n\t\t\tif ( computed ) {\n\n\t\t\t\t// Certain elements can have dimension info if we invisibly show them\n\t\t\t\t// but it must have a current display style that would benefit\n\t\t\t\treturn rdisplayswap.test( jQuery.css( elem, \"display\" ) ) &&\n\n\t\t\t\t\t// Support: Safari 8+\n\t\t\t\t\t// Table columns in Safari have non-zero offsetWidth & zero\n\t\t\t\t\t// getBoundingClientRect().width unless display is changed.\n\t\t\t\t\t// Support: IE <=11 only\n\t\t\t\t\t// Running getBoundingClientRect on a disconnected node\n\t\t\t\t\t// in IE throws an error.\n\t\t\t\t\t( !elem.getClientRects().length || !elem.getBoundingClientRect().width ) ?\n\t\t\t\t\tswap( elem, cssShow, function() {\n\t\t\t\t\t\treturn getWidthOrHeight( elem, dimension, extra );\n\t\t\t\t\t} ) :\n\t\t\t\t\tgetWidthOrHeight( elem, dimension, extra );\n\t\t\t}\n\t\t},\n\n\t\tset: function( elem, value, extra ) {\n\t\t\tvar matches,\n\t\t\t\tstyles = getStyles( elem ),\n\n\t\t\t\t// Only read styles.position if the test has a chance to fail\n\t\t\t\t// to avoid forcing a reflow.\n\t\t\t\tscrollboxSizeBuggy = !support.scrollboxSize() &&\n\t\t\t\t\tstyles.position === \"absolute\",\n\n\t\t\t\t// To avoid forcing a reflow, only fetch boxSizing if we need it (gh-3991)\n\t\t\t\tboxSizingNeeded = scrollboxSizeBuggy || extra,\n\t\t\t\tisBorderBox = boxSizingNeeded &&\n\t\t\t\t\tjQuery.css( elem, \"boxSizing\", false, styles ) === \"border-box\",\n\t\t\t\tsubtract = extra ?\n\t\t\t\t\tboxModelAdjustment(\n\t\t\t\t\t\telem,\n\t\t\t\t\t\tdimension,\n\t\t\t\t\t\textra,\n\t\t\t\t\t\tisBorderBox,\n\t\t\t\t\t\tstyles\n\t\t\t\t\t) :\n\t\t\t\t\t0;\n\n\t\t\t// Account for unreliable border-box dimensions by comparing offset* to computed and\n\t\t\t// faking a content-box to get border and padding (gh-3699)\n\t\t\tif ( isBorderBox && scrollboxSizeBuggy ) {\n\t\t\t\tsubtract -= Math.ceil(\n\t\t\t\t\telem[ \"offset\" + dimension[ 0 ].toUpperCase() + dimension.slice( 1 ) ] -\n\t\t\t\t\tparseFloat( styles[ dimension ] ) -\n\t\t\t\t\tboxModelAdjustment( elem, dimension, \"border\", false, styles ) -\n\t\t\t\t\t0.5\n\t\t\t\t);\n\t\t\t}\n\n\t\t\t// Convert to pixels if value adjustment is needed\n\t\t\tif ( subtract && ( matches = rcssNum.exec( value ) ) &&\n\t\t\t\t( matches[ 3 ] || \"px\" ) !== \"px\" ) {\n\n\t\t\t\telem.style[ dimension ] = value;\n\t\t\t\tvalue = jQuery.css( elem, dimension );\n\t\t\t}\n\n\t\t\treturn setPositiveNumber( elem, value, subtract );\n\t\t}\n\t};\n} );\n\njQuery.cssHooks.marginLeft = addGetHookIf( support.reliableMarginLeft,\n\tfunction( elem, computed ) {\n\t\tif ( computed ) {\n\t\t\treturn ( parseFloat( curCSS( elem, \"marginLeft\" ) ) ||\n\t\t\t\telem.getBoundingClientRect().left -\n\t\t\t\t\tswap( elem, { marginLeft: 0 }, function() {\n\t\t\t\t\t\treturn elem.getBoundingClientRect().left;\n\t\t\t\t\t} )\n\t\t\t) + \"px\";\n\t\t}\n\t}\n);\n\n// These hooks are used by animate to expand properties\njQuery.each( {\n\tmargin: \"\",\n\tpadding: \"\",\n\tborder: \"Width\"\n}, function( prefix, suffix ) {\n\tjQuery.cssHooks[ prefix + suffix ] = {\n\t\texpand: function( value ) {\n\t\t\tvar i = 0,\n\t\t\t\texpanded = {},\n\n\t\t\t\t// Assumes a single number if not a string\n\t\t\t\tparts = typeof value === \"string\" ? value.split( \" \" ) : [ value ];\n\n\t\t\tfor ( ; i < 4; i++ ) {\n\t\t\t\texpanded[ prefix + cssExpand[ i ] + suffix ] =\n\t\t\t\t\tparts[ i ] || parts[ i - 2 ] || parts[ 0 ];\n\t\t\t}\n\n\t\t\treturn expanded;\n\t\t}\n\t};\n\n\tif ( prefix !== \"margin\" ) {\n\t\tjQuery.cssHooks[ prefix + suffix ].set = setPositiveNumber;\n\t}\n} );\n\njQuery.fn.extend( {\n\tcss: function( name, value ) {\n\t\treturn access( this, function( elem, name, value ) {\n\t\t\tvar styles, len,\n\t\t\t\tmap = {},\n\t\t\t\ti = 0;\n\n\t\t\tif ( Array.isArray( name ) ) {\n\t\t\t\tstyles = getStyles( elem );\n\t\t\t\tlen = name.length;\n\n\t\t\t\tfor ( ; i < len; i++ ) {\n\t\t\t\t\tmap[ name[ i ] ] = jQuery.css( elem, name[ i ], false, styles );\n\t\t\t\t}\n\n\t\t\t\treturn map;\n\t\t\t}\n\n\t\t\treturn value !== undefined ?\n\t\t\t\tjQuery.style( elem, name, value ) :\n\t\t\t\tjQuery.css( elem, name );\n\t\t}, name, value, arguments.length > 1 );\n\t}\n} );\n\n\nfunction Tween( elem, options, prop, end, easing ) {\n\treturn new Tween.prototype.init( elem, options, prop, end, easing );\n}\njQuery.Tween = Tween;\n\nTween.prototype = {\n\tconstructor: Tween,\n\tinit: function( elem, options, prop, end, easing, unit ) {\n\t\tthis.elem = elem;\n\t\tthis.prop = prop;\n\t\tthis.easing = easing || jQuery.easing._default;\n\t\tthis.options = options;\n\t\tthis.start = this.now = this.cur();\n\t\tthis.end = end;\n\t\tthis.unit = unit || ( jQuery.cssNumber[ prop ] ? \"\" : \"px\" );\n\t},\n\tcur: function() {\n\t\tvar hooks = Tween.propHooks[ this.prop ];\n\n\t\treturn hooks && hooks.get ?\n\t\t\thooks.get( this ) :\n\t\t\tTween.propHooks._default.get( this );\n\t},\n\trun: function( percent ) {\n\t\tvar eased,\n\t\t\thooks = Tween.propHooks[ this.prop ];\n\n\t\tif ( this.options.duration ) {\n\t\t\tthis.pos = eased = jQuery.easing[ this.easing ](\n\t\t\t\tpercent, this.options.duration * percent, 0, 1, this.options.duration\n\t\t\t);\n\t\t} else {\n\t\t\tthis.pos = eased = percent;\n\t\t}\n\t\tthis.now = ( this.end - this.start ) * eased + this.start;\n\n\t\tif ( this.options.step ) {\n\t\t\tthis.options.step.call( this.elem, this.now, this );\n\t\t}\n\n\t\tif ( hooks && hooks.set ) {\n\t\t\thooks.set( this );\n\t\t} else {\n\t\t\tTween.propHooks._default.set( this );\n\t\t}\n\t\treturn this;\n\t}\n};\n\nTween.prototype.init.prototype = Tween.prototype;\n\nTween.propHooks = {\n\t_default: {\n\t\tget: function( tween ) {\n\t\t\tvar result;\n\n\t\t\t// Use a property on the element directly when it is not a DOM element,\n\t\t\t// or when there is no matching style property that exists.\n\t\t\tif ( tween.elem.nodeType !== 1 ||\n\t\t\t\ttween.elem[ tween.prop ] != null && tween.elem.style[ tween.prop ] == null ) {\n\t\t\t\treturn tween.elem[ tween.prop ];\n\t\t\t}\n\n\t\t\t// Passing an empty string as a 3rd parameter to .css will automatically\n\t\t\t// attempt a parseFloat and fallback to a string if the parse fails.\n\t\t\t// Simple values such as \"10px\" are parsed to Float;\n\t\t\t// complex values such as \"rotate(1rad)\" are returned as-is.\n\t\t\tresult = jQuery.css( tween.elem, tween.prop, \"\" );\n\n\t\t\t// Empty strings, null, undefined and \"auto\" are converted to 0.\n\t\t\treturn !result || result === \"auto\" ? 0 : result;\n\t\t},\n\t\tset: function( tween ) {\n\n\t\t\t// Use step hook for back compat.\n\t\t\t// Use cssHook if its there.\n\t\t\t// Use .style if available and use plain properties where available.\n\t\t\tif ( jQuery.fx.step[ tween.prop ] ) {\n\t\t\t\tjQuery.fx.step[ tween.prop ]( tween );\n\t\t\t} else if ( tween.elem.nodeType === 1 && (\n\t\t\t\tjQuery.cssHooks[ tween.prop ] ||\n\t\t\t\t\ttween.elem.style[ finalPropName( tween.prop ) ] != null ) ) {\n\t\t\t\tjQuery.style( tween.elem, tween.prop, tween.now + tween.unit );\n\t\t\t} else {\n\t\t\t\ttween.elem[ tween.prop ] = tween.now;\n\t\t\t}\n\t\t}\n\t}\n};\n\n// Support: IE <=9 only\n// Panic based approach to setting things on disconnected nodes\nTween.propHooks.scrollTop = Tween.propHooks.scrollLeft = {\n\tset: function( tween ) {\n\t\tif ( tween.elem.nodeType && tween.elem.parentNode ) {\n\t\t\ttween.elem[ tween.prop ] = tween.now;\n\t\t}\n\t}\n};\n\njQuery.easing = {\n\tlinear: function( p ) {\n\t\treturn p;\n\t},\n\tswing: function( p ) {\n\t\treturn 0.5 - Math.cos( p * Math.PI ) / 2;\n\t},\n\t_default: \"swing\"\n};\n\njQuery.fx = Tween.prototype.init;\n\n// Back compat <1.8 extension point\njQuery.fx.step = {};\n\n\n\n\nvar\n\tfxNow, inProgress,\n\trfxtypes = /^(?:toggle|show|hide)$/,\n\trrun = /queueHooks$/;\n\nfunction schedule() {\n\tif ( inProgress ) {\n\t\tif ( document.hidden === false && window.requestAnimationFrame ) {\n\t\t\twindow.requestAnimationFrame( schedule );\n\t\t} else {\n\t\t\twindow.setTimeout( schedule, jQuery.fx.interval );\n\t\t}\n\n\t\tjQuery.fx.tick();\n\t}\n}\n\n// Animations created synchronously will run synchronously\nfunction createFxNow() {\n\twindow.setTimeout( function() {\n\t\tfxNow = undefined;\n\t} );\n\treturn ( fxNow = Date.now() );\n}\n\n// Generate parameters to create a standard animation\nfunction genFx( type, includeWidth ) {\n\tvar which,\n\t\ti = 0,\n\t\tattrs = { height: type };\n\n\t// If we include width, step value is 1 to do all cssExpand values,\n\t// otherwise step value is 2 to skip over Left and Right\n\tincludeWidth = includeWidth ? 1 : 0;\n\tfor ( ; i < 4; i += 2 - includeWidth ) {\n\t\twhich = cssExpand[ i ];\n\t\tattrs[ \"margin\" + which ] = attrs[ \"padding\" + which ] = type;\n\t}\n\n\tif ( includeWidth ) {\n\t\tattrs.opacity = attrs.width = type;\n\t}\n\n\treturn attrs;\n}\n\nfunction createTween( value, prop, animation ) {\n\tvar tween,\n\t\tcollection = ( Animation.tweeners[ prop ] || [] ).concat( Animation.tweeners[ \"*\" ] ),\n\t\tindex = 0,\n\t\tlength = collection.length;\n\tfor ( ; index < length; index++ ) {\n\t\tif ( ( tween = collection[ index ].call( animation, prop, value ) ) ) {\n\n\t\t\t// We're done with this property\n\t\t\treturn tween;\n\t\t}\n\t}\n}\n\nfunction defaultPrefilter( elem, props, opts ) {\n\tvar prop, value, toggle, hooks, oldfire, propTween, restoreDisplay, display,\n\t\tisBox = \"width\" in props || \"height\" in props,\n\t\tanim = this,\n\t\torig = {},\n\t\tstyle = elem.style,\n\t\thidden = elem.nodeType && isHiddenWithinTree( elem ),\n\t\tdataShow = dataPriv.get( elem, \"fxshow\" );\n\n\t// Queue-skipping animations hijack the fx hooks\n\tif ( !opts.queue ) {\n\t\thooks = jQuery._queueHooks( elem, \"fx\" );\n\t\tif ( hooks.unqueued == null ) {\n\t\t\thooks.unqueued = 0;\n\t\t\toldfire = hooks.empty.fire;\n\t\t\thooks.empty.fire = function() {\n\t\t\t\tif ( !hooks.unqueued ) {\n\t\t\t\t\toldfire();\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\t\thooks.unqueued++;\n\n\t\tanim.always( function() {\n\n\t\t\t// Ensure the complete handler is called before this completes\n\t\t\tanim.always( function() {\n\t\t\t\thooks.unqueued--;\n\t\t\t\tif ( !jQuery.queue( elem, \"fx\" ).length ) {\n\t\t\t\t\thooks.empty.fire();\n\t\t\t\t}\n\t\t\t} );\n\t\t} );\n\t}\n\n\t// Detect show/hide animations\n\tfor ( prop in props ) {\n\t\tvalue = props[ prop ];\n\t\tif ( rfxtypes.test( value ) ) {\n\t\t\tdelete props[ prop ];\n\t\t\ttoggle = toggle || value === \"toggle\";\n\t\t\tif ( value === ( hidden ? \"hide\" : \"show\" ) ) {\n\n\t\t\t\t// Pretend to be hidden if this is a \"show\" and\n\t\t\t\t// there is still data from a stopped show/hide\n\t\t\t\tif ( value === \"show\" && dataShow && dataShow[ prop ] !== undefined ) {\n\t\t\t\t\thidden = true;\n\n\t\t\t\t// Ignore all other no-op show/hide data\n\t\t\t\t} else {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t}\n\t\t\torig[ prop ] = dataShow && dataShow[ prop ] || jQuery.style( elem, prop );\n\t\t}\n\t}\n\n\t// Bail out if this is a no-op like .hide().hide()\n\tpropTween = !jQuery.isEmptyObject( props );\n\tif ( !propTween && jQuery.isEmptyObject( orig ) ) {\n\t\treturn;\n\t}\n\n\t// Restrict \"overflow\" and \"display\" styles during box animations\n\tif ( isBox && elem.nodeType === 1 ) {\n\n\t\t// Support: IE <=9 - 11, Edge 12 - 15\n\t\t// Record all 3 overflow attributes because IE does not infer the shorthand\n\t\t// from identically-valued overflowX and overflowY and Edge just mirrors\n\t\t// the overflowX value there.\n\t\topts.overflow = [ style.overflow, style.overflowX, style.overflowY ];\n\n\t\t// Identify a display type, preferring old show/hide data over the CSS cascade\n\t\trestoreDisplay = dataShow && dataShow.display;\n\t\tif ( restoreDisplay == null ) {\n\t\t\trestoreDisplay = dataPriv.get( elem, \"display\" );\n\t\t}\n\t\tdisplay = jQuery.css( elem, \"display\" );\n\t\tif ( display === \"none\" ) {\n\t\t\tif ( restoreDisplay ) {\n\t\t\t\tdisplay = restoreDisplay;\n\t\t\t} else {\n\n\t\t\t\t// Get nonempty value(s) by temporarily forcing visibility\n\t\t\t\tshowHide( [ elem ], true );\n\t\t\t\trestoreDisplay = elem.style.display || restoreDisplay;\n\t\t\t\tdisplay = jQuery.css( elem, \"display\" );\n\t\t\t\tshowHide( [ elem ] );\n\t\t\t}\n\t\t}\n\n\t\t// Animate inline elements as inline-block\n\t\tif ( display === \"inline\" || display === \"inline-block\" && restoreDisplay != null ) {\n\t\t\tif ( jQuery.css( elem, \"float\" ) === \"none\" ) {\n\n\t\t\t\t// Restore the original display value at the end of pure show/hide animations\n\t\t\t\tif ( !propTween ) {\n\t\t\t\t\tanim.done( function() {\n\t\t\t\t\t\tstyle.display = restoreDisplay;\n\t\t\t\t\t} );\n\t\t\t\t\tif ( restoreDisplay == null ) {\n\t\t\t\t\t\tdisplay = style.display;\n\t\t\t\t\t\trestoreDisplay = display === \"none\" ? \"\" : display;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tstyle.display = \"inline-block\";\n\t\t\t}\n\t\t}\n\t}\n\n\tif ( opts.overflow ) {\n\t\tstyle.overflow = \"hidden\";\n\t\tanim.always( function() {\n\t\t\tstyle.overflow = opts.overflow[ 0 ];\n\t\t\tstyle.overflowX = opts.overflow[ 1 ];\n\t\t\tstyle.overflowY = opts.overflow[ 2 ];\n\t\t} );\n\t}\n\n\t// Implement show/hide animations\n\tpropTween = false;\n\tfor ( prop in orig ) {\n\n\t\t// General show/hide setup for this element animation\n\t\tif ( !propTween ) {\n\t\t\tif ( dataShow ) {\n\t\t\t\tif ( \"hidden\" in dataShow ) {\n\t\t\t\t\thidden = dataShow.hidden;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tdataShow = dataPriv.access( elem, \"fxshow\", { display: restoreDisplay } );\n\t\t\t}\n\n\t\t\t// Store hidden/visible for toggle so `.stop().toggle()` \"reverses\"\n\t\t\tif ( toggle ) {\n\t\t\t\tdataShow.hidden = !hidden;\n\t\t\t}\n\n\t\t\t// Show elements before animating them\n\t\t\tif ( hidden ) {\n\t\t\t\tshowHide( [ elem ], true );\n\t\t\t}\n\n\t\t\t/* eslint-disable no-loop-func */\n\n\t\t\tanim.done( function() {\n\n\t\t\t\t/* eslint-enable no-loop-func */\n\n\t\t\t\t// The final step of a \"hide\" animation is actually hiding the element\n\t\t\t\tif ( !hidden ) {\n\t\t\t\t\tshowHide( [ elem ] );\n\t\t\t\t}\n\t\t\t\tdataPriv.remove( elem, \"fxshow\" );\n\t\t\t\tfor ( prop in orig ) {\n\t\t\t\t\tjQuery.style( elem, prop, orig[ prop ] );\n\t\t\t\t}\n\t\t\t} );\n\t\t}\n\n\t\t// Per-property setup\n\t\tpropTween = createTween( hidden ? dataShow[ prop ] : 0, prop, anim );\n\t\tif ( !( prop in dataShow ) ) {\n\t\t\tdataShow[ prop ] = propTween.start;\n\t\t\tif ( hidden ) {\n\t\t\t\tpropTween.end = propTween.start;\n\t\t\t\tpropTween.start = 0;\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction propFilter( props, specialEasing ) {\n\tvar index, name, easing, value, hooks;\n\n\t// camelCase, specialEasing and expand cssHook pass\n\tfor ( index in props ) {\n\t\tname = camelCase( index );\n\t\teasing = specialEasing[ name ];\n\t\tvalue = props[ index ];\n\t\tif ( Array.isArray( value ) ) {\n\t\t\teasing = value[ 1 ];\n\t\t\tvalue = props[ index ] = value[ 0 ];\n\t\t}\n\n\t\tif ( index !== name ) {\n\t\t\tprops[ name ] = value;\n\t\t\tdelete props[ index ];\n\t\t}\n\n\t\thooks = jQuery.cssHooks[ name ];\n\t\tif ( hooks && \"expand\" in hooks ) {\n\t\t\tvalue = hooks.expand( value );\n\t\t\tdelete props[ name ];\n\n\t\t\t// Not quite $.extend, this won't overwrite existing keys.\n\t\t\t// Reusing 'index' because we have the correct \"name\"\n\t\t\tfor ( index in value ) {\n\t\t\t\tif ( !( index in props ) ) {\n\t\t\t\t\tprops[ index ] = value[ index ];\n\t\t\t\t\tspecialEasing[ index ] = easing;\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tspecialEasing[ name ] = easing;\n\t\t}\n\t}\n}\n\nfunction Animation( elem, properties, options ) {\n\tvar result,\n\t\tstopped,\n\t\tindex = 0,\n\t\tlength = Animation.prefilters.length,\n\t\tdeferred = jQuery.Deferred().always( function() {\n\n\t\t\t// Don't match elem in the :animated selector\n\t\t\tdelete tick.elem;\n\t\t} ),\n\t\ttick = function() {\n\t\t\tif ( stopped ) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tvar currentTime = fxNow || createFxNow(),\n\t\t\t\tremaining = Math.max( 0, animation.startTime + animation.duration - currentTime ),\n\n\t\t\t\t// Support: Android 2.3 only\n\t\t\t\t// Archaic crash bug won't allow us to use `1 - ( 0.5 || 0 )` (trac-12497)\n\t\t\t\ttemp = remaining / animation.duration || 0,\n\t\t\t\tpercent = 1 - temp,\n\t\t\t\tindex = 0,\n\t\t\t\tlength = animation.tweens.length;\n\n\t\t\tfor ( ; index < length; index++ ) {\n\t\t\t\tanimation.tweens[ index ].run( percent );\n\t\t\t}\n\n\t\t\tdeferred.notifyWith( elem, [ animation, percent, remaining ] );\n\n\t\t\t// If there's more to do, yield\n\t\t\tif ( percent < 1 && length ) {\n\t\t\t\treturn remaining;\n\t\t\t}\n\n\t\t\t// If this was an empty animation, synthesize a final progress notification\n\t\t\tif ( !length ) {\n\t\t\t\tdeferred.notifyWith( elem, [ animation, 1, 0 ] );\n\t\t\t}\n\n\t\t\t// Resolve the animation and report its conclusion\n\t\t\tdeferred.resolveWith( elem, [ animation ] );\n\t\t\treturn false;\n\t\t},\n\t\tanimation = deferred.promise( {\n\t\t\telem: elem,\n\t\t\tprops: jQuery.extend( {}, properties ),\n\t\t\topts: jQuery.extend( true, {\n\t\t\t\tspecialEasing: {},\n\t\t\t\teasing: jQuery.easing._default\n\t\t\t}, options ),\n\t\t\toriginalProperties: properties,\n\t\t\toriginalOptions: options,\n\t\t\tstartTime: fxNow || createFxNow(),\n\t\t\tduration: options.duration,\n\t\t\ttweens: [],\n\t\t\tcreateTween: function( prop, end ) {\n\t\t\t\tvar tween = jQuery.Tween( elem, animation.opts, prop, end,\n\t\t\t\t\tanimation.opts.specialEasing[ prop ] || animation.opts.easing );\n\t\t\t\tanimation.tweens.push( tween );\n\t\t\t\treturn tween;\n\t\t\t},\n\t\t\tstop: function( gotoEnd ) {\n\t\t\t\tvar index = 0,\n\n\t\t\t\t\t// If we are going to the end, we want to run all the tweens\n\t\t\t\t\t// otherwise we skip this part\n\t\t\t\t\tlength = gotoEnd ? animation.tweens.length : 0;\n\t\t\t\tif ( stopped ) {\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\t\t\t\tstopped = true;\n\t\t\t\tfor ( ; index < length; index++ ) {\n\t\t\t\t\tanimation.tweens[ index ].run( 1 );\n\t\t\t\t}\n\n\t\t\t\t// Resolve when we played the last frame; otherwise, reject\n\t\t\t\tif ( gotoEnd ) {\n\t\t\t\t\tdeferred.notifyWith( elem, [ animation, 1, 0 ] );\n\t\t\t\t\tdeferred.resolveWith( elem, [ animation, gotoEnd ] );\n\t\t\t\t} else {\n\t\t\t\t\tdeferred.rejectWith( elem, [ animation, gotoEnd ] );\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t}\n\t\t} ),\n\t\tprops = animation.props;\n\n\tpropFilter( props, animation.opts.specialEasing );\n\n\tfor ( ; index < length; index++ ) {\n\t\tresult = Animation.prefilters[ index ].call( animation, elem, props, animation.opts );\n\t\tif ( result ) {\n\t\t\tif ( isFunction( result.stop ) ) {\n\t\t\t\tjQuery._queueHooks( animation.elem, animation.opts.queue ).stop =\n\t\t\t\t\tresult.stop.bind( result );\n\t\t\t}\n\t\t\treturn result;\n\t\t}\n\t}\n\n\tjQuery.map( props, createTween, animation );\n\n\tif ( isFunction( animation.opts.start ) ) {\n\t\tanimation.opts.start.call( elem, animation );\n\t}\n\n\t// Attach callbacks from options\n\tanimation\n\t\t.progress( animation.opts.progress )\n\t\t.done( animation.opts.done, animation.opts.complete )\n\t\t.fail( animation.opts.fail )\n\t\t.always( animation.opts.always );\n\n\tjQuery.fx.timer(\n\t\tjQuery.extend( tick, {\n\t\t\telem: elem,\n\t\t\tanim: animation,\n\t\t\tqueue: animation.opts.queue\n\t\t} )\n\t);\n\n\treturn animation;\n}\n\njQuery.Animation = jQuery.extend( Animation, {\n\n\ttweeners: {\n\t\t\"*\": [ function( prop, value ) {\n\t\t\tvar tween = this.createTween( prop, value );\n\t\t\tadjustCSS( tween.elem, prop, rcssNum.exec( value ), tween );\n\t\t\treturn tween;\n\t\t} ]\n\t},\n\n\ttweener: function( props, callback ) {\n\t\tif ( isFunction( props ) ) {\n\t\t\tcallback = props;\n\t\t\tprops = [ \"*\" ];\n\t\t} else {\n\t\t\tprops = props.match( rnothtmlwhite );\n\t\t}\n\n\t\tvar prop,\n\t\t\tindex = 0,\n\t\t\tlength = props.length;\n\n\t\tfor ( ; index < length; index++ ) {\n\t\t\tprop = props[ index ];\n\t\t\tAnimation.tweeners[ prop ] = Animation.tweeners[ prop ] || [];\n\t\t\tAnimation.tweeners[ prop ].unshift( callback );\n\t\t}\n\t},\n\n\tprefilters: [ defaultPrefilter ],\n\n\tprefilter: function( callback, prepend ) {\n\t\tif ( prepend ) {\n\t\t\tAnimation.prefilters.unshift( callback );\n\t\t} else {\n\t\t\tAnimation.prefilters.push( callback );\n\t\t}\n\t}\n} );\n\njQuery.speed = function( speed, easing, fn ) {\n\tvar opt = speed && typeof speed === \"object\" ? jQuery.extend( {}, speed ) : {\n\t\tcomplete: fn || !fn && easing ||\n\t\t\tisFunction( speed ) && speed,\n\t\tduration: speed,\n\t\teasing: fn && easing || easing && !isFunction( easing ) && easing\n\t};\n\n\t// Go to the end state if fx are off\n\tif ( jQuery.fx.off ) {\n\t\topt.duration = 0;\n\n\t} else {\n\t\tif ( typeof opt.duration !== \"number\" ) {\n\t\t\tif ( opt.duration in jQuery.fx.speeds ) {\n\t\t\t\topt.duration = jQuery.fx.speeds[ opt.duration ];\n\n\t\t\t} else {\n\t\t\t\topt.duration = jQuery.fx.speeds._default;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Normalize opt.queue - true/undefined/null -> \"fx\"\n\tif ( opt.queue == null || opt.queue === true ) {\n\t\topt.queue = \"fx\";\n\t}\n\n\t// Queueing\n\topt.old = opt.complete;\n\n\topt.complete = function() {\n\t\tif ( isFunction( opt.old ) ) {\n\t\t\topt.old.call( this );\n\t\t}\n\n\t\tif ( opt.queue ) {\n\t\t\tjQuery.dequeue( this, opt.queue );\n\t\t}\n\t};\n\n\treturn opt;\n};\n\njQuery.fn.extend( {\n\tfadeTo: function( speed, to, easing, callback ) {\n\n\t\t// Show any hidden elements after setting opacity to 0\n\t\treturn this.filter( isHiddenWithinTree ).css( \"opacity\", 0 ).show()\n\n\t\t\t// Animate to the value specified\n\t\t\t.end().animate( { opacity: to }, speed, easing, callback );\n\t},\n\tanimate: function( prop, speed, easing, callback ) {\n\t\tvar empty = jQuery.isEmptyObject( prop ),\n\t\t\toptall = jQuery.speed( speed, easing, callback ),\n\t\t\tdoAnimation = function() {\n\n\t\t\t\t// Operate on a copy of prop so per-property easing won't be lost\n\t\t\t\tvar anim = Animation( this, jQuery.extend( {}, prop ), optall );\n\n\t\t\t\t// Empty animations, or finishing resolves immediately\n\t\t\t\tif ( empty || dataPriv.get( this, \"finish\" ) ) {\n\t\t\t\t\tanim.stop( true );\n\t\t\t\t}\n\t\t\t};\n\n\t\tdoAnimation.finish = doAnimation;\n\n\t\treturn empty || optall.queue === false ?\n\t\t\tthis.each( doAnimation ) :\n\t\t\tthis.queue( optall.queue, doAnimation );\n\t},\n\tstop: function( type, clearQueue, gotoEnd ) {\n\t\tvar stopQueue = function( hooks ) {\n\t\t\tvar stop = hooks.stop;\n\t\t\tdelete hooks.stop;\n\t\t\tstop( gotoEnd );\n\t\t};\n\n\t\tif ( typeof type !== \"string\" ) {\n\t\t\tgotoEnd = clearQueue;\n\t\t\tclearQueue = type;\n\t\t\ttype = undefined;\n\t\t}\n\t\tif ( clearQueue ) {\n\t\t\tthis.queue( type || \"fx\", [] );\n\t\t}\n\n\t\treturn this.each( function() {\n\t\t\tvar dequeue = true,\n\t\t\t\tindex = type != null && type + \"queueHooks\",\n\t\t\t\ttimers = jQuery.timers,\n\t\t\t\tdata = dataPriv.get( this );\n\n\t\t\tif ( index ) {\n\t\t\t\tif ( data[ index ] && data[ index ].stop ) {\n\t\t\t\t\tstopQueue( data[ index ] );\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tfor ( index in data ) {\n\t\t\t\t\tif ( data[ index ] && data[ index ].stop && rrun.test( index ) ) {\n\t\t\t\t\t\tstopQueue( data[ index ] );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfor ( index = timers.length; index--; ) {\n\t\t\t\tif ( timers[ index ].elem === this &&\n\t\t\t\t\t( type == null || timers[ index ].queue === type ) ) {\n\n\t\t\t\t\ttimers[ index ].anim.stop( gotoEnd );\n\t\t\t\t\tdequeue = false;\n\t\t\t\t\ttimers.splice( index, 1 );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Start the next in the queue if the last step wasn't forced.\n\t\t\t// Timers currently will call their complete callbacks, which\n\t\t\t// will dequeue but only if they were gotoEnd.\n\t\t\tif ( dequeue || !gotoEnd ) {\n\t\t\t\tjQuery.dequeue( this, type );\n\t\t\t}\n\t\t} );\n\t},\n\tfinish: function( type ) {\n\t\tif ( type !== false ) {\n\t\t\ttype = type || \"fx\";\n\t\t}\n\t\treturn this.each( function() {\n\t\t\tvar index,\n\t\t\t\tdata = dataPriv.get( this ),\n\t\t\t\tqueue = data[ type + \"queue\" ],\n\t\t\t\thooks = data[ type + \"queueHooks\" ],\n\t\t\t\ttimers = jQuery.timers,\n\t\t\t\tlength = queue ? queue.length : 0;\n\n\t\t\t// Enable finishing flag on private data\n\t\t\tdata.finish = true;\n\n\t\t\t// Empty the queue first\n\t\t\tjQuery.queue( this, type, [] );\n\n\t\t\tif ( hooks && hooks.stop ) {\n\t\t\t\thooks.stop.call( this, true );\n\t\t\t}\n\n\t\t\t// Look for any active animations, and finish them\n\t\t\tfor ( index = timers.length; index--; ) {\n\t\t\t\tif ( timers[ index ].elem === this && timers[ index ].queue === type ) {\n\t\t\t\t\ttimers[ index ].anim.stop( true );\n\t\t\t\t\ttimers.splice( index, 1 );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Look for any animations in the old queue and finish them\n\t\t\tfor ( index = 0; index < length; index++ ) {\n\t\t\t\tif ( queue[ index ] && queue[ index ].finish ) {\n\t\t\t\t\tqueue[ index ].finish.call( this );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Turn off finishing flag\n\t\t\tdelete data.finish;\n\t\t} );\n\t}\n} );\n\njQuery.each( [ \"toggle\", \"show\", \"hide\" ], function( _i, name ) {\n\tvar cssFn = jQuery.fn[ name ];\n\tjQuery.fn[ name ] = function( speed, easing, callback ) {\n\t\treturn speed == null || typeof speed === \"boolean\" ?\n\t\t\tcssFn.apply( this, arguments ) :\n\t\t\tthis.animate( genFx( name, true ), speed, easing, callback );\n\t};\n} );\n\n// Generate shortcuts for custom animations\njQuery.each( {\n\tslideDown: genFx( \"show\" ),\n\tslideUp: genFx( \"hide\" ),\n\tslideToggle: genFx( \"toggle\" ),\n\tfadeIn: { opacity: \"show\" },\n\tfadeOut: { opacity: \"hide\" },\n\tfadeToggle: { opacity: \"toggle\" }\n}, function( name, props ) {\n\tjQuery.fn[ name ] = function( speed, easing, callback ) {\n\t\treturn this.animate( props, speed, easing, callback );\n\t};\n} );\n\njQuery.timers = [];\njQuery.fx.tick = function() {\n\tvar timer,\n\t\ti = 0,\n\t\ttimers = jQuery.timers;\n\n\tfxNow = Date.now();\n\n\tfor ( ; i < timers.length; i++ ) {\n\t\ttimer = timers[ i ];\n\n\t\t// Run the timer and safely remove it when done (allowing for external removal)\n\t\tif ( !timer() && timers[ i ] === timer ) {\n\t\t\ttimers.splice( i--, 1 );\n\t\t}\n\t}\n\n\tif ( !timers.length ) {\n\t\tjQuery.fx.stop();\n\t}\n\tfxNow = undefined;\n};\n\njQuery.fx.timer = function( timer ) {\n\tjQuery.timers.push( timer );\n\tjQuery.fx.start();\n};\n\njQuery.fx.interval = 13;\njQuery.fx.start = function() {\n\tif ( inProgress ) {\n\t\treturn;\n\t}\n\n\tinProgress = true;\n\tschedule();\n};\n\njQuery.fx.stop = function() {\n\tinProgress = null;\n};\n\njQuery.fx.speeds = {\n\tslow: 600,\n\tfast: 200,\n\n\t// Default speed\n\t_default: 400\n};\n\n\n// Based off of the plugin by Clint Helfers, with permission.\njQuery.fn.delay = function( time, type ) {\n\ttime = jQuery.fx ? jQuery.fx.speeds[ time ] || time : time;\n\ttype = type || \"fx\";\n\n\treturn this.queue( type, function( next, hooks ) {\n\t\tvar timeout = window.setTimeout( next, time );\n\t\thooks.stop = function() {\n\t\t\twindow.clearTimeout( timeout );\n\t\t};\n\t} );\n};\n\n\n( function() {\n\tvar input = document.createElement( \"input\" ),\n\t\tselect = document.createElement( \"select\" ),\n\t\topt = select.appendChild( document.createElement( \"option\" ) );\n\n\tinput.type = \"checkbox\";\n\n\t// Support: Android <=4.3 only\n\t// Default value for a checkbox should be \"on\"\n\tsupport.checkOn = input.value !== \"\";\n\n\t// Support: IE <=11 only\n\t// Must access selectedIndex to make default options select\n\tsupport.optSelected = opt.selected;\n\n\t// Support: IE <=11 only\n\t// An input loses its value after becoming a radio\n\tinput = document.createElement( \"input\" );\n\tinput.value = \"t\";\n\tinput.type = \"radio\";\n\tsupport.radioValue = input.value === \"t\";\n} )();\n\n\nvar boolHook,\n\tattrHandle = jQuery.expr.attrHandle;\n\njQuery.fn.extend( {\n\tattr: function( name, value ) {\n\t\treturn access( this, jQuery.attr, name, value, arguments.length > 1 );\n\t},\n\n\tremoveAttr: function( name ) {\n\t\treturn this.each( function() {\n\t\t\tjQuery.removeAttr( this, name );\n\t\t} );\n\t}\n} );\n\njQuery.extend( {\n\tattr: function( elem, name, value ) {\n\t\tvar ret, hooks,\n\t\t\tnType = elem.nodeType;\n\n\t\t// Don't get/set attributes on text, comment and attribute nodes\n\t\tif ( nType === 3 || nType === 8 || nType === 2 ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Fallback to prop when attributes are not supported\n\t\tif ( typeof elem.getAttribute === \"undefined\" ) {\n\t\t\treturn jQuery.prop( elem, name, value );\n\t\t}\n\n\t\t// Attribute hooks are determined by the lowercase version\n\t\t// Grab necessary hook if one is defined\n\t\tif ( nType !== 1 || !jQuery.isXMLDoc( elem ) ) {\n\t\t\thooks = jQuery.attrHooks[ name.toLowerCase() ] ||\n\t\t\t\t( jQuery.expr.match.bool.test( name ) ? boolHook : undefined );\n\t\t}\n\n\t\tif ( value !== undefined ) {\n\t\t\tif ( value === null ) {\n\t\t\t\tjQuery.removeAttr( elem, name );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ( hooks && \"set\" in hooks &&\n\t\t\t\t( ret = hooks.set( elem, value, name ) ) !== undefined ) {\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\telem.setAttribute( name, value + \"\" );\n\t\t\treturn value;\n\t\t}\n\n\t\tif ( hooks && \"get\" in hooks && ( ret = hooks.get( elem, name ) ) !== null ) {\n\t\t\treturn ret;\n\t\t}\n\n\t\tret = jQuery.find.attr( elem, name );\n\n\t\t// Non-existent attributes return null, we normalize to undefined\n\t\treturn ret == null ? undefined : ret;\n\t},\n\n\tattrHooks: {\n\t\ttype: {\n\t\t\tset: function( elem, value ) {\n\t\t\t\tif ( !support.radioValue && value === \"radio\" &&\n\t\t\t\t\tnodeName( elem, \"input\" ) ) {\n\t\t\t\t\tvar val = elem.value;\n\t\t\t\t\telem.setAttribute( \"type\", value );\n\t\t\t\t\tif ( val ) {\n\t\t\t\t\t\telem.value = val;\n\t\t\t\t\t}\n\t\t\t\t\treturn value;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\n\tremoveAttr: function( elem, value ) {\n\t\tvar name,\n\t\t\ti = 0,\n\n\t\t\t// Attribute names can contain non-HTML whitespace characters\n\t\t\t// https://html.spec.whatwg.org/multipage/syntax.html#attributes-2\n\t\t\tattrNames = value && value.match( rnothtmlwhite );\n\n\t\tif ( attrNames && elem.nodeType === 1 ) {\n\t\t\twhile ( ( name = attrNames[ i++ ] ) ) {\n\t\t\t\telem.removeAttribute( name );\n\t\t\t}\n\t\t}\n\t}\n} );\n\n// Hooks for boolean attributes\nboolHook = {\n\tset: function( elem, value, name ) {\n\t\tif ( value === false ) {\n\n\t\t\t// Remove boolean attributes when set to false\n\t\t\tjQuery.removeAttr( elem, name );\n\t\t} else {\n\t\t\telem.setAttribute( name, name );\n\t\t}\n\t\treturn name;\n\t}\n};\n\njQuery.each( jQuery.expr.match.bool.source.match( /\\w+/g ), function( _i, name ) {\n\tvar getter = attrHandle[ name ] || jQuery.find.attr;\n\n\tattrHandle[ name ] = function( elem, name, isXML ) {\n\t\tvar ret, handle,\n\t\t\tlowercaseName = name.toLowerCase();\n\n\t\tif ( !isXML ) {\n\n\t\t\t// Avoid an infinite loop by temporarily removing this function from the getter\n\t\t\thandle = attrHandle[ lowercaseName ];\n\t\t\tattrHandle[ lowercaseName ] = ret;\n\t\t\tret = getter( elem, name, isXML ) != null ?\n\t\t\t\tlowercaseName :\n\t\t\t\tnull;\n\t\t\tattrHandle[ lowercaseName ] = handle;\n\t\t}\n\t\treturn ret;\n\t};\n} );\n\n\n\n\nvar rfocusable = /^(?:input|select|textarea|button)$/i,\n\trclickable = /^(?:a|area)$/i;\n\njQuery.fn.extend( {\n\tprop: function( name, value ) {\n\t\treturn access( this, jQuery.prop, name, value, arguments.length > 1 );\n\t},\n\n\tremoveProp: function( name ) {\n\t\treturn this.each( function() {\n\t\t\tdelete this[ jQuery.propFix[ name ] || name ];\n\t\t} );\n\t}\n} );\n\njQuery.extend( {\n\tprop: function( elem, name, value ) {\n\t\tvar ret, hooks,\n\t\t\tnType = elem.nodeType;\n\n\t\t// Don't get/set properties on text, comment and attribute nodes\n\t\tif ( nType === 3 || nType === 8 || nType === 2 ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( nType !== 1 || !jQuery.isXMLDoc( elem ) ) {\n\n\t\t\t// Fix name and attach hooks\n\t\t\tname = jQuery.propFix[ name ] || name;\n\t\t\thooks = jQuery.propHooks[ name ];\n\t\t}\n\n\t\tif ( value !== undefined ) {\n\t\t\tif ( hooks && \"set\" in hooks &&\n\t\t\t\t( ret = hooks.set( elem, value, name ) ) !== undefined ) {\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\treturn ( elem[ name ] = value );\n\t\t}\n\n\t\tif ( hooks && \"get\" in hooks && ( ret = hooks.get( elem, name ) ) !== null ) {\n\t\t\treturn ret;\n\t\t}\n\n\t\treturn elem[ name ];\n\t},\n\n\tpropHooks: {\n\t\ttabIndex: {\n\t\t\tget: function( elem ) {\n\n\t\t\t\t// Support: IE <=9 - 11 only\n\t\t\t\t// elem.tabIndex doesn't always return the\n\t\t\t\t// correct value when it hasn't been explicitly set\n\t\t\t\t// Use proper attribute retrieval (trac-12072)\n\t\t\t\tvar tabindex = jQuery.find.attr( elem, \"tabindex\" );\n\n\t\t\t\tif ( tabindex ) {\n\t\t\t\t\treturn parseInt( tabindex, 10 );\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\trfocusable.test( elem.nodeName ) ||\n\t\t\t\t\trclickable.test( elem.nodeName ) &&\n\t\t\t\t\telem.href\n\t\t\t\t) {\n\t\t\t\t\treturn 0;\n\t\t\t\t}\n\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t}\n\t},\n\n\tpropFix: {\n\t\t\"for\": \"htmlFor\",\n\t\t\"class\": \"className\"\n\t}\n} );\n\n// Support: IE <=11 only\n// Accessing the selectedIndex property\n// forces the browser to respect setting selected\n// on the option\n// The getter ensures a default option is selected\n// when in an optgroup\n// eslint rule \"no-unused-expressions\" is disabled for this code\n// since it considers such accessions noop\nif ( !support.optSelected ) {\n\tjQuery.propHooks.selected = {\n\t\tget: function( elem ) {\n\n\t\t\t/* eslint no-unused-expressions: \"off\" */\n\n\t\t\tvar parent = elem.parentNode;\n\t\t\tif ( parent && parent.parentNode ) {\n\t\t\t\tparent.parentNode.selectedIndex;\n\t\t\t}\n\t\t\treturn null;\n\t\t},\n\t\tset: function( elem ) {\n\n\t\t\t/* eslint no-unused-expressions: \"off\" */\n\n\t\t\tvar parent = elem.parentNode;\n\t\t\tif ( parent ) {\n\t\t\t\tparent.selectedIndex;\n\n\t\t\t\tif ( parent.parentNode ) {\n\t\t\t\t\tparent.parentNode.selectedIndex;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n}\n\njQuery.each( [\n\t\"tabIndex\",\n\t\"readOnly\",\n\t\"maxLength\",\n\t\"cellSpacing\",\n\t\"cellPadding\",\n\t\"rowSpan\",\n\t\"colSpan\",\n\t\"useMap\",\n\t\"frameBorder\",\n\t\"contentEditable\"\n], function() {\n\tjQuery.propFix[ this.toLowerCase() ] = this;\n} );\n\n\n\n\n\t// Strip and collapse whitespace according to HTML spec\n\t// https://infra.spec.whatwg.org/#strip-and-collapse-ascii-whitespace\n\tfunction stripAndCollapse( value ) {\n\t\tvar tokens = value.match( rnothtmlwhite ) || [];\n\t\treturn tokens.join( \" \" );\n\t}\n\n\nfunction getClass( elem ) {\n\treturn elem.getAttribute && elem.getAttribute( \"class\" ) || \"\";\n}\n\nfunction classesToArray( value ) {\n\tif ( Array.isArray( value ) ) {\n\t\treturn value;\n\t}\n\tif ( typeof value === \"string\" ) {\n\t\treturn value.match( rnothtmlwhite ) || [];\n\t}\n\treturn [];\n}\n\njQuery.fn.extend( {\n\taddClass: function( value ) {\n\t\tvar classNames, cur, curValue, className, i, finalValue;\n\n\t\tif ( isFunction( value ) ) {\n\t\t\treturn this.each( function( j ) {\n\t\t\t\tjQuery( this ).addClass( value.call( this, j, getClass( this ) ) );\n\t\t\t} );\n\t\t}\n\n\t\tclassNames = classesToArray( value );\n\n\t\tif ( classNames.length ) {\n\t\t\treturn this.each( function() {\n\t\t\t\tcurValue = getClass( this );\n\t\t\t\tcur = this.nodeType === 1 && ( \" \" + stripAndCollapse( curValue ) + \" \" );\n\n\t\t\t\tif ( cur ) {\n\t\t\t\t\tfor ( i = 0; i < classNames.length; i++ ) {\n\t\t\t\t\t\tclassName = classNames[ i ];\n\t\t\t\t\t\tif ( cur.indexOf( \" \" + className + \" \" ) < 0 ) {\n\t\t\t\t\t\t\tcur += className + \" \";\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// Only assign if different to avoid unneeded rendering.\n\t\t\t\t\tfinalValue = stripAndCollapse( cur );\n\t\t\t\t\tif ( curValue !== finalValue ) {\n\t\t\t\t\t\tthis.setAttribute( \"class\", finalValue );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} );\n\t\t}\n\n\t\treturn this;\n\t},\n\n\tremoveClass: function( value ) {\n\t\tvar classNames, cur, curValue, className, i, finalValue;\n\n\t\tif ( isFunction( value ) ) {\n\t\t\treturn this.each( function( j ) {\n\t\t\t\tjQuery( this ).removeClass( value.call( this, j, getClass( this ) ) );\n\t\t\t} );\n\t\t}\n\n\t\tif ( !arguments.length ) {\n\t\t\treturn this.attr( \"class\", \"\" );\n\t\t}\n\n\t\tclassNames = classesToArray( value );\n\n\t\tif ( classNames.length ) {\n\t\t\treturn this.each( function() {\n\t\t\t\tcurValue = getClass( this );\n\n\t\t\t\t// This expression is here for better compressibility (see addClass)\n\t\t\t\tcur = this.nodeType === 1 && ( \" \" + stripAndCollapse( curValue ) + \" \" );\n\n\t\t\t\tif ( cur ) {\n\t\t\t\t\tfor ( i = 0; i < classNames.length; i++ ) {\n\t\t\t\t\t\tclassName = classNames[ i ];\n\n\t\t\t\t\t\t// Remove *all* instances\n\t\t\t\t\t\twhile ( cur.indexOf( \" \" + className + \" \" ) > -1 ) {\n\t\t\t\t\t\t\tcur = cur.replace( \" \" + className + \" \", \" \" );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// Only assign if different to avoid unneeded rendering.\n\t\t\t\t\tfinalValue = stripAndCollapse( cur );\n\t\t\t\t\tif ( curValue !== finalValue ) {\n\t\t\t\t\t\tthis.setAttribute( \"class\", finalValue );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} );\n\t\t}\n\n\t\treturn this;\n\t},\n\n\ttoggleClass: function( value, stateVal ) {\n\t\tvar classNames, className, i, self,\n\t\t\ttype = typeof value,\n\t\t\tisValidValue = type === \"string\" || Array.isArray( value );\n\n\t\tif ( isFunction( value ) ) {\n\t\t\treturn this.each( function( i ) {\n\t\t\t\tjQuery( this ).toggleClass(\n\t\t\t\t\tvalue.call( this, i, getClass( this ), stateVal ),\n\t\t\t\t\tstateVal\n\t\t\t\t);\n\t\t\t} );\n\t\t}\n\n\t\tif ( typeof stateVal === \"boolean\" && isValidValue ) {\n\t\t\treturn stateVal ? this.addClass( value ) : this.removeClass( value );\n\t\t}\n\n\t\tclassNames = classesToArray( value );\n\n\t\treturn this.each( function() {\n\t\t\tif ( isValidValue ) {\n\n\t\t\t\t// Toggle individual class names\n\t\t\t\tself = jQuery( this );\n\n\t\t\t\tfor ( i = 0; i < classNames.length; i++ ) {\n\t\t\t\t\tclassName = classNames[ i ];\n\n\t\t\t\t\t// Check each className given, space separated list\n\t\t\t\t\tif ( self.hasClass( className ) ) {\n\t\t\t\t\t\tself.removeClass( className );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.addClass( className );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t// Toggle whole class name\n\t\t\t} else if ( value === undefined || type === \"boolean\" ) {\n\t\t\t\tclassName = getClass( this );\n\t\t\t\tif ( className ) {\n\n\t\t\t\t\t// Store className if set\n\t\t\t\t\tdataPriv.set( this, \"__className__\", className );\n\t\t\t\t}\n\n\t\t\t\t// If the element has a class name or if we're passed `false`,\n\t\t\t\t// then remove the whole classname (if there was one, the above saved it).\n\t\t\t\t// Otherwise bring back whatever was previously saved (if anything),\n\t\t\t\t// falling back to the empty string if nothing was stored.\n\t\t\t\tif ( this.setAttribute ) {\n\t\t\t\t\tthis.setAttribute( \"class\",\n\t\t\t\t\t\tclassName || value === false ?\n\t\t\t\t\t\t\t\"\" :\n\t\t\t\t\t\t\tdataPriv.get( this, \"__className__\" ) || \"\"\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t} );\n\t},\n\n\thasClass: function( selector ) {\n\t\tvar className, elem,\n\t\t\ti = 0;\n\n\t\tclassName = \" \" + selector + \" \";\n\t\twhile ( ( elem = this[ i++ ] ) ) {\n\t\t\tif ( elem.nodeType === 1 &&\n\t\t\t\t( \" \" + stripAndCollapse( getClass( elem ) ) + \" \" ).indexOf( className ) > -1 ) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n} );\n\n\n\n\nvar rreturn = /\\r/g;\n\njQuery.fn.extend( {\n\tval: function( value ) {\n\t\tvar hooks, ret, valueIsFunction,\n\t\t\telem = this[ 0 ];\n\n\t\tif ( !arguments.length ) {\n\t\t\tif ( elem ) {\n\t\t\t\thooks = jQuery.valHooks[ elem.type ] ||\n\t\t\t\t\tjQuery.valHooks[ elem.nodeName.toLowerCase() ];\n\n\t\t\t\tif ( hooks &&\n\t\t\t\t\t\"get\" in hooks &&\n\t\t\t\t\t( ret = hooks.get( elem, \"value\" ) ) !== undefined\n\t\t\t\t) {\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\n\t\t\t\tret = elem.value;\n\n\t\t\t\t// Handle most common string cases\n\t\t\t\tif ( typeof ret === \"string\" ) {\n\t\t\t\t\treturn ret.replace( rreturn, \"\" );\n\t\t\t\t}\n\n\t\t\t\t// Handle cases where value is null/undef or number\n\t\t\t\treturn ret == null ? \"\" : ret;\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\tvalueIsFunction = isFunction( value );\n\n\t\treturn this.each( function( i ) {\n\t\t\tvar val;\n\n\t\t\tif ( this.nodeType !== 1 ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ( valueIsFunction ) {\n\t\t\t\tval = value.call( this, i, jQuery( this ).val() );\n\t\t\t} else {\n\t\t\t\tval = value;\n\t\t\t}\n\n\t\t\t// Treat null/undefined as \"\"; convert numbers to string\n\t\t\tif ( val == null ) {\n\t\t\t\tval = \"\";\n\n\t\t\t} else if ( typeof val === \"number\" ) {\n\t\t\t\tval += \"\";\n\n\t\t\t} else if ( Array.isArray( val ) ) {\n\t\t\t\tval = jQuery.map( val, function( value ) {\n\t\t\t\t\treturn value == null ? \"\" : value + \"\";\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\thooks = jQuery.valHooks[ this.type ] || jQuery.valHooks[ this.nodeName.toLowerCase() ];\n\n\t\t\t// If set returns undefined, fall back to normal setting\n\t\t\tif ( !hooks || !( \"set\" in hooks ) || hooks.set( this, val, \"value\" ) === undefined ) {\n\t\t\t\tthis.value = val;\n\t\t\t}\n\t\t} );\n\t}\n} );\n\njQuery.extend( {\n\tvalHooks: {\n\t\toption: {\n\t\t\tget: function( elem ) {\n\n\t\t\t\tvar val = jQuery.find.attr( elem, \"value\" );\n\t\t\t\treturn val != null ?\n\t\t\t\t\tval :\n\n\t\t\t\t\t// Support: IE <=10 - 11 only\n\t\t\t\t\t// option.text throws exceptions (trac-14686, trac-14858)\n\t\t\t\t\t// Strip and collapse whitespace\n\t\t\t\t\t// https://html.spec.whatwg.org/#strip-and-collapse-whitespace\n\t\t\t\t\tstripAndCollapse( jQuery.text( elem ) );\n\t\t\t}\n\t\t},\n\t\tselect: {\n\t\t\tget: function( elem ) {\n\t\t\t\tvar value, option, i,\n\t\t\t\t\toptions = elem.options,\n\t\t\t\t\tindex = elem.selectedIndex,\n\t\t\t\t\tone = elem.type === \"select-one\",\n\t\t\t\t\tvalues = one ? null : [],\n\t\t\t\t\tmax = one ? index + 1 : options.length;\n\n\t\t\t\tif ( index < 0 ) {\n\t\t\t\t\ti = max;\n\n\t\t\t\t} else {\n\t\t\t\t\ti = one ? index : 0;\n\t\t\t\t}\n\n\t\t\t\t// Loop through all the selected options\n\t\t\t\tfor ( ; i < max; i++ ) {\n\t\t\t\t\toption = options[ i ];\n\n\t\t\t\t\t// Support: IE <=9 only\n\t\t\t\t\t// IE8-9 doesn't update selected after form reset (trac-2551)\n\t\t\t\t\tif ( ( option.selected || i === index ) &&\n\n\t\t\t\t\t\t\t// Don't return options that are disabled or in a disabled optgroup\n\t\t\t\t\t\t\t!option.disabled &&\n\t\t\t\t\t\t\t( !option.parentNode.disabled ||\n\t\t\t\t\t\t\t\t!nodeName( option.parentNode, \"optgroup\" ) ) ) {\n\n\t\t\t\t\t\t// Get the specific value for the option\n\t\t\t\t\t\tvalue = jQuery( option ).val();\n\n\t\t\t\t\t\t// We don't need an array for one selects\n\t\t\t\t\t\tif ( one ) {\n\t\t\t\t\t\t\treturn value;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Multi-Selects return an array\n\t\t\t\t\t\tvalues.push( value );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn values;\n\t\t\t},\n\n\t\t\tset: function( elem, value ) {\n\t\t\t\tvar optionSet, option,\n\t\t\t\t\toptions = elem.options,\n\t\t\t\t\tvalues = jQuery.makeArray( value ),\n\t\t\t\t\ti = options.length;\n\n\t\t\t\twhile ( i-- ) {\n\t\t\t\t\toption = options[ i ];\n\n\t\t\t\t\t/* eslint-disable no-cond-assign */\n\n\t\t\t\t\tif ( option.selected =\n\t\t\t\t\t\tjQuery.inArray( jQuery.valHooks.option.get( option ), values ) > -1\n\t\t\t\t\t) {\n\t\t\t\t\t\toptionSet = true;\n\t\t\t\t\t}\n\n\t\t\t\t\t/* eslint-enable no-cond-assign */\n\t\t\t\t}\n\n\t\t\t\t// Force browsers to behave consistently when non-matching value is set\n\t\t\t\tif ( !optionSet ) {\n\t\t\t\t\telem.selectedIndex = -1;\n\t\t\t\t}\n\t\t\t\treturn values;\n\t\t\t}\n\t\t}\n\t}\n} );\n\n// Radios and checkboxes getter/setter\njQuery.each( [ \"radio\", \"checkbox\" ], function() {\n\tjQuery.valHooks[ this ] = {\n\t\tset: function( elem, value ) {\n\t\t\tif ( Array.isArray( value ) ) {\n\t\t\t\treturn ( elem.checked = jQuery.inArray( jQuery( elem ).val(), value ) > -1 );\n\t\t\t}\n\t\t}\n\t};\n\tif ( !support.checkOn ) {\n\t\tjQuery.valHooks[ this ].get = function( elem ) {\n\t\t\treturn elem.getAttribute( \"value\" ) === null ? \"on\" : elem.value;\n\t\t};\n\t}\n} );\n\n\n\n\n// Return jQuery for attributes-only inclusion\nvar location = window.location;\n\nvar nonce = { guid: Date.now() };\n\nvar rquery = ( /\\?/ );\n\n\n\n// Cross-browser xml parsing\njQuery.parseXML = function( data ) {\n\tvar xml, parserErrorElem;\n\tif ( !data || typeof data !== \"string\" ) {\n\t\treturn null;\n\t}\n\n\t// Support: IE 9 - 11 only\n\t// IE throws on parseFromString with invalid input.\n\ttry {\n\t\txml = ( new window.DOMParser() ).parseFromString( data, \"text/xml\" );\n\t} catch ( e ) {}\n\n\tparserErrorElem = xml && xml.getElementsByTagName( \"parsererror\" )[ 0 ];\n\tif ( !xml || parserErrorElem ) {\n\t\tjQuery.error( \"Invalid XML: \" + (\n\t\t\tparserErrorElem ?\n\t\t\t\tjQuery.map( parserErrorElem.childNodes, function( el ) {\n\t\t\t\t\treturn el.textContent;\n\t\t\t\t} ).join( \"\\n\" ) :\n\t\t\t\tdata\n\t\t) );\n\t}\n\treturn xml;\n};\n\n\nvar rfocusMorph = /^(?:focusinfocus|focusoutblur)$/,\n\tstopPropagationCallback = function( e ) {\n\t\te.stopPropagation();\n\t};\n\njQuery.extend( jQuery.event, {\n\n\ttrigger: function( event, data, elem, onlyHandlers ) {\n\n\t\tvar i, cur, tmp, bubbleType, ontype, handle, special, lastElement,\n\t\t\teventPath = [ elem || document ],\n\t\t\ttype = hasOwn.call( event, \"type\" ) ? event.type : event,\n\t\t\tnamespaces = hasOwn.call( event, \"namespace\" ) ? event.namespace.split( \".\" ) : [];\n\n\t\tcur = lastElement = tmp = elem = elem || document;\n\n\t\t// Don't do events on text and comment nodes\n\t\tif ( elem.nodeType === 3 || elem.nodeType === 8 ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// focus/blur morphs to focusin/out; ensure we're not firing them right now\n\t\tif ( rfocusMorph.test( type + jQuery.event.triggered ) ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( type.indexOf( \".\" ) > -1 ) {\n\n\t\t\t// Namespaced trigger; create a regexp to match event type in handle()\n\t\t\tnamespaces = type.split( \".\" );\n\t\t\ttype = namespaces.shift();\n\t\t\tnamespaces.sort();\n\t\t}\n\t\tontype = type.indexOf( \":\" ) < 0 && \"on\" + type;\n\n\t\t// Caller can pass in a jQuery.Event object, Object, or just an event type string\n\t\tevent = event[ jQuery.expando ] ?\n\t\t\tevent :\n\t\t\tnew jQuery.Event( type, typeof event === \"object\" && event );\n\n\t\t// Trigger bitmask: & 1 for native handlers; & 2 for jQuery (always true)\n\t\tevent.isTrigger = onlyHandlers ? 2 : 3;\n\t\tevent.namespace = namespaces.join( \".\" );\n\t\tevent.rnamespace = event.namespace ?\n\t\t\tnew RegExp( \"(^|\\\\.)\" + namespaces.join( \"\\\\.(?:.*\\\\.|)\" ) + \"(\\\\.|$)\" ) :\n\t\t\tnull;\n\n\t\t// Clean up the event in case it is being reused\n\t\tevent.result = undefined;\n\t\tif ( !event.target ) {\n\t\t\tevent.target = elem;\n\t\t}\n\n\t\t// Clone any incoming data and prepend the event, creating the handler arg list\n\t\tdata = data == null ?\n\t\t\t[ event ] :\n\t\t\tjQuery.makeArray( data, [ event ] );\n\n\t\t// Allow special events to draw outside the lines\n\t\tspecial = jQuery.event.special[ type ] || {};\n\t\tif ( !onlyHandlers && special.trigger && special.trigger.apply( elem, data ) === false ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Determine event propagation path in advance, per W3C events spec (trac-9951)\n\t\t// Bubble up to document, then to window; watch for a global ownerDocument var (trac-9724)\n\t\tif ( !onlyHandlers && !special.noBubble && !isWindow( elem ) ) {\n\n\t\t\tbubbleType = special.delegateType || type;\n\t\t\tif ( !rfocusMorph.test( bubbleType + type ) ) {\n\t\t\t\tcur = cur.parentNode;\n\t\t\t}\n\t\t\tfor ( ; cur; cur = cur.parentNode ) {\n\t\t\t\teventPath.push( cur );\n\t\t\t\ttmp = cur;\n\t\t\t}\n\n\t\t\t// Only add window if we got to document (e.g., not plain obj or detached DOM)\n\t\t\tif ( tmp === ( elem.ownerDocument || document ) ) {\n\t\t\t\teventPath.push( tmp.defaultView || tmp.parentWindow || window );\n\t\t\t}\n\t\t}\n\n\t\t// Fire handlers on the event path\n\t\ti = 0;\n\t\twhile ( ( cur = eventPath[ i++ ] ) && !event.isPropagationStopped() ) {\n\t\t\tlastElement = cur;\n\t\t\tevent.type = i > 1 ?\n\t\t\t\tbubbleType :\n\t\t\t\tspecial.bindType || type;\n\n\t\t\t// jQuery handler\n\t\t\thandle = ( dataPriv.get( cur, \"events\" ) || Object.create( null ) )[ event.type ] &&\n\t\t\t\tdataPriv.get( cur, \"handle\" );\n\t\t\tif ( handle ) {\n\t\t\t\thandle.apply( cur, data );\n\t\t\t}\n\n\t\t\t// Native handler\n\t\t\thandle = ontype && cur[ ontype ];\n\t\t\tif ( handle && handle.apply && acceptData( cur ) ) {\n\t\t\t\tevent.result = handle.apply( cur, data );\n\t\t\t\tif ( event.result === false ) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tevent.type = type;\n\n\t\t// If nobody prevented the default action, do it now\n\t\tif ( !onlyHandlers && !event.isDefaultPrevented() ) {\n\n\t\t\tif ( ( !special._default ||\n\t\t\t\tspecial._default.apply( eventPath.pop(), data ) === false ) &&\n\t\t\t\tacceptData( elem ) ) {\n\n\t\t\t\t// Call a native DOM method on the target with the same name as the event.\n\t\t\t\t// Don't do default actions on window, that's where global variables be (trac-6170)\n\t\t\t\tif ( ontype && isFunction( elem[ type ] ) && !isWindow( elem ) ) {\n\n\t\t\t\t\t// Don't re-trigger an onFOO event when we call its FOO() method\n\t\t\t\t\ttmp = elem[ ontype ];\n\n\t\t\t\t\tif ( tmp ) {\n\t\t\t\t\t\telem[ ontype ] = null;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Prevent re-triggering of the same event, since we already bubbled it above\n\t\t\t\t\tjQuery.event.triggered = type;\n\n\t\t\t\t\tif ( event.isPropagationStopped() ) {\n\t\t\t\t\t\tlastElement.addEventListener( type, stopPropagationCallback );\n\t\t\t\t\t}\n\n\t\t\t\t\telem[ type ]();\n\n\t\t\t\t\tif ( event.isPropagationStopped() ) {\n\t\t\t\t\t\tlastElement.removeEventListener( type, stopPropagationCallback );\n\t\t\t\t\t}\n\n\t\t\t\t\tjQuery.event.triggered = undefined;\n\n\t\t\t\t\tif ( tmp ) {\n\t\t\t\t\t\telem[ ontype ] = tmp;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn event.result;\n\t},\n\n\t// Piggyback on a donor event to simulate a different one\n\t// Used only for `focus(in | out)` events\n\tsimulate: function( type, elem, event ) {\n\t\tvar e = jQuery.extend(\n\t\t\tnew jQuery.Event(),\n\t\t\tevent,\n\t\t\t{\n\t\t\t\ttype: type,\n\t\t\t\tisSimulated: true\n\t\t\t}\n\t\t);\n\n\t\tjQuery.event.trigger( e, null, elem );\n\t}\n\n} );\n\njQuery.fn.extend( {\n\n\ttrigger: function( type, data ) {\n\t\treturn this.each( function() {\n\t\t\tjQuery.event.trigger( type, data, this );\n\t\t} );\n\t},\n\ttriggerHandler: function( type, data ) {\n\t\tvar elem = this[ 0 ];\n\t\tif ( elem ) {\n\t\t\treturn jQuery.event.trigger( type, data, elem, true );\n\t\t}\n\t}\n} );\n\n\nvar\n\trbracket = /\\[\\]$/,\n\trCRLF = /\\r?\\n/g,\n\trsubmitterTypes = /^(?:submit|button|image|reset|file)$/i,\n\trsubmittable = /^(?:input|select|textarea|keygen)/i;\n\nfunction buildParams( prefix, obj, traditional, add ) {\n\tvar name;\n\n\tif ( Array.isArray( obj ) ) {\n\n\t\t// Serialize array item.\n\t\tjQuery.each( obj, function( i, v ) {\n\t\t\tif ( traditional || rbracket.test( prefix ) ) {\n\n\t\t\t\t// Treat each array item as a scalar.\n\t\t\t\tadd( prefix, v );\n\n\t\t\t} else {\n\n\t\t\t\t// Item is non-scalar (array or object), encode its numeric index.\n\t\t\t\tbuildParams(\n\t\t\t\t\tprefix + \"[\" + ( typeof v === \"object\" && v != null ? i : \"\" ) + \"]\",\n\t\t\t\t\tv,\n\t\t\t\t\ttraditional,\n\t\t\t\t\tadd\n\t\t\t\t);\n\t\t\t}\n\t\t} );\n\n\t} else if ( !traditional && toType( obj ) === \"object\" ) {\n\n\t\t// Serialize object item.\n\t\tfor ( name in obj ) {\n\t\t\tbuildParams( prefix + \"[\" + name + \"]\", obj[ name ], traditional, add );\n\t\t}\n\n\t} else {\n\n\t\t// Serialize scalar item.\n\t\tadd( prefix, obj );\n\t}\n}\n\n// Serialize an array of form elements or a set of\n// key/values into a query string\njQuery.param = function( a, traditional ) {\n\tvar prefix,\n\t\ts = [],\n\t\tadd = function( key, valueOrFunction ) {\n\n\t\t\t// If value is a function, invoke it and use its return value\n\t\t\tvar value = isFunction( valueOrFunction ) ?\n\t\t\t\tvalueOrFunction() :\n\t\t\t\tvalueOrFunction;\n\n\t\t\ts[ s.length ] = encodeURIComponent( key ) + \"=\" +\n\t\t\t\tencodeURIComponent( value == null ? \"\" : value );\n\t\t};\n\n\tif ( a == null ) {\n\t\treturn \"\";\n\t}\n\n\t// If an array was passed in, assume that it is an array of form elements.\n\tif ( Array.isArray( a ) || ( a.jquery && !jQuery.isPlainObject( a ) ) ) {\n\n\t\t// Serialize the form elements\n\t\tjQuery.each( a, function() {\n\t\t\tadd( this.name, this.value );\n\t\t} );\n\n\t} else {\n\n\t\t// If traditional, encode the \"old\" way (the way 1.3.2 or older\n\t\t// did it), otherwise encode params recursively.\n\t\tfor ( prefix in a ) {\n\t\t\tbuildParams( prefix, a[ prefix ], traditional, add );\n\t\t}\n\t}\n\n\t// Return the resulting serialization\n\treturn s.join( \"&\" );\n};\n\njQuery.fn.extend( {\n\tserialize: function() {\n\t\treturn jQuery.param( this.serializeArray() );\n\t},\n\tserializeArray: function() {\n\t\treturn this.map( function() {\n\n\t\t\t// Can add propHook for \"elements\" to filter or add form elements\n\t\t\tvar elements = jQuery.prop( this, \"elements\" );\n\t\t\treturn elements ? jQuery.makeArray( elements ) : this;\n\t\t} ).filter( function() {\n\t\t\tvar type = this.type;\n\n\t\t\t// Use .is( \":disabled\" ) so that fieldset[disabled] works\n\t\t\treturn this.name && !jQuery( this ).is( \":disabled\" ) &&\n\t\t\t\trsubmittable.test( this.nodeName ) && !rsubmitterTypes.test( type ) &&\n\t\t\t\t( this.checked || !rcheckableType.test( type ) );\n\t\t} ).map( function( _i, elem ) {\n\t\t\tvar val = jQuery( this ).val();\n\n\t\t\tif ( val == null ) {\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\tif ( Array.isArray( val ) ) {\n\t\t\t\treturn jQuery.map( val, function( val ) {\n\t\t\t\t\treturn { name: elem.name, value: val.replace( rCRLF, \"\\r\\n\" ) };\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\treturn { name: elem.name, value: val.replace( rCRLF, \"\\r\\n\" ) };\n\t\t} ).get();\n\t}\n} );\n\n\nvar\n\tr20 = /%20/g,\n\trhash = /#.*$/,\n\trantiCache = /([?&])_=[^&]*/,\n\trheaders = /^(.*?):[ \\t]*([^\\r\\n]*)$/mg,\n\n\t// trac-7653, trac-8125, trac-8152: local protocol detection\n\trlocalProtocol = /^(?:about|app|app-storage|.+-extension|file|res|widget):$/,\n\trnoContent = /^(?:GET|HEAD)$/,\n\trprotocol = /^\\/\\//,\n\n\t/* Prefilters\n\t * 1) They are useful to introduce custom dataTypes (see ajax/jsonp.js for an example)\n\t * 2) These are called:\n\t *    - BEFORE asking for a transport\n\t *    - AFTER param serialization (s.data is a string if s.processData is true)\n\t * 3) key is the dataType\n\t * 4) the catchall symbol \"*\" can be used\n\t * 5) execution will start with transport dataType and THEN continue down to \"*\" if needed\n\t */\n\tprefilters = {},\n\n\t/* Transports bindings\n\t * 1) key is the dataType\n\t * 2) the catchall symbol \"*\" can be used\n\t * 3) selection will start with transport dataType and THEN go to \"*\" if needed\n\t */\n\ttransports = {},\n\n\t// Avoid comment-prolog char sequence (trac-10098); must appease lint and evade compression\n\tallTypes = \"*/\".concat( \"*\" ),\n\n\t// Anchor tag for parsing the document origin\n\toriginAnchor = document.createElement( \"a\" );\n\noriginAnchor.href = location.href;\n\n// Base \"constructor\" for jQuery.ajaxPrefilter and jQuery.ajaxTransport\nfunction addToPrefiltersOrTransports( structure ) {\n\n\t// dataTypeExpression is optional and defaults to \"*\"\n\treturn function( dataTypeExpression, func ) {\n\n\t\tif ( typeof dataTypeExpression !== \"string\" ) {\n\t\t\tfunc = dataTypeExpression;\n\t\t\tdataTypeExpression = \"*\";\n\t\t}\n\n\t\tvar dataType,\n\t\t\ti = 0,\n\t\t\tdataTypes = dataTypeExpression.toLowerCase().match( rnothtmlwhite ) || [];\n\n\t\tif ( isFunction( func ) ) {\n\n\t\t\t// For each dataType in the dataTypeExpression\n\t\t\twhile ( ( dataType = dataTypes[ i++ ] ) ) {\n\n\t\t\t\t// Prepend if requested\n\t\t\t\tif ( dataType[ 0 ] === \"+\" ) {\n\t\t\t\t\tdataType = dataType.slice( 1 ) || \"*\";\n\t\t\t\t\t( structure[ dataType ] = structure[ dataType ] || [] ).unshift( func );\n\n\t\t\t\t// Otherwise append\n\t\t\t\t} else {\n\t\t\t\t\t( structure[ dataType ] = structure[ dataType ] || [] ).push( func );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n}\n\n// Base inspection function for prefilters and transports\nfunction inspectPrefiltersOrTransports( structure, options, originalOptions, jqXHR ) {\n\n\tvar inspected = {},\n\t\tseekingTransport = ( structure === transports );\n\n\tfunction inspect( dataType ) {\n\t\tvar selected;\n\t\tinspected[ dataType ] = true;\n\t\tjQuery.each( structure[ dataType ] || [], function( _, prefilterOrFactory ) {\n\t\t\tvar dataTypeOrTransport = prefilterOrFactory( options, originalOptions, jqXHR );\n\t\t\tif ( typeof dataTypeOrTransport === \"string\" &&\n\t\t\t\t!seekingTransport && !inspected[ dataTypeOrTransport ] ) {\n\n\t\t\t\toptions.dataTypes.unshift( dataTypeOrTransport );\n\t\t\t\tinspect( dataTypeOrTransport );\n\t\t\t\treturn false;\n\t\t\t} else if ( seekingTransport ) {\n\t\t\t\treturn !( selected = dataTypeOrTransport );\n\t\t\t}\n\t\t} );\n\t\treturn selected;\n\t}\n\n\treturn inspect( options.dataTypes[ 0 ] ) || !inspected[ \"*\" ] && inspect( \"*\" );\n}\n\n// A special extend for ajax options\n// that takes \"flat\" options (not to be deep extended)\n// Fixes trac-9887\nfunction ajaxExtend( target, src ) {\n\tvar key, deep,\n\t\tflatOptions = jQuery.ajaxSettings.flatOptions || {};\n\n\tfor ( key in src ) {\n\t\tif ( src[ key ] !== undefined ) {\n\t\t\t( flatOptions[ key ] ? target : ( deep || ( deep = {} ) ) )[ key ] = src[ key ];\n\t\t}\n\t}\n\tif ( deep ) {\n\t\tjQuery.extend( true, target, deep );\n\t}\n\n\treturn target;\n}\n\n/* Handles responses to an ajax request:\n * - finds the right dataType (mediates between content-type and expected dataType)\n * - returns the corresponding response\n */\nfunction ajaxHandleResponses( s, jqXHR, responses ) {\n\n\tvar ct, type, finalDataType, firstDataType,\n\t\tcontents = s.contents,\n\t\tdataTypes = s.dataTypes;\n\n\t// Remove auto dataType and get content-type in the process\n\twhile ( dataTypes[ 0 ] === \"*\" ) {\n\t\tdataTypes.shift();\n\t\tif ( ct === undefined ) {\n\t\t\tct = s.mimeType || jqXHR.getResponseHeader( \"Content-Type\" );\n\t\t}\n\t}\n\n\t// Check if we're dealing with a known content-type\n\tif ( ct ) {\n\t\tfor ( type in contents ) {\n\t\t\tif ( contents[ type ] && contents[ type ].test( ct ) ) {\n\t\t\t\tdataTypes.unshift( type );\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Check to see if we have a response for the expected dataType\n\tif ( dataTypes[ 0 ] in responses ) {\n\t\tfinalDataType = dataTypes[ 0 ];\n\t} else {\n\n\t\t// Try convertible dataTypes\n\t\tfor ( type in responses ) {\n\t\t\tif ( !dataTypes[ 0 ] || s.converters[ type + \" \" + dataTypes[ 0 ] ] ) {\n\t\t\t\tfinalDataType = type;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif ( !firstDataType ) {\n\t\t\t\tfirstDataType = type;\n\t\t\t}\n\t\t}\n\n\t\t// Or just use first one\n\t\tfinalDataType = finalDataType || firstDataType;\n\t}\n\n\t// If we found a dataType\n\t// We add the dataType to the list if needed\n\t// and return the corresponding response\n\tif ( finalDataType ) {\n\t\tif ( finalDataType !== dataTypes[ 0 ] ) {\n\t\t\tdataTypes.unshift( finalDataType );\n\t\t}\n\t\treturn responses[ finalDataType ];\n\t}\n}\n\n/* Chain conversions given the request and the original response\n * Also sets the responseXXX fields on the jqXHR instance\n */\nfunction ajaxConvert( s, response, jqXHR, isSuccess ) {\n\tvar conv2, current, conv, tmp, prev,\n\t\tconverters = {},\n\n\t\t// Work with a copy of dataTypes in case we need to modify it for conversion\n\t\tdataTypes = s.dataTypes.slice();\n\n\t// Create converters map with lowercased keys\n\tif ( dataTypes[ 1 ] ) {\n\t\tfor ( conv in s.converters ) {\n\t\t\tconverters[ conv.toLowerCase() ] = s.converters[ conv ];\n\t\t}\n\t}\n\n\tcurrent = dataTypes.shift();\n\n\t// Convert to each sequential dataType\n\twhile ( current ) {\n\n\t\tif ( s.responseFields[ current ] ) {\n\t\t\tjqXHR[ s.responseFields[ current ] ] = response;\n\t\t}\n\n\t\t// Apply the dataFilter if provided\n\t\tif ( !prev && isSuccess && s.dataFilter ) {\n\t\t\tresponse = s.dataFilter( response, s.dataType );\n\t\t}\n\n\t\tprev = current;\n\t\tcurrent = dataTypes.shift();\n\n\t\tif ( current ) {\n\n\t\t\t// There's only work to do if current dataType is non-auto\n\t\t\tif ( current === \"*\" ) {\n\n\t\t\t\tcurrent = prev;\n\n\t\t\t// Convert response if prev dataType is non-auto and differs from current\n\t\t\t} else if ( prev !== \"*\" && prev !== current ) {\n\n\t\t\t\t// Seek a direct converter\n\t\t\t\tconv = converters[ prev + \" \" + current ] || converters[ \"* \" + current ];\n\n\t\t\t\t// If none found, seek a pair\n\t\t\t\tif ( !conv ) {\n\t\t\t\t\tfor ( conv2 in converters ) {\n\n\t\t\t\t\t\t// If conv2 outputs current\n\t\t\t\t\t\ttmp = conv2.split( \" \" );\n\t\t\t\t\t\tif ( tmp[ 1 ] === current ) {\n\n\t\t\t\t\t\t\t// If prev can be converted to accepted input\n\t\t\t\t\t\t\tconv = converters[ prev + \" \" + tmp[ 0 ] ] ||\n\t\t\t\t\t\t\t\tconverters[ \"* \" + tmp[ 0 ] ];\n\t\t\t\t\t\t\tif ( conv ) {\n\n\t\t\t\t\t\t\t\t// Condense equivalence converters\n\t\t\t\t\t\t\t\tif ( conv === true ) {\n\t\t\t\t\t\t\t\t\tconv = converters[ conv2 ];\n\n\t\t\t\t\t\t\t\t// Otherwise, insert the intermediate dataType\n\t\t\t\t\t\t\t\t} else if ( converters[ conv2 ] !== true ) {\n\t\t\t\t\t\t\t\t\tcurrent = tmp[ 0 ];\n\t\t\t\t\t\t\t\t\tdataTypes.unshift( tmp[ 1 ] );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Apply converter (if not an equivalence)\n\t\t\t\tif ( conv !== true ) {\n\n\t\t\t\t\t// Unless errors are allowed to bubble, catch and return them\n\t\t\t\t\tif ( conv && s.throws ) {\n\t\t\t\t\t\tresponse = conv( response );\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tresponse = conv( response );\n\t\t\t\t\t\t} catch ( e ) {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tstate: \"parsererror\",\n\t\t\t\t\t\t\t\terror: conv ? e : \"No conversion from \" + prev + \" to \" + current\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn { state: \"success\", data: response };\n}\n\njQuery.extend( {\n\n\t// Counter for holding the number of active queries\n\tactive: 0,\n\n\t// Last-Modified header cache for next request\n\tlastModified: {},\n\tetag: {},\n\n\tajaxSettings: {\n\t\turl: location.href,\n\t\ttype: \"GET\",\n\t\tisLocal: rlocalProtocol.test( location.protocol ),\n\t\tglobal: true,\n\t\tprocessData: true,\n\t\tasync: true,\n\t\tcontentType: \"application/x-www-form-urlencoded; charset=UTF-8\",\n\n\t\t/*\n\t\ttimeout: 0,\n\t\tdata: null,\n\t\tdataType: null,\n\t\tusername: null,\n\t\tpassword: null,\n\t\tcache: null,\n\t\tthrows: false,\n\t\ttraditional: false,\n\t\theaders: {},\n\t\t*/\n\n\t\taccepts: {\n\t\t\t\"*\": allTypes,\n\t\t\ttext: \"text/plain\",\n\t\t\thtml: \"text/html\",\n\t\t\txml: \"application/xml, text/xml\",\n\t\t\tjson: \"application/json, text/javascript\"\n\t\t},\n\n\t\tcontents: {\n\t\t\txml: /\\bxml\\b/,\n\t\t\thtml: /\\bhtml/,\n\t\t\tjson: /\\bjson\\b/\n\t\t},\n\n\t\tresponseFields: {\n\t\t\txml: \"responseXML\",\n\t\t\ttext: \"responseText\",\n\t\t\tjson: \"responseJSON\"\n\t\t},\n\n\t\t// Data converters\n\t\t// Keys separate source (or catchall \"*\") and destination types with a single space\n\t\tconverters: {\n\n\t\t\t// Convert anything to text\n\t\t\t\"* text\": String,\n\n\t\t\t// Text to html (true = no transformation)\n\t\t\t\"text html\": true,\n\n\t\t\t// Evaluate text as a json expression\n\t\t\t\"text json\": JSON.parse,\n\n\t\t\t// Parse text as xml\n\t\t\t\"text xml\": jQuery.parseXML\n\t\t},\n\n\t\t// For options that shouldn't be deep extended:\n\t\t// you can add your own custom options here if\n\t\t// and when you create one that shouldn't be\n\t\t// deep extended (see ajaxExtend)\n\t\tflatOptions: {\n\t\t\turl: true,\n\t\t\tcontext: true\n\t\t}\n\t},\n\n\t// Creates a full fledged settings object into target\n\t// with both ajaxSettings and settings fields.\n\t// If target is omitted, writes into ajaxSettings.\n\tajaxSetup: function( target, settings ) {\n\t\treturn settings ?\n\n\t\t\t// Building a settings object\n\t\t\tajaxExtend( ajaxExtend( target, jQuery.ajaxSettings ), settings ) :\n\n\t\t\t// Extending ajaxSettings\n\t\t\tajaxExtend( jQuery.ajaxSettings, target );\n\t},\n\n\tajaxPrefilter: addToPrefiltersOrTransports( prefilters ),\n\tajaxTransport: addToPrefiltersOrTransports( transports ),\n\n\t// Main method\n\tajax: function( url, options ) {\n\n\t\t// If url is an object, simulate pre-1.5 signature\n\t\tif ( typeof url === \"object\" ) {\n\t\t\toptions = url;\n\t\t\turl = undefined;\n\t\t}\n\n\t\t// Force options to be an object\n\t\toptions = options || {};\n\n\t\tvar transport,\n\n\t\t\t// URL without anti-cache param\n\t\t\tcacheURL,\n\n\t\t\t// Response headers\n\t\t\tresponseHeadersString,\n\t\t\tresponseHeaders,\n\n\t\t\t// timeout handle\n\t\t\ttimeoutTimer,\n\n\t\t\t// Url cleanup var\n\t\t\turlAnchor,\n\n\t\t\t// Request state (becomes false upon send and true upon completion)\n\t\t\tcompleted,\n\n\t\t\t// To know if global events are to be dispatched\n\t\t\tfireGlobals,\n\n\t\t\t// Loop variable\n\t\t\ti,\n\n\t\t\t// uncached part of the url\n\t\t\tuncached,\n\n\t\t\t// Create the final options object\n\t\t\ts = jQuery.ajaxSetup( {}, options ),\n\n\t\t\t// Callbacks context\n\t\t\tcallbackContext = s.context || s,\n\n\t\t\t// Context for global events is callbackContext if it is a DOM node or jQuery collection\n\t\t\tglobalEventContext = s.context &&\n\t\t\t\t( callbackContext.nodeType || callbackContext.jquery ) ?\n\t\t\t\tjQuery( callbackContext ) :\n\t\t\t\tjQuery.event,\n\n\t\t\t// Deferreds\n\t\t\tdeferred = jQuery.Deferred(),\n\t\t\tcompleteDeferred = jQuery.Callbacks( \"once memory\" ),\n\n\t\t\t// Status-dependent callbacks\n\t\t\tstatusCode = s.statusCode || {},\n\n\t\t\t// Headers (they are sent all at once)\n\t\t\trequestHeaders = {},\n\t\t\trequestHeadersNames = {},\n\n\t\t\t// Default abort message\n\t\t\tstrAbort = \"canceled\",\n\n\t\t\t// Fake xhr\n\t\t\tjqXHR = {\n\t\t\t\treadyState: 0,\n\n\t\t\t\t// Builds headers hashtable if needed\n\t\t\t\tgetResponseHeader: function( key ) {\n\t\t\t\t\tvar match;\n\t\t\t\t\tif ( completed ) {\n\t\t\t\t\t\tif ( !responseHeaders ) {\n\t\t\t\t\t\t\tresponseHeaders = {};\n\t\t\t\t\t\t\twhile ( ( match = rheaders.exec( responseHeadersString ) ) ) {\n\t\t\t\t\t\t\t\tresponseHeaders[ match[ 1 ].toLowerCase() + \" \" ] =\n\t\t\t\t\t\t\t\t\t( responseHeaders[ match[ 1 ].toLowerCase() + \" \" ] || [] )\n\t\t\t\t\t\t\t\t\t\t.concat( match[ 2 ] );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tmatch = responseHeaders[ key.toLowerCase() + \" \" ];\n\t\t\t\t\t}\n\t\t\t\t\treturn match == null ? null : match.join( \", \" );\n\t\t\t\t},\n\n\t\t\t\t// Raw string\n\t\t\t\tgetAllResponseHeaders: function() {\n\t\t\t\t\treturn completed ? responseHeadersString : null;\n\t\t\t\t},\n\n\t\t\t\t// Caches the header\n\t\t\t\tsetRequestHeader: function( name, value ) {\n\t\t\t\t\tif ( completed == null ) {\n\t\t\t\t\t\tname = requestHeadersNames[ name.toLowerCase() ] =\n\t\t\t\t\t\t\trequestHeadersNames[ name.toLowerCase() ] || name;\n\t\t\t\t\t\trequestHeaders[ name ] = value;\n\t\t\t\t\t}\n\t\t\t\t\treturn this;\n\t\t\t\t},\n\n\t\t\t\t// Overrides response content-type header\n\t\t\t\toverrideMimeType: function( type ) {\n\t\t\t\t\tif ( completed == null ) {\n\t\t\t\t\t\ts.mimeType = type;\n\t\t\t\t\t}\n\t\t\t\t\treturn this;\n\t\t\t\t},\n\n\t\t\t\t// Status-dependent callbacks\n\t\t\t\tstatusCode: function( map ) {\n\t\t\t\t\tvar code;\n\t\t\t\t\tif ( map ) {\n\t\t\t\t\t\tif ( completed ) {\n\n\t\t\t\t\t\t\t// Execute the appropriate callbacks\n\t\t\t\t\t\t\tjqXHR.always( map[ jqXHR.status ] );\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t// Lazy-add the new callbacks in a way that preserves old ones\n\t\t\t\t\t\t\tfor ( code in map ) {\n\t\t\t\t\t\t\t\tstatusCode[ code ] = [ statusCode[ code ], map[ code ] ];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn this;\n\t\t\t\t},\n\n\t\t\t\t// Cancel the request\n\t\t\t\tabort: function( statusText ) {\n\t\t\t\t\tvar finalText = statusText || strAbort;\n\t\t\t\t\tif ( transport ) {\n\t\t\t\t\t\ttransport.abort( finalText );\n\t\t\t\t\t}\n\t\t\t\t\tdone( 0, finalText );\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\t\t\t};\n\n\t\t// Attach deferreds\n\t\tdeferred.promise( jqXHR );\n\n\t\t// Add protocol if not provided (prefilters might expect it)\n\t\t// Handle falsy url in the settings object (trac-10093: consistency with old signature)\n\t\t// We also use the url parameter if available\n\t\ts.url = ( ( url || s.url || location.href ) + \"\" )\n\t\t\t.replace( rprotocol, location.protocol + \"//\" );\n\n\t\t// Alias method option to type as per ticket trac-12004\n\t\ts.type = options.method || options.type || s.method || s.type;\n\n\t\t// Extract dataTypes list\n\t\ts.dataTypes = ( s.dataType || \"*\" ).toLowerCase().match( rnothtmlwhite ) || [ \"\" ];\n\n\t\t// A cross-domain request is in order when the origin doesn't match the current origin.\n\t\tif ( s.crossDomain == null ) {\n\t\t\turlAnchor = document.createElement( \"a\" );\n\n\t\t\t// Support: IE <=8 - 11, Edge 12 - 15\n\t\t\t// IE throws exception on accessing the href property if url is malformed,\n\t\t\t// e.g. http://example.com:80x/\n\t\t\ttry {\n\t\t\t\turlAnchor.href = s.url;\n\n\t\t\t\t// Support: IE <=8 - 11 only\n\t\t\t\t// Anchor's host property isn't correctly set when s.url is relative\n\t\t\t\turlAnchor.href = urlAnchor.href;\n\t\t\t\ts.crossDomain = originAnchor.protocol + \"//\" + originAnchor.host !==\n\t\t\t\t\turlAnchor.protocol + \"//\" + urlAnchor.host;\n\t\t\t} catch ( e ) {\n\n\t\t\t\t// If there is an error parsing the URL, assume it is crossDomain,\n\t\t\t\t// it can be rejected by the transport if it is invalid\n\t\t\t\ts.crossDomain = true;\n\t\t\t}\n\t\t}\n\n\t\t// Convert data if not already a string\n\t\tif ( s.data && s.processData && typeof s.data !== \"string\" ) {\n\t\t\ts.data = jQuery.param( s.data, s.traditional );\n\t\t}\n\n\t\t// Apply prefilters\n\t\tinspectPrefiltersOrTransports( prefilters, s, options, jqXHR );\n\n\t\t// If request was aborted inside a prefilter, stop there\n\t\tif ( completed ) {\n\t\t\treturn jqXHR;\n\t\t}\n\n\t\t// We can fire global events as of now if asked to\n\t\t// Don't fire events if jQuery.event is undefined in an AMD-usage scenario (trac-15118)\n\t\tfireGlobals = jQuery.event && s.global;\n\n\t\t// Watch for a new set of requests\n\t\tif ( fireGlobals && jQuery.active++ === 0 ) {\n\t\t\tjQuery.event.trigger( \"ajaxStart\" );\n\t\t}\n\n\t\t// Uppercase the type\n\t\ts.type = s.type.toUpperCase();\n\n\t\t// Determine if request has content\n\t\ts.hasContent = !rnoContent.test( s.type );\n\n\t\t// Save the URL in case we're toying with the If-Modified-Since\n\t\t// and/or If-None-Match header later on\n\t\t// Remove hash to simplify url manipulation\n\t\tcacheURL = s.url.replace( rhash, \"\" );\n\n\t\t// More options handling for requests with no content\n\t\tif ( !s.hasContent ) {\n\n\t\t\t// Remember the hash so we can put it back\n\t\t\tuncached = s.url.slice( cacheURL.length );\n\n\t\t\t// If data is available and should be processed, append data to url\n\t\t\tif ( s.data && ( s.processData || typeof s.data === \"string\" ) ) {\n\t\t\t\tcacheURL += ( rquery.test( cacheURL ) ? \"&\" : \"?\" ) + s.data;\n\n\t\t\t\t// trac-9682: remove data so that it's not used in an eventual retry\n\t\t\t\tdelete s.data;\n\t\t\t}\n\n\t\t\t// Add or update anti-cache param if needed\n\t\t\tif ( s.cache === false ) {\n\t\t\t\tcacheURL = cacheURL.replace( rantiCache, \"$1\" );\n\t\t\t\tuncached = ( rquery.test( cacheURL ) ? \"&\" : \"?\" ) + \"_=\" + ( nonce.guid++ ) +\n\t\t\t\t\tuncached;\n\t\t\t}\n\n\t\t\t// Put hash and anti-cache on the URL that will be requested (gh-1732)\n\t\t\ts.url = cacheURL + uncached;\n\n\t\t// Change '%20' to '+' if this is encoded form body content (gh-2658)\n\t\t} else if ( s.data && s.processData &&\n\t\t\t( s.contentType || \"\" ).indexOf( \"application/x-www-form-urlencoded\" ) === 0 ) {\n\t\t\ts.data = s.data.replace( r20, \"+\" );\n\t\t}\n\n\t\t// Set the If-Modified-Since and/or If-None-Match header, if in ifModified mode.\n\t\tif ( s.ifModified ) {\n\t\t\tif ( jQuery.lastModified[ cacheURL ] ) {\n\t\t\t\tjqXHR.setRequestHeader( \"If-Modified-Since\", jQuery.lastModified[ cacheURL ] );\n\t\t\t}\n\t\t\tif ( jQuery.etag[ cacheURL ] ) {\n\t\t\t\tjqXHR.setRequestHeader( \"If-None-Match\", jQuery.etag[ cacheURL ] );\n\t\t\t}\n\t\t}\n\n\t\t// Set the correct header, if data is being sent\n\t\tif ( s.data && s.hasContent && s.contentType !== false || options.contentType ) {\n\t\t\tjqXHR.setRequestHeader( \"Content-Type\", s.contentType );\n\t\t}\n\n\t\t// Set the Accepts header for the server, depending on the dataType\n\t\tjqXHR.setRequestHeader(\n\t\t\t\"Accept\",\n\t\t\ts.dataTypes[ 0 ] && s.accepts[ s.dataTypes[ 0 ] ] ?\n\t\t\t\ts.accepts[ s.dataTypes[ 0 ] ] +\n\t\t\t\t\t( s.dataTypes[ 0 ] !== \"*\" ? \", \" + allTypes + \"; q=0.01\" : \"\" ) :\n\t\t\t\ts.accepts[ \"*\" ]\n\t\t);\n\n\t\t// Check for headers option\n\t\tfor ( i in s.headers ) {\n\t\t\tjqXHR.setRequestHeader( i, s.headers[ i ] );\n\t\t}\n\n\t\t// Allow custom headers/mimetypes and early abort\n\t\tif ( s.beforeSend &&\n\t\t\t( s.beforeSend.call( callbackContext, jqXHR, s ) === false || completed ) ) {\n\n\t\t\t// Abort if not done already and return\n\t\t\treturn jqXHR.abort();\n\t\t}\n\n\t\t// Aborting is no longer a cancellation\n\t\tstrAbort = \"abort\";\n\n\t\t// Install callbacks on deferreds\n\t\tcompleteDeferred.add( s.complete );\n\t\tjqXHR.done( s.success );\n\t\tjqXHR.fail( s.error );\n\n\t\t// Get transport\n\t\ttransport = inspectPrefiltersOrTransports( transports, s, options, jqXHR );\n\n\t\t// If no transport, we auto-abort\n\t\tif ( !transport ) {\n\t\t\tdone( -1, \"No Transport\" );\n\t\t} else {\n\t\t\tjqXHR.readyState = 1;\n\n\t\t\t// Send global event\n\t\t\tif ( fireGlobals ) {\n\t\t\t\tglobalEventContext.trigger( \"ajaxSend\", [ jqXHR, s ] );\n\t\t\t}\n\n\t\t\t// If request was aborted inside ajaxSend, stop there\n\t\t\tif ( completed ) {\n\t\t\t\treturn jqXHR;\n\t\t\t}\n\n\t\t\t// Timeout\n\t\t\tif ( s.async && s.timeout > 0 ) {\n\t\t\t\ttimeoutTimer = window.setTimeout( function() {\n\t\t\t\t\tjqXHR.abort( \"timeout\" );\n\t\t\t\t}, s.timeout );\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\tcompleted = false;\n\t\t\t\ttransport.send( requestHeaders, done );\n\t\t\t} catch ( e ) {\n\n\t\t\t\t// Rethrow post-completion exceptions\n\t\t\t\tif ( completed ) {\n\t\t\t\t\tthrow e;\n\t\t\t\t}\n\n\t\t\t\t// Propagate others as results\n\t\t\t\tdone( -1, e );\n\t\t\t}\n\t\t}\n\n\t\t// Callback for when everything is done\n\t\tfunction done( status, nativeStatusText, responses, headers ) {\n\t\t\tvar isSuccess, success, error, response, modified,\n\t\t\t\tstatusText = nativeStatusText;\n\n\t\t\t// Ignore repeat invocations\n\t\t\tif ( completed ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tcompleted = true;\n\n\t\t\t// Clear timeout if it exists\n\t\t\tif ( timeoutTimer ) {\n\t\t\t\twindow.clearTimeout( timeoutTimer );\n\t\t\t}\n\n\t\t\t// Dereference transport for early garbage collection\n\t\t\t// (no matter how long the jqXHR object will be used)\n\t\t\ttransport = undefined;\n\n\t\t\t// Cache response headers\n\t\t\tresponseHeadersString = headers || \"\";\n\n\t\t\t// Set readyState\n\t\t\tjqXHR.readyState = status > 0 ? 4 : 0;\n\n\t\t\t// Determine if successful\n\t\t\tisSuccess = status >= 200 && status < 300 || status === 304;\n\n\t\t\t// Get response data\n\t\t\tif ( responses ) {\n\t\t\t\tresponse = ajaxHandleResponses( s, jqXHR, responses );\n\t\t\t}\n\n\t\t\t// Use a noop converter for missing script but not if jsonp\n\t\t\tif ( !isSuccess &&\n\t\t\t\tjQuery.inArray( \"script\", s.dataTypes ) > -1 &&\n\t\t\t\tjQuery.inArray( \"json\", s.dataTypes ) < 0 ) {\n\t\t\t\ts.converters[ \"text script\" ] = function() {};\n\t\t\t}\n\n\t\t\t// Convert no matter what (that way responseXXX fields are always set)\n\t\t\tresponse = ajaxConvert( s, response, jqXHR, isSuccess );\n\n\t\t\t// If successful, handle type chaining\n\t\t\tif ( isSuccess ) {\n\n\t\t\t\t// Set the If-Modified-Since and/or If-None-Match header, if in ifModified mode.\n\t\t\t\tif ( s.ifModified ) {\n\t\t\t\t\tmodified = jqXHR.getResponseHeader( \"Last-Modified\" );\n\t\t\t\t\tif ( modified ) {\n\t\t\t\t\t\tjQuery.lastModified[ cacheURL ] = modified;\n\t\t\t\t\t}\n\t\t\t\t\tmodified = jqXHR.getResponseHeader( \"etag\" );\n\t\t\t\t\tif ( modified ) {\n\t\t\t\t\t\tjQuery.etag[ cacheURL ] = modified;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// if no content\n\t\t\t\tif ( status === 204 || s.type === \"HEAD\" ) {\n\t\t\t\t\tstatusText = \"nocontent\";\n\n\t\t\t\t// if not modified\n\t\t\t\t} else if ( status === 304 ) {\n\t\t\t\t\tstatusText = \"notmodified\";\n\n\t\t\t\t// If we have data, let's convert it\n\t\t\t\t} else {\n\t\t\t\t\tstatusText = response.state;\n\t\t\t\t\tsuccess = response.data;\n\t\t\t\t\terror = response.error;\n\t\t\t\t\tisSuccess = !error;\n\t\t\t\t}\n\t\t\t} else {\n\n\t\t\t\t// Extract error from statusText and normalize for non-aborts\n\t\t\t\terror = statusText;\n\t\t\t\tif ( status || !statusText ) {\n\t\t\t\t\tstatusText = \"error\";\n\t\t\t\t\tif ( status < 0 ) {\n\t\t\t\t\t\tstatus = 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Set data for the fake xhr object\n\t\t\tjqXHR.status = status;\n\t\t\tjqXHR.statusText = ( nativeStatusText || statusText ) + \"\";\n\n\t\t\t// Success/Error\n\t\t\tif ( isSuccess ) {\n\t\t\t\tdeferred.resolveWith( callbackContext, [ success, statusText, jqXHR ] );\n\t\t\t} else {\n\t\t\t\tdeferred.rejectWith( callbackContext, [ jqXHR, statusText, error ] );\n\t\t\t}\n\n\t\t\t// Status-dependent callbacks\n\t\t\tjqXHR.statusCode( statusCode );\n\t\t\tstatusCode = undefined;\n\n\t\t\tif ( fireGlobals ) {\n\t\t\t\tglobalEventContext.trigger( isSuccess ? \"ajaxSuccess\" : \"ajaxError\",\n\t\t\t\t\t[ jqXHR, s, isSuccess ? success : error ] );\n\t\t\t}\n\n\t\t\t// Complete\n\t\t\tcompleteDeferred.fireWith( callbackContext, [ jqXHR, statusText ] );\n\n\t\t\tif ( fireGlobals ) {\n\t\t\t\tglobalEventContext.trigger( \"ajaxComplete\", [ jqXHR, s ] );\n\n\t\t\t\t// Handle the global AJAX counter\n\t\t\t\tif ( !( --jQuery.active ) ) {\n\t\t\t\t\tjQuery.event.trigger( \"ajaxStop\" );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn jqXHR;\n\t},\n\n\tgetJSON: function( url, data, callback ) {\n\t\treturn jQuery.get( url, data, callback, \"json\" );\n\t},\n\n\tgetScript: function( url, callback ) {\n\t\treturn jQuery.get( url, undefined, callback, \"script\" );\n\t}\n} );\n\njQuery.each( [ \"get\", \"post\" ], function( _i, method ) {\n\tjQuery[ method ] = function( url, data, callback, type ) {\n\n\t\t// Shift arguments if data argument was omitted\n\t\tif ( isFunction( data ) ) {\n\t\t\ttype = type || callback;\n\t\t\tcallback = data;\n\t\t\tdata = undefined;\n\t\t}\n\n\t\t// The url can be an options object (which then must have .url)\n\t\treturn jQuery.ajax( jQuery.extend( {\n\t\t\turl: url,\n\t\t\ttype: method,\n\t\t\tdataType: type,\n\t\t\tdata: data,\n\t\t\tsuccess: callback\n\t\t}, jQuery.isPlainObject( url ) && url ) );\n\t};\n} );\n\njQuery.ajaxPrefilter( function( s ) {\n\tvar i;\n\tfor ( i in s.headers ) {\n\t\tif ( i.toLowerCase() === \"content-type\" ) {\n\t\t\ts.contentType = s.headers[ i ] || \"\";\n\t\t}\n\t}\n} );\n\n\njQuery._evalUrl = function( url, options, doc ) {\n\treturn jQuery.ajax( {\n\t\turl: url,\n\n\t\t// Make this explicit, since user can override this through ajaxSetup (trac-11264)\n\t\ttype: \"GET\",\n\t\tdataType: \"script\",\n\t\tcache: true,\n\t\tasync: false,\n\t\tglobal: false,\n\n\t\t// Only evaluate the response if it is successful (gh-4126)\n\t\t// dataFilter is not invoked for failure responses, so using it instead\n\t\t// of the default converter is kludgy but it works.\n\t\tconverters: {\n\t\t\t\"text script\": function() {}\n\t\t},\n\t\tdataFilter: function( response ) {\n\t\t\tjQuery.globalEval( response, options, doc );\n\t\t}\n\t} );\n};\n\n\njQuery.fn.extend( {\n\twrapAll: function( html ) {\n\t\tvar wrap;\n\n\t\tif ( this[ 0 ] ) {\n\t\t\tif ( isFunction( html ) ) {\n\t\t\t\thtml = html.call( this[ 0 ] );\n\t\t\t}\n\n\t\t\t// The elements to wrap the target around\n\t\t\twrap = jQuery( html, this[ 0 ].ownerDocument ).eq( 0 ).clone( true );\n\n\t\t\tif ( this[ 0 ].parentNode ) {\n\t\t\t\twrap.insertBefore( this[ 0 ] );\n\t\t\t}\n\n\t\t\twrap.map( function() {\n\t\t\t\tvar elem = this;\n\n\t\t\t\twhile ( elem.firstElementChild ) {\n\t\t\t\t\telem = elem.firstElementChild;\n\t\t\t\t}\n\n\t\t\t\treturn elem;\n\t\t\t} ).append( this );\n\t\t}\n\n\t\treturn this;\n\t},\n\n\twrapInner: function( html ) {\n\t\tif ( isFunction( html ) ) {\n\t\t\treturn this.each( function( i ) {\n\t\t\t\tjQuery( this ).wrapInner( html.call( this, i ) );\n\t\t\t} );\n\t\t}\n\n\t\treturn this.each( function() {\n\t\t\tvar self = jQuery( this ),\n\t\t\t\tcontents = self.contents();\n\n\t\t\tif ( contents.length ) {\n\t\t\t\tcontents.wrapAll( html );\n\n\t\t\t} else {\n\t\t\t\tself.append( html );\n\t\t\t}\n\t\t} );\n\t},\n\n\twrap: function( html ) {\n\t\tvar htmlIsFunction = isFunction( html );\n\n\t\treturn this.each( function( i ) {\n\t\t\tjQuery( this ).wrapAll( htmlIsFunction ? html.call( this, i ) : html );\n\t\t} );\n\t},\n\n\tunwrap: function( selector ) {\n\t\tthis.parent( selector ).not( \"body\" ).each( function() {\n\t\t\tjQuery( this ).replaceWith( this.childNodes );\n\t\t} );\n\t\treturn this;\n\t}\n} );\n\n\njQuery.expr.pseudos.hidden = function( elem ) {\n\treturn !jQuery.expr.pseudos.visible( elem );\n};\njQuery.expr.pseudos.visible = function( elem ) {\n\treturn !!( elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length );\n};\n\n\n\n\njQuery.ajaxSettings.xhr = function() {\n\ttry {\n\t\treturn new window.XMLHttpRequest();\n\t} catch ( e ) {}\n};\n\nvar xhrSuccessStatus = {\n\n\t\t// File protocol always yields status code 0, assume 200\n\t\t0: 200,\n\n\t\t// Support: IE <=9 only\n\t\t// trac-1450: sometimes IE returns 1223 when it should be 204\n\t\t1223: 204\n\t},\n\txhrSupported = jQuery.ajaxSettings.xhr();\n\nsupport.cors = !!xhrSupported && ( \"withCredentials\" in xhrSupported );\nsupport.ajax = xhrSupported = !!xhrSupported;\n\njQuery.ajaxTransport( function( options ) {\n\tvar callback, errorCallback;\n\n\t// Cross domain only allowed if supported through XMLHttpRequest\n\tif ( support.cors || xhrSupported && !options.crossDomain ) {\n\t\treturn {\n\t\t\tsend: function( headers, complete ) {\n\t\t\t\tvar i,\n\t\t\t\t\txhr = options.xhr();\n\n\t\t\t\txhr.open(\n\t\t\t\t\toptions.type,\n\t\t\t\t\toptions.url,\n\t\t\t\t\toptions.async,\n\t\t\t\t\toptions.username,\n\t\t\t\t\toptions.password\n\t\t\t\t);\n\n\t\t\t\t// Apply custom fields if provided\n\t\t\t\tif ( options.xhrFields ) {\n\t\t\t\t\tfor ( i in options.xhrFields ) {\n\t\t\t\t\t\txhr[ i ] = options.xhrFields[ i ];\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Override mime type if needed\n\t\t\t\tif ( options.mimeType && xhr.overrideMimeType ) {\n\t\t\t\t\txhr.overrideMimeType( options.mimeType );\n\t\t\t\t}\n\n\t\t\t\t// X-Requested-With header\n\t\t\t\t// For cross-domain requests, seeing as conditions for a preflight are\n\t\t\t\t// akin to a jigsaw puzzle, we simply never set it to be sure.\n\t\t\t\t// (it can always be set on a per-request basis or even using ajaxSetup)\n\t\t\t\t// For same-domain requests, won't change header if already provided.\n\t\t\t\tif ( !options.crossDomain && !headers[ \"X-Requested-With\" ] ) {\n\t\t\t\t\theaders[ \"X-Requested-With\" ] = \"XMLHttpRequest\";\n\t\t\t\t}\n\n\t\t\t\t// Set headers\n\t\t\t\tfor ( i in headers ) {\n\t\t\t\t\txhr.setRequestHeader( i, headers[ i ] );\n\t\t\t\t}\n\n\t\t\t\t// Callback\n\t\t\t\tcallback = function( type ) {\n\t\t\t\t\treturn function() {\n\t\t\t\t\t\tif ( callback ) {\n\t\t\t\t\t\t\tcallback = errorCallback = xhr.onload =\n\t\t\t\t\t\t\t\txhr.onerror = xhr.onabort = xhr.ontimeout =\n\t\t\t\t\t\t\t\t\txhr.onreadystatechange = null;\n\n\t\t\t\t\t\t\tif ( type === \"abort\" ) {\n\t\t\t\t\t\t\t\txhr.abort();\n\t\t\t\t\t\t\t} else if ( type === \"error\" ) {\n\n\t\t\t\t\t\t\t\t// Support: IE <=9 only\n\t\t\t\t\t\t\t\t// On a manual native abort, IE9 throws\n\t\t\t\t\t\t\t\t// errors on any property access that is not readyState\n\t\t\t\t\t\t\t\tif ( typeof xhr.status !== \"number\" ) {\n\t\t\t\t\t\t\t\t\tcomplete( 0, \"error\" );\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tcomplete(\n\n\t\t\t\t\t\t\t\t\t\t// File: protocol always yields status 0; see trac-8605, trac-14207\n\t\t\t\t\t\t\t\t\t\txhr.status,\n\t\t\t\t\t\t\t\t\t\txhr.statusText\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tcomplete(\n\t\t\t\t\t\t\t\t\txhrSuccessStatus[ xhr.status ] || xhr.status,\n\t\t\t\t\t\t\t\t\txhr.statusText,\n\n\t\t\t\t\t\t\t\t\t// Support: IE <=9 only\n\t\t\t\t\t\t\t\t\t// IE9 has no XHR2 but throws on binary (trac-11426)\n\t\t\t\t\t\t\t\t\t// For XHR2 non-text, let the caller handle it (gh-2498)\n\t\t\t\t\t\t\t\t\t( xhr.responseType || \"text\" ) !== \"text\"  ||\n\t\t\t\t\t\t\t\t\ttypeof xhr.responseText !== \"string\" ?\n\t\t\t\t\t\t\t\t\t\t{ binary: xhr.response } :\n\t\t\t\t\t\t\t\t\t\t{ text: xhr.responseText },\n\t\t\t\t\t\t\t\t\txhr.getAllResponseHeaders()\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t};\n\n\t\t\t\t// Listen to events\n\t\t\t\txhr.onload = callback();\n\t\t\t\terrorCallback = xhr.onerror = xhr.ontimeout = callback( \"error\" );\n\n\t\t\t\t// Support: IE 9 only\n\t\t\t\t// Use onreadystatechange to replace onabort\n\t\t\t\t// to handle uncaught aborts\n\t\t\t\tif ( xhr.onabort !== undefined ) {\n\t\t\t\t\txhr.onabort = errorCallback;\n\t\t\t\t} else {\n\t\t\t\t\txhr.onreadystatechange = function() {\n\n\t\t\t\t\t\t// Check readyState before timeout as it changes\n\t\t\t\t\t\tif ( xhr.readyState === 4 ) {\n\n\t\t\t\t\t\t\t// Allow onerror to be called first,\n\t\t\t\t\t\t\t// but that will not handle a native abort\n\t\t\t\t\t\t\t// Also, save errorCallback to a variable\n\t\t\t\t\t\t\t// as xhr.onerror cannot be accessed\n\t\t\t\t\t\t\twindow.setTimeout( function() {\n\t\t\t\t\t\t\t\tif ( callback ) {\n\t\t\t\t\t\t\t\t\terrorCallback();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} );\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\t// Create the abort callback\n\t\t\t\tcallback = callback( \"abort\" );\n\n\t\t\t\ttry {\n\n\t\t\t\t\t// Do send the request (this may raise an exception)\n\t\t\t\t\txhr.send( options.hasContent && options.data || null );\n\t\t\t\t} catch ( e ) {\n\n\t\t\t\t\t// trac-14683: Only rethrow if this hasn't been notified as an error yet\n\t\t\t\t\tif ( callback ) {\n\t\t\t\t\t\tthrow e;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tabort: function() {\n\t\t\t\tif ( callback ) {\n\t\t\t\t\tcallback();\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t}\n} );\n\n\n\n\n// Prevent auto-execution of scripts when no explicit dataType was provided (See gh-2432)\njQuery.ajaxPrefilter( function( s ) {\n\tif ( s.crossDomain ) {\n\t\ts.contents.script = false;\n\t}\n} );\n\n// Install script dataType\njQuery.ajaxSetup( {\n\taccepts: {\n\t\tscript: \"text/javascript, application/javascript, \" +\n\t\t\t\"application/ecmascript, application/x-ecmascript\"\n\t},\n\tcontents: {\n\t\tscript: /\\b(?:java|ecma)script\\b/\n\t},\n\tconverters: {\n\t\t\"text script\": function( text ) {\n\t\t\tjQuery.globalEval( text );\n\t\t\treturn text;\n\t\t}\n\t}\n} );\n\n// Handle cache's special case and crossDomain\njQuery.ajaxPrefilter( \"script\", function( s ) {\n\tif ( s.cache === undefined ) {\n\t\ts.cache = false;\n\t}\n\tif ( s.crossDomain ) {\n\t\ts.type = \"GET\";\n\t}\n} );\n\n// Bind script tag hack transport\njQuery.ajaxTransport( \"script\", function( s ) {\n\n\t// This transport only deals with cross domain or forced-by-attrs requests\n\tif ( s.crossDomain || s.scriptAttrs ) {\n\t\tvar script, callback;\n\t\treturn {\n\t\t\tsend: function( _, complete ) {\n\t\t\t\tscript = jQuery( \"<script>\" )\n\t\t\t\t\t.attr( s.scriptAttrs || {} )\n\t\t\t\t\t.prop( { charset: s.scriptCharset, src: s.url } )\n\t\t\t\t\t.on( \"load error\", callback = function( evt ) {\n\t\t\t\t\t\tscript.remove();\n\t\t\t\t\t\tcallback = null;\n\t\t\t\t\t\tif ( evt ) {\n\t\t\t\t\t\t\tcomplete( evt.type === \"error\" ? 404 : 200, evt.type );\n\t\t\t\t\t\t}\n\t\t\t\t\t} );\n\n\t\t\t\t// Use native DOM manipulation to avoid our domManip AJAX trickery\n\t\t\t\tdocument.head.appendChild( script[ 0 ] );\n\t\t\t},\n\t\t\tabort: function() {\n\t\t\t\tif ( callback ) {\n\t\t\t\t\tcallback();\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t}\n} );\n\n\n\n\nvar oldCallbacks = [],\n\trjsonp = /(=)\\?(?=&|$)|\\?\\?/;\n\n// Default jsonp settings\njQuery.ajaxSetup( {\n\tjsonp: \"callback\",\n\tjsonpCallback: function() {\n\t\tvar callback = oldCallbacks.pop() || ( jQuery.expando + \"_\" + ( nonce.guid++ ) );\n\t\tthis[ callback ] = true;\n\t\treturn callback;\n\t}\n} );\n\n// Detect, normalize options and install callbacks for jsonp requests\njQuery.ajaxPrefilter( \"json jsonp\", function( s, originalSettings, jqXHR ) {\n\n\tvar callbackName, overwritten, responseContainer,\n\t\tjsonProp = s.jsonp !== false && ( rjsonp.test( s.url ) ?\n\t\t\t\"url\" :\n\t\t\ttypeof s.data === \"string\" &&\n\t\t\t\t( s.contentType || \"\" )\n\t\t\t\t\t.indexOf( \"application/x-www-form-urlencoded\" ) === 0 &&\n\t\t\t\trjsonp.test( s.data ) && \"data\"\n\t\t);\n\n\t// Handle iff the expected data type is \"jsonp\" or we have a parameter to set\n\tif ( jsonProp || s.dataTypes[ 0 ] === \"jsonp\" ) {\n\n\t\t// Get callback name, remembering preexisting value associated with it\n\t\tcallbackName = s.jsonpCallback = isFunction( s.jsonpCallback ) ?\n\t\t\ts.jsonpCallback() :\n\t\t\ts.jsonpCallback;\n\n\t\t// Insert callback into url or form data\n\t\tif ( jsonProp ) {\n\t\t\ts[ jsonProp ] = s[ jsonProp ].replace( rjsonp, \"$1\" + callbackName );\n\t\t} else if ( s.jsonp !== false ) {\n\t\t\ts.url += ( rquery.test( s.url ) ? \"&\" : \"?\" ) + s.jsonp + \"=\" + callbackName;\n\t\t}\n\n\t\t// Use data converter to retrieve json after script execution\n\t\ts.converters[ \"script json\" ] = function() {\n\t\t\tif ( !responseContainer ) {\n\t\t\t\tjQuery.error( callbackName + \" was not called\" );\n\t\t\t}\n\t\t\treturn responseContainer[ 0 ];\n\t\t};\n\n\t\t// Force json dataType\n\t\ts.dataTypes[ 0 ] = \"json\";\n\n\t\t// Install callback\n\t\toverwritten = window[ callbackName ];\n\t\twindow[ callbackName ] = function() {\n\t\t\tresponseContainer = arguments;\n\t\t};\n\n\t\t// Clean-up function (fires after converters)\n\t\tjqXHR.always( function() {\n\n\t\t\t// If previous value didn't exist - remove it\n\t\t\tif ( overwritten === undefined ) {\n\t\t\t\tjQuery( window ).removeProp( callbackName );\n\n\t\t\t// Otherwise restore preexisting value\n\t\t\t} else {\n\t\t\t\twindow[ callbackName ] = overwritten;\n\t\t\t}\n\n\t\t\t// Save back as free\n\t\t\tif ( s[ callbackName ] ) {\n\n\t\t\t\t// Make sure that re-using the options doesn't screw things around\n\t\t\t\ts.jsonpCallback = originalSettings.jsonpCallback;\n\n\t\t\t\t// Save the callback name for future use\n\t\t\t\toldCallbacks.push( callbackName );\n\t\t\t}\n\n\t\t\t// Call if it was a function and we have a response\n\t\t\tif ( responseContainer && isFunction( overwritten ) ) {\n\t\t\t\toverwritten( responseContainer[ 0 ] );\n\t\t\t}\n\n\t\t\tresponseContainer = overwritten = undefined;\n\t\t} );\n\n\t\t// Delegate to script\n\t\treturn \"script\";\n\t}\n} );\n\n\n\n\n// Support: Safari 8 only\n// In Safari 8 documents created via document.implementation.createHTMLDocument\n// collapse sibling forms: the second one becomes a child of the first one.\n// Because of that, this security measure has to be disabled in Safari 8.\n// https://bugs.webkit.org/show_bug.cgi?id=137337\nsupport.createHTMLDocument = ( function() {\n\tvar body = document.implementation.createHTMLDocument( \"\" ).body;\n\tbody.innerHTML = \"<form></form><form></form>\";\n\treturn body.childNodes.length === 2;\n} )();\n\n\n// Argument \"data\" should be string of html\n// context (optional): If specified, the fragment will be created in this context,\n// defaults to document\n// keepScripts (optional): If true, will include scripts passed in the html string\njQuery.parseHTML = function( data, context, keepScripts ) {\n\tif ( typeof data !== \"string\" ) {\n\t\treturn [];\n\t}\n\tif ( typeof context === \"boolean\" ) {\n\t\tkeepScripts = context;\n\t\tcontext = false;\n\t}\n\n\tvar base, parsed, scripts;\n\n\tif ( !context ) {\n\n\t\t// Stop scripts or inline event handlers from being executed immediately\n\t\t// by using document.implementation\n\t\tif ( support.createHTMLDocument ) {\n\t\t\tcontext = document.implementation.createHTMLDocument( \"\" );\n\n\t\t\t// Set the base href for the created document\n\t\t\t// so any parsed elements with URLs\n\t\t\t// are based on the document's URL (gh-2965)\n\t\t\tbase = context.createElement( \"base\" );\n\t\t\tbase.href = document.location.href;\n\t\t\tcontext.head.appendChild( base );\n\t\t} else {\n\t\t\tcontext = document;\n\t\t}\n\t}\n\n\tparsed = rsingleTag.exec( data );\n\tscripts = !keepScripts && [];\n\n\t// Single tag\n\tif ( parsed ) {\n\t\treturn [ context.createElement( parsed[ 1 ] ) ];\n\t}\n\n\tparsed = buildFragment( [ data ], context, scripts );\n\n\tif ( scripts && scripts.length ) {\n\t\tjQuery( scripts ).remove();\n\t}\n\n\treturn jQuery.merge( [], parsed.childNodes );\n};\n\n\n/**\n * Load a url into a page\n */\njQuery.fn.load = function( url, params, callback ) {\n\tvar selector, type, response,\n\t\tself = this,\n\t\toff = url.indexOf( \" \" );\n\n\tif ( off > -1 ) {\n\t\tselector = stripAndCollapse( url.slice( off ) );\n\t\turl = url.slice( 0, off );\n\t}\n\n\t// If it's a function\n\tif ( isFunction( params ) ) {\n\n\t\t// We assume that it's the callback\n\t\tcallback = params;\n\t\tparams = undefined;\n\n\t// Otherwise, build a param string\n\t} else if ( params && typeof params === \"object\" ) {\n\t\ttype = \"POST\";\n\t}\n\n\t// If we have elements to modify, make the request\n\tif ( self.length > 0 ) {\n\t\tjQuery.ajax( {\n\t\t\turl: url,\n\n\t\t\t// If \"type\" variable is undefined, then \"GET\" method will be used.\n\t\t\t// Make value of this field explicit since\n\t\t\t// user can override it through ajaxSetup method\n\t\t\ttype: type || \"GET\",\n\t\t\tdataType: \"html\",\n\t\t\tdata: params\n\t\t} ).done( function( responseText ) {\n\n\t\t\t// Save response for use in complete callback\n\t\t\tresponse = arguments;\n\n\t\t\tself.html( selector ?\n\n\t\t\t\t// If a selector was specified, locate the right elements in a dummy div\n\t\t\t\t// Exclude scripts to avoid IE 'Permission Denied' errors\n\t\t\t\tjQuery( \"<div>\" ).append( jQuery.parseHTML( responseText ) ).find( selector ) :\n\n\t\t\t\t// Otherwise use the full result\n\t\t\t\tresponseText );\n\n\t\t// If the request succeeds, this function gets \"data\", \"status\", \"jqXHR\"\n\t\t// but they are ignored because response was set above.\n\t\t// If it fails, this function gets \"jqXHR\", \"status\", \"error\"\n\t\t} ).always( callback && function( jqXHR, status ) {\n\t\t\tself.each( function() {\n\t\t\t\tcallback.apply( this, response || [ jqXHR.responseText, status, jqXHR ] );\n\t\t\t} );\n\t\t} );\n\t}\n\n\treturn this;\n};\n\n\n\n\njQuery.expr.pseudos.animated = function( elem ) {\n\treturn jQuery.grep( jQuery.timers, function( fn ) {\n\t\treturn elem === fn.elem;\n\t} ).length;\n};\n\n\n\n\njQuery.offset = {\n\tsetOffset: function( elem, options, i ) {\n\t\tvar curPosition, curLeft, curCSSTop, curTop, curOffset, curCSSLeft, calculatePosition,\n\t\t\tposition = jQuery.css( elem, \"position\" ),\n\t\t\tcurElem = jQuery( elem ),\n\t\t\tprops = {};\n\n\t\t// Set position first, in-case top/left are set even on static elem\n\t\tif ( position === \"static\" ) {\n\t\t\telem.style.position = \"relative\";\n\t\t}\n\n\t\tcurOffset = curElem.offset();\n\t\tcurCSSTop = jQuery.css( elem, \"top\" );\n\t\tcurCSSLeft = jQuery.css( elem, \"left\" );\n\t\tcalculatePosition = ( position === \"absolute\" || position === \"fixed\" ) &&\n\t\t\t( curCSSTop + curCSSLeft ).indexOf( \"auto\" ) > -1;\n\n\t\t// Need to be able to calculate position if either\n\t\t// top or left is auto and position is either absolute or fixed\n\t\tif ( calculatePosition ) {\n\t\t\tcurPosition = curElem.position();\n\t\t\tcurTop = curPosition.top;\n\t\t\tcurLeft = curPosition.left;\n\n\t\t} else {\n\t\t\tcurTop = parseFloat( curCSSTop ) || 0;\n\t\t\tcurLeft = parseFloat( curCSSLeft ) || 0;\n\t\t}\n\n\t\tif ( isFunction( options ) ) {\n\n\t\t\t// Use jQuery.extend here to allow modification of coordinates argument (gh-1848)\n\t\t\toptions = options.call( elem, i, jQuery.extend( {}, curOffset ) );\n\t\t}\n\n\t\tif ( options.top != null ) {\n\t\t\tprops.top = ( options.top - curOffset.top ) + curTop;\n\t\t}\n\t\tif ( options.left != null ) {\n\t\t\tprops.left = ( options.left - curOffset.left ) + curLeft;\n\t\t}\n\n\t\tif ( \"using\" in options ) {\n\t\t\toptions.using.call( elem, props );\n\n\t\t} else {\n\t\t\tcurElem.css( props );\n\t\t}\n\t}\n};\n\njQuery.fn.extend( {\n\n\t// offset() relates an element's border box to the document origin\n\toffset: function( options ) {\n\n\t\t// Preserve chaining for setter\n\t\tif ( arguments.length ) {\n\t\t\treturn options === undefined ?\n\t\t\t\tthis :\n\t\t\t\tthis.each( function( i ) {\n\t\t\t\t\tjQuery.offset.setOffset( this, options, i );\n\t\t\t\t} );\n\t\t}\n\n\t\tvar rect, win,\n\t\t\telem = this[ 0 ];\n\n\t\tif ( !elem ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Return zeros for disconnected and hidden (display: none) elements (gh-2310)\n\t\t// Support: IE <=11 only\n\t\t// Running getBoundingClientRect on a\n\t\t// disconnected node in IE throws an error\n\t\tif ( !elem.getClientRects().length ) {\n\t\t\treturn { top: 0, left: 0 };\n\t\t}\n\n\t\t// Get document-relative position by adding viewport scroll to viewport-relative gBCR\n\t\trect = elem.getBoundingClientRect();\n\t\twin = elem.ownerDocument.defaultView;\n\t\treturn {\n\t\t\ttop: rect.top + win.pageYOffset,\n\t\t\tleft: rect.left + win.pageXOffset\n\t\t};\n\t},\n\n\t// position() relates an element's margin box to its offset parent's padding box\n\t// This corresponds to the behavior of CSS absolute positioning\n\tposition: function() {\n\t\tif ( !this[ 0 ] ) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar offsetParent, offset, doc,\n\t\t\telem = this[ 0 ],\n\t\t\tparentOffset = { top: 0, left: 0 };\n\n\t\t// position:fixed elements are offset from the viewport, which itself always has zero offset\n\t\tif ( jQuery.css( elem, \"position\" ) === \"fixed\" ) {\n\n\t\t\t// Assume position:fixed implies availability of getBoundingClientRect\n\t\t\toffset = elem.getBoundingClientRect();\n\n\t\t} else {\n\t\t\toffset = this.offset();\n\n\t\t\t// Account for the *real* offset parent, which can be the document or its root element\n\t\t\t// when a statically positioned element is identified\n\t\t\tdoc = elem.ownerDocument;\n\t\t\toffsetParent = elem.offsetParent || doc.documentElement;\n\t\t\twhile ( offsetParent &&\n\t\t\t\t( offsetParent === doc.body || offsetParent === doc.documentElement ) &&\n\t\t\t\tjQuery.css( offsetParent, \"position\" ) === \"static\" ) {\n\n\t\t\t\toffsetParent = offsetParent.parentNode;\n\t\t\t}\n\t\t\tif ( offsetParent && offsetParent !== elem && offsetParent.nodeType === 1 ) {\n\n\t\t\t\t// Incorporate borders into its offset, since they are outside its content origin\n\t\t\t\tparentOffset = jQuery( offsetParent ).offset();\n\t\t\t\tparentOffset.top += jQuery.css( offsetParent, \"borderTopWidth\", true );\n\t\t\t\tparentOffset.left += jQuery.css( offsetParent, \"borderLeftWidth\", true );\n\t\t\t}\n\t\t}\n\n\t\t// Subtract parent offsets and element margins\n\t\treturn {\n\t\t\ttop: offset.top - parentOffset.top - jQuery.css( elem, \"marginTop\", true ),\n\t\t\tleft: offset.left - parentOffset.left - jQuery.css( elem, \"marginLeft\", true )\n\t\t};\n\t},\n\n\t// This method will return documentElement in the following cases:\n\t// 1) For the element inside the iframe without offsetParent, this method will return\n\t//    documentElement of the parent window\n\t// 2) For the hidden or detached element\n\t// 3) For body or html element, i.e. in case of the html node - it will return itself\n\t//\n\t// but those exceptions were never presented as a real life use-cases\n\t// and might be considered as more preferable results.\n\t//\n\t// This logic, however, is not guaranteed and can change at any point in the future\n\toffsetParent: function() {\n\t\treturn this.map( function() {\n\t\t\tvar offsetParent = this.offsetParent;\n\n\t\t\twhile ( offsetParent && jQuery.css( offsetParent, \"position\" ) === \"static\" ) {\n\t\t\t\toffsetParent = offsetParent.offsetParent;\n\t\t\t}\n\n\t\t\treturn offsetParent || documentElement;\n\t\t} );\n\t}\n} );\n\n// Create scrollLeft and scrollTop methods\njQuery.each( { scrollLeft: \"pageXOffset\", scrollTop: \"pageYOffset\" }, function( method, prop ) {\n\tvar top = \"pageYOffset\" === prop;\n\n\tjQuery.fn[ method ] = function( val ) {\n\t\treturn access( this, function( elem, method, val ) {\n\n\t\t\t// Coalesce documents and windows\n\t\t\tvar win;\n\t\t\tif ( isWindow( elem ) ) {\n\t\t\t\twin = elem;\n\t\t\t} else if ( elem.nodeType === 9 ) {\n\t\t\t\twin = elem.defaultView;\n\t\t\t}\n\n\t\t\tif ( val === undefined ) {\n\t\t\t\treturn win ? win[ prop ] : elem[ method ];\n\t\t\t}\n\n\t\t\tif ( win ) {\n\t\t\t\twin.scrollTo(\n\t\t\t\t\t!top ? val : win.pageXOffset,\n\t\t\t\t\ttop ? val : win.pageYOffset\n\t\t\t\t);\n\n\t\t\t} else {\n\t\t\t\telem[ method ] = val;\n\t\t\t}\n\t\t}, method, val, arguments.length );\n\t};\n} );\n\n// Support: Safari <=7 - 9.1, Chrome <=37 - 49\n// Add the top/left cssHooks using jQuery.fn.position\n// Webkit bug: https://bugs.webkit.org/show_bug.cgi?id=29084\n// Blink bug: https://bugs.chromium.org/p/chromium/issues/detail?id=589347\n// getComputedStyle returns percent when specified for top/left/bottom/right;\n// rather than make the css module depend on the offset module, just check for it here\njQuery.each( [ \"top\", \"left\" ], function( _i, prop ) {\n\tjQuery.cssHooks[ prop ] = addGetHookIf( support.pixelPosition,\n\t\tfunction( elem, computed ) {\n\t\t\tif ( computed ) {\n\t\t\t\tcomputed = curCSS( elem, prop );\n\n\t\t\t\t// If curCSS returns percentage, fallback to offset\n\t\t\t\treturn rnumnonpx.test( computed ) ?\n\t\t\t\t\tjQuery( elem ).position()[ prop ] + \"px\" :\n\t\t\t\t\tcomputed;\n\t\t\t}\n\t\t}\n\t);\n} );\n\n\n// Create innerHeight, innerWidth, height, width, outerHeight and outerWidth methods\njQuery.each( { Height: \"height\", Width: \"width\" }, function( name, type ) {\n\tjQuery.each( {\n\t\tpadding: \"inner\" + name,\n\t\tcontent: type,\n\t\t\"\": \"outer\" + name\n\t}, function( defaultExtra, funcName ) {\n\n\t\t// Margin is only for outerHeight, outerWidth\n\t\tjQuery.fn[ funcName ] = function( margin, value ) {\n\t\t\tvar chainable = arguments.length && ( defaultExtra || typeof margin !== \"boolean\" ),\n\t\t\t\textra = defaultExtra || ( margin === true || value === true ? \"margin\" : \"border\" );\n\n\t\t\treturn access( this, function( elem, type, value ) {\n\t\t\t\tvar doc;\n\n\t\t\t\tif ( isWindow( elem ) ) {\n\n\t\t\t\t\t// $( window ).outerWidth/Height return w/h including scrollbars (gh-1729)\n\t\t\t\t\treturn funcName.indexOf( \"outer\" ) === 0 ?\n\t\t\t\t\t\telem[ \"inner\" + name ] :\n\t\t\t\t\t\telem.document.documentElement[ \"client\" + name ];\n\t\t\t\t}\n\n\t\t\t\t// Get document width or height\n\t\t\t\tif ( elem.nodeType === 9 ) {\n\t\t\t\t\tdoc = elem.documentElement;\n\n\t\t\t\t\t// Either scroll[Width/Height] or offset[Width/Height] or client[Width/Height],\n\t\t\t\t\t// whichever is greatest\n\t\t\t\t\treturn Math.max(\n\t\t\t\t\t\telem.body[ \"scroll\" + name ], doc[ \"scroll\" + name ],\n\t\t\t\t\t\telem.body[ \"offset\" + name ], doc[ \"offset\" + name ],\n\t\t\t\t\t\tdoc[ \"client\" + name ]\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\treturn value === undefined ?\n\n\t\t\t\t\t// Get width or height on the element, requesting but not forcing parseFloat\n\t\t\t\t\tjQuery.css( elem, type, extra ) :\n\n\t\t\t\t\t// Set width or height on the element\n\t\t\t\t\tjQuery.style( elem, type, value, extra );\n\t\t\t}, type, chainable ? margin : undefined, chainable );\n\t\t};\n\t} );\n} );\n\n\njQuery.each( [\n\t\"ajaxStart\",\n\t\"ajaxStop\",\n\t\"ajaxComplete\",\n\t\"ajaxError\",\n\t\"ajaxSuccess\",\n\t\"ajaxSend\"\n], function( _i, type ) {\n\tjQuery.fn[ type ] = function( fn ) {\n\t\treturn this.on( type, fn );\n\t};\n} );\n\n\n\n\njQuery.fn.extend( {\n\n\tbind: function( types, data, fn ) {\n\t\treturn this.on( types, null, data, fn );\n\t},\n\tunbind: function( types, fn ) {\n\t\treturn this.off( types, null, fn );\n\t},\n\n\tdelegate: function( selector, types, data, fn ) {\n\t\treturn this.on( types, selector, data, fn );\n\t},\n\tundelegate: function( selector, types, fn ) {\n\n\t\t// ( namespace ) or ( selector, types [, fn] )\n\t\treturn arguments.length === 1 ?\n\t\t\tthis.off( selector, \"**\" ) :\n\t\t\tthis.off( types, selector || \"**\", fn );\n\t},\n\n\thover: function( fnOver, fnOut ) {\n\t\treturn this\n\t\t\t.on( \"mouseenter\", fnOver )\n\t\t\t.on( \"mouseleave\", fnOut || fnOver );\n\t}\n} );\n\njQuery.each(\n\t( \"blur focus focusin focusout resize scroll click dblclick \" +\n\t\"mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave \" +\n\t\"change select submit keydown keypress keyup contextmenu\" ).split( \" \" ),\n\tfunction( _i, name ) {\n\n\t\t// Handle event binding\n\t\tjQuery.fn[ name ] = function( data, fn ) {\n\t\t\treturn arguments.length > 0 ?\n\t\t\t\tthis.on( name, null, data, fn ) :\n\t\t\t\tthis.trigger( name );\n\t\t};\n\t}\n);\n\n\n\n\n// Support: Android <=4.0 only\n// Make sure we trim BOM and NBSP\n// Require that the \"whitespace run\" starts from a non-whitespace\n// to avoid O(N^2) behavior when the engine would try matching \"\\s+$\" at each space position.\nvar rtrim = /^[\\s\\uFEFF\\xA0]+|([^\\s\\uFEFF\\xA0])[\\s\\uFEFF\\xA0]+$/g;\n\n// Bind a function to a context, optionally partially applying any\n// arguments.\n// jQuery.proxy is deprecated to promote standards (specifically Function#bind)\n// However, it is not slated for removal any time soon\njQuery.proxy = function( fn, context ) {\n\tvar tmp, args, proxy;\n\n\tif ( typeof context === \"string\" ) {\n\t\ttmp = fn[ context ];\n\t\tcontext = fn;\n\t\tfn = tmp;\n\t}\n\n\t// Quick check to determine if target is callable, in the spec\n\t// this throws a TypeError, but we will just return undefined.\n\tif ( !isFunction( fn ) ) {\n\t\treturn undefined;\n\t}\n\n\t// Simulated bind\n\targs = slice.call( arguments, 2 );\n\tproxy = function() {\n\t\treturn fn.apply( context || this, args.concat( slice.call( arguments ) ) );\n\t};\n\n\t// Set the guid of unique handler to the same of original handler, so it can be removed\n\tproxy.guid = fn.guid = fn.guid || jQuery.guid++;\n\n\treturn proxy;\n};\n\njQuery.holdReady = function( hold ) {\n\tif ( hold ) {\n\t\tjQuery.readyWait++;\n\t} else {\n\t\tjQuery.ready( true );\n\t}\n};\njQuery.isArray = Array.isArray;\njQuery.parseJSON = JSON.parse;\njQuery.nodeName = nodeName;\njQuery.isFunction = isFunction;\njQuery.isWindow = isWindow;\njQuery.camelCase = camelCase;\njQuery.type = toType;\n\njQuery.now = Date.now;\n\njQuery.isNumeric = function( obj ) {\n\n\t// As of jQuery 3.0, isNumeric is limited to\n\t// strings and numbers (primitives or objects)\n\t// that can be coerced to finite numbers (gh-2662)\n\tvar type = jQuery.type( obj );\n\treturn ( type === \"number\" || type === \"string\" ) &&\n\n\t\t// parseFloat NaNs numeric-cast false positives (\"\")\n\t\t// ...but misinterprets leading-number strings, particularly hex literals (\"0x...\")\n\t\t// subtraction forces infinities to NaN\n\t\t!isNaN( obj - parseFloat( obj ) );\n};\n\njQuery.trim = function( text ) {\n\treturn text == null ?\n\t\t\"\" :\n\t\t( text + \"\" ).replace( rtrim, \"$1\" );\n};\n\n\n\n// Register as a named AMD module, since jQuery can be concatenated with other\n// files that may use define, but not via a proper concatenation script that\n// understands anonymous AMD modules. A named AMD is safest and most robust\n// way to register. Lowercase jquery is used because AMD module names are\n// derived from file names, and jQuery is normally delivered in a lowercase\n// file name. Do this after creating the global so that if an AMD module wants\n// to call noConflict to hide this version of jQuery, it will work.\n\n// Note that for maximum portability, libraries that are not jQuery should\n// declare themselves as anonymous modules, and avoid setting a global if an\n// AMD loader is present. jQuery is a special case. For more information, see\n// https://github.com/jrburke/requirejs/wiki/Updating-existing-libraries#wiki-anon\n\nif ( typeof define === \"function\" && define.amd ) {\n\tdefine( \"jquery\", [], function() {\n\t\treturn jQuery;\n\t} );\n}\n\n\n\n\nvar\n\n\t// Map over jQuery in case of overwrite\n\t_jQuery = window.jQuery,\n\n\t// Map over the $ in case of overwrite\n\t_$ = window.$;\n\njQuery.noConflict = function( deep ) {\n\tif ( window.$ === jQuery ) {\n\t\twindow.$ = _$;\n\t}\n\n\tif ( deep && window.jQuery === jQuery ) {\n\t\twindow.jQuery = _jQuery;\n\t}\n\n\treturn jQuery;\n};\n\n// Expose jQuery and $ identifiers, even in AMD\n// (trac-7102#comment:10, https://github.com/jquery/jquery/pull/557)\n// and CommonJS for browser emulators (trac-13566)\nif ( typeof noGlobal === \"undefined\" ) {\n\twindow.jQuery = window.$ = jQuery;\n}\n\n\n\n\nreturn jQuery;\n} );\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,KAAE,SAAU,QAAQ,SAAU;AAE7B;AAEA,UAAK,OAAO,WAAW,YAAY,OAAO,OAAO,YAAY,UAAW;AASvE,eAAO,UAAU,OAAO,WACvB,QAAS,QAAQ,IAAK,IACtB,SAAU,GAAI;AACb,cAAK,CAAC,EAAE,UAAW;AAClB,kBAAM,IAAI,MAAO,0CAA2C;AAAA,UAC7D;AACA,iBAAO,QAAS,CAAE;AAAA,QACnB;AAAA,MACF,OAAO;AACN,gBAAS,MAAO;AAAA,MACjB;AAAA,IAGD,GAAK,OAAO,WAAW,cAAc,SAAS,SAAM,SAAUA,SAAQ,UAAW;AAMjF;AAEA,UAAI,MAAM,CAAC;AAEX,UAAI,WAAW,OAAO;AAEtB,UAAI,QAAQ,IAAI;AAEhB,UAAI,OAAO,IAAI,OAAO,SAAU,OAAQ;AACvC,eAAO,IAAI,KAAK,KAAM,KAAM;AAAA,MAC7B,IAAI,SAAU,OAAQ;AACrB,eAAO,IAAI,OAAO,MAAO,CAAC,GAAG,KAAM;AAAA,MACpC;AAGA,UAAI,OAAO,IAAI;AAEf,UAAI,UAAU,IAAI;AAElB,UAAI,aAAa,CAAC;AAElB,UAAI,WAAW,WAAW;AAE1B,UAAI,SAAS,WAAW;AAExB,UAAI,aAAa,OAAO;AAExB,UAAI,uBAAuB,WAAW,KAAM,MAAO;AAEnD,UAAI,UAAU,CAAC;AAEf,UAAI,aAAa,SAASC,YAAY,KAAM;AAS1C,eAAO,OAAO,QAAQ,cAAc,OAAO,IAAI,aAAa,YAC3D,OAAO,IAAI,SAAS;AAAA,MACtB;AAGD,UAAI,WAAW,SAASC,UAAU,KAAM;AACtC,eAAO,OAAO,QAAQ,QAAQ,IAAI;AAAA,MACnC;AAGD,UAAI,WAAWF,QAAO;AAIrB,UAAI,4BAA4B;AAAA,QAC/B,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,QACP,UAAU;AAAA,MACX;AAEA,eAAS,QAAS,MAAM,MAAM,KAAM;AACnC,cAAM,OAAO;AAEb,YAAI,GAAG,KACN,SAAS,IAAI,cAAe,QAAS;AAEtC,eAAO,OAAO;AACd,YAAK,MAAO;AACX,eAAM,KAAK,2BAA4B;AAYtC,kBAAM,KAAM,CAAE,KAAK,KAAK,gBAAgB,KAAK,aAAc,CAAE;AAC7D,gBAAK,KAAM;AACV,qBAAO,aAAc,GAAG,GAAI;AAAA,YAC7B;AAAA,UACD;AAAA,QACD;AACA,YAAI,KAAK,YAAa,MAAO,EAAE,WAAW,YAAa,MAAO;AAAA,MAC/D;AAGD,eAAS,OAAQ,KAAM;AACtB,YAAK,OAAO,MAAO;AAClB,iBAAO,MAAM;AAAA,QACd;AAGA,eAAO,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAChD,WAAY,SAAS,KAAM,GAAI,CAAE,KAAK,WACtC,OAAO;AAAA,MACT;AAOA,UAAI,UAAU,SAEb,cAAc,UAGd,SAAS,SAAU,UAAU,SAAU;AAItC,eAAO,IAAI,OAAO,GAAG,KAAM,UAAU,OAAQ;AAAA,MAC9C;AAED,aAAO,KAAK,OAAO,YAAY;AAAA;AAAA,QAG9B,QAAQ;AAAA,QAER,aAAa;AAAA;AAAA,QAGb,QAAQ;AAAA,QAER,SAAS,WAAW;AACnB,iBAAO,MAAM,KAAM,IAAK;AAAA,QACzB;AAAA;AAAA;AAAA,QAIA,KAAK,SAAU,KAAM;AAGpB,cAAK,OAAO,MAAO;AAClB,mBAAO,MAAM,KAAM,IAAK;AAAA,UACzB;AAGA,iBAAO,MAAM,IAAI,KAAM,MAAM,KAAK,MAAO,IAAI,KAAM,GAAI;AAAA,QACxD;AAAA;AAAA;AAAA,QAIA,WAAW,SAAU,OAAQ;AAG5B,cAAI,MAAM,OAAO,MAAO,KAAK,YAAY,GAAG,KAAM;AAGlD,cAAI,aAAa;AAGjB,iBAAO;AAAA,QACR;AAAA;AAAA,QAGA,MAAM,SAAU,UAAW;AAC1B,iBAAO,OAAO,KAAM,MAAM,QAAS;AAAA,QACpC;AAAA,QAEA,KAAK,SAAU,UAAW;AACzB,iBAAO,KAAK,UAAW,OAAO,IAAK,MAAM,SAAU,MAAM,GAAI;AAC5D,mBAAO,SAAS,KAAM,MAAM,GAAG,IAAK;AAAA,UACrC,CAAE,CAAE;AAAA,QACL;AAAA,QAEA,OAAO,WAAW;AACjB,iBAAO,KAAK,UAAW,MAAM,MAAO,MAAM,SAAU,CAAE;AAAA,QACvD;AAAA,QAEA,OAAO,WAAW;AACjB,iBAAO,KAAK,GAAI,CAAE;AAAA,QACnB;AAAA,QAEA,MAAM,WAAW;AAChB,iBAAO,KAAK,GAAI,EAAG;AAAA,QACpB;AAAA,QAEA,MAAM,WAAW;AAChB,iBAAO,KAAK,UAAW,OAAO,KAAM,MAAM,SAAU,OAAO,GAAI;AAC9D,oBAAS,IAAI,KAAM;AAAA,UACpB,CAAE,CAAE;AAAA,QACL;AAAA,QAEA,KAAK,WAAW;AACf,iBAAO,KAAK,UAAW,OAAO,KAAM,MAAM,SAAU,OAAO,GAAI;AAC9D,mBAAO,IAAI;AAAA,UACZ,CAAE,CAAE;AAAA,QACL;AAAA,QAEA,IAAI,SAAU,GAAI;AACjB,cAAI,MAAM,KAAK,QACd,IAAI,CAAC,KAAM,IAAI,IAAI,MAAM;AAC1B,iBAAO,KAAK,UAAW,KAAK,KAAK,IAAI,MAAM,CAAE,KAAM,CAAE,CAAE,IAAI,CAAC,CAAE;AAAA,QAC/D;AAAA,QAEA,KAAK,WAAW;AACf,iBAAO,KAAK,cAAc,KAAK,YAAY;AAAA,QAC5C;AAAA;AAAA;AAAA,QAIA;AAAA,QACA,MAAM,IAAI;AAAA,QACV,QAAQ,IAAI;AAAA,MACb;AAEA,aAAO,SAAS,OAAO,GAAG,SAAS,WAAW;AAC7C,YAAI,SAAS,MAAM,KAAK,MAAM,aAAa,OAC1C,SAAS,UAAW,CAAE,KAAK,CAAC,GAC5B,IAAI,GACJ,SAAS,UAAU,QACnB,OAAO;AAGR,YAAK,OAAO,WAAW,WAAY;AAClC,iBAAO;AAGP,mBAAS,UAAW,CAAE,KAAK,CAAC;AAC5B;AAAA,QACD;AAGA,YAAK,OAAO,WAAW,YAAY,CAAC,WAAY,MAAO,GAAI;AAC1D,mBAAS,CAAC;AAAA,QACX;AAGA,YAAK,MAAM,QAAS;AACnB,mBAAS;AACT;AAAA,QACD;AAEA,eAAQ,IAAI,QAAQ,KAAM;AAGzB,eAAO,UAAU,UAAW,CAAE,MAAO,MAAO;AAG3C,iBAAM,QAAQ,SAAU;AACvB,qBAAO,QAAS,IAAK;AAIrB,kBAAK,SAAS,eAAe,WAAW,MAAO;AAC9C;AAAA,cACD;AAGA,kBAAK,QAAQ,SAAU,OAAO,cAAe,IAAK,MAC/C,cAAc,MAAM,QAAS,IAAK,KAAQ;AAC5C,sBAAM,OAAQ,IAAK;AAGnB,oBAAK,eAAe,CAAC,MAAM,QAAS,GAAI,GAAI;AAC3C,0BAAQ,CAAC;AAAA,gBACV,WAAY,CAAC,eAAe,CAAC,OAAO,cAAe,GAAI,GAAI;AAC1D,0BAAQ,CAAC;AAAA,gBACV,OAAO;AACN,0BAAQ;AAAA,gBACT;AACA,8BAAc;AAGd,uBAAQ,IAAK,IAAI,OAAO,OAAQ,MAAM,OAAO,IAAK;AAAA,cAGnD,WAAY,SAAS,QAAY;AAChC,uBAAQ,IAAK,IAAI;AAAA,cAClB;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAGA,eAAO;AAAA,MACR;AAEA,aAAO,OAAQ;AAAA;AAAA,QAGd,SAAS,YAAa,UAAU,KAAK,OAAO,GAAI,QAAS,OAAO,EAAG;AAAA;AAAA,QAGnE,SAAS;AAAA,QAET,OAAO,SAAU,KAAM;AACtB,gBAAM,IAAI,MAAO,GAAI;AAAA,QACtB;AAAA,QAEA,MAAM,WAAW;AAAA,QAAC;AAAA,QAElB,eAAe,SAAU,KAAM;AAC9B,cAAI,OAAO;AAIX,cAAK,CAAC,OAAO,SAAS,KAAM,GAAI,MAAM,mBAAoB;AACzD,mBAAO;AAAA,UACR;AAEA,kBAAQ,SAAU,GAAI;AAGtB,cAAK,CAAC,OAAQ;AACb,mBAAO;AAAA,UACR;AAGA,iBAAO,OAAO,KAAM,OAAO,aAAc,KAAK,MAAM;AACpD,iBAAO,OAAO,SAAS,cAAc,WAAW,KAAM,IAAK,MAAM;AAAA,QAClE;AAAA,QAEA,eAAe,SAAU,KAAM;AAC9B,cAAI;AAEJ,eAAM,QAAQ,KAAM;AACnB,mBAAO;AAAA,UACR;AACA,iBAAO;AAAA,QACR;AAAA;AAAA;AAAA,QAIA,YAAY,SAAU,MAAM,SAAS,KAAM;AAC1C,kBAAS,MAAM,EAAE,OAAO,WAAW,QAAQ,MAAM,GAAG,GAAI;AAAA,QACzD;AAAA,QAEA,MAAM,SAAU,KAAK,UAAW;AAC/B,cAAI,QAAQ,IAAI;AAEhB,cAAK,YAAa,GAAI,GAAI;AACzB,qBAAS,IAAI;AACb,mBAAQ,IAAI,QAAQ,KAAM;AACzB,kBAAK,SAAS,KAAM,IAAK,CAAE,GAAG,GAAG,IAAK,CAAE,CAAE,MAAM,OAAQ;AACvD;AAAA,cACD;AAAA,YACD;AAAA,UACD,OAAO;AACN,iBAAM,KAAK,KAAM;AAChB,kBAAK,SAAS,KAAM,IAAK,CAAE,GAAG,GAAG,IAAK,CAAE,CAAE,MAAM,OAAQ;AACvD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA;AAAA,QAIA,MAAM,SAAU,MAAO;AACtB,cAAI,MACH,MAAM,IACN,IAAI,GACJ,WAAW,KAAK;AAEjB,cAAK,CAAC,UAAW;AAGhB,mBAAU,OAAO,KAAM,GAAI,GAAM;AAGhC,qBAAO,OAAO,KAAM,IAAK;AAAA,YAC1B;AAAA,UACD;AACA,cAAK,aAAa,KAAK,aAAa,IAAK;AACxC,mBAAO,KAAK;AAAA,UACb;AACA,cAAK,aAAa,GAAI;AACrB,mBAAO,KAAK,gBAAgB;AAAA,UAC7B;AACA,cAAK,aAAa,KAAK,aAAa,GAAI;AACvC,mBAAO,KAAK;AAAA,UACb;AAIA,iBAAO;AAAA,QACR;AAAA;AAAA,QAGA,WAAW,SAAUG,MAAK,SAAU;AACnC,cAAI,MAAM,WAAW,CAAC;AAEtB,cAAKA,QAAO,MAAO;AAClB,gBAAK,YAAa,OAAQA,IAAI,CAAE,GAAI;AACnC,qBAAO;AAAA,gBAAO;AAAA,gBACb,OAAOA,SAAQ,WACd,CAAEA,IAAI,IAAIA;AAAA,cACZ;AAAA,YACD,OAAO;AACN,mBAAK,KAAM,KAAKA,IAAI;AAAA,YACrB;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,QAEA,SAAS,SAAU,MAAMA,MAAK,GAAI;AACjC,iBAAOA,QAAO,OAAO,KAAK,QAAQ,KAAMA,MAAK,MAAM,CAAE;AAAA,QACtD;AAAA,QAEA,UAAU,SAAU,MAAO;AAC1B,cAAI,YAAY,QAAQ,KAAK,cAC5B,UAAU,SAAU,KAAK,iBAAiB,MAAO;AAIlD,iBAAO,CAAC,YAAY,KAAM,aAAa,WAAW,QAAQ,YAAY,MAAO;AAAA,QAC9E;AAAA;AAAA;AAAA,QAIA,OAAO,SAAU,OAAO,QAAS;AAChC,cAAI,MAAM,CAAC,OAAO,QACjB,IAAI,GACJ,IAAI,MAAM;AAEX,iBAAQ,IAAI,KAAK,KAAM;AACtB,kBAAO,GAAI,IAAI,OAAQ,CAAE;AAAA,UAC1B;AAEA,gBAAM,SAAS;AAEf,iBAAO;AAAA,QACR;AAAA,QAEA,MAAM,SAAU,OAAO,UAAU,QAAS;AACzC,cAAI,iBACH,UAAU,CAAC,GACX,IAAI,GACJ,SAAS,MAAM,QACf,iBAAiB,CAAC;AAInB,iBAAQ,IAAI,QAAQ,KAAM;AACzB,8BAAkB,CAAC,SAAU,MAAO,CAAE,GAAG,CAAE;AAC3C,gBAAK,oBAAoB,gBAAiB;AACzC,sBAAQ,KAAM,MAAO,CAAE,CAAE;AAAA,YAC1B;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA;AAAA,QAGA,KAAK,SAAU,OAAO,UAAU,KAAM;AACrC,cAAI,QAAQ,OACX,IAAI,GACJ,MAAM,CAAC;AAGR,cAAK,YAAa,KAAM,GAAI;AAC3B,qBAAS,MAAM;AACf,mBAAQ,IAAI,QAAQ,KAAM;AACzB,sBAAQ,SAAU,MAAO,CAAE,GAAG,GAAG,GAAI;AAErC,kBAAK,SAAS,MAAO;AACpB,oBAAI,KAAM,KAAM;AAAA,cACjB;AAAA,YACD;AAAA,UAGD,OAAO;AACN,iBAAM,KAAK,OAAQ;AAClB,sBAAQ,SAAU,MAAO,CAAE,GAAG,GAAG,GAAI;AAErC,kBAAK,SAAS,MAAO;AACpB,oBAAI,KAAM,KAAM;AAAA,cACjB;AAAA,YACD;AAAA,UACD;AAGA,iBAAO,KAAM,GAAI;AAAA,QAClB;AAAA;AAAA,QAGA,MAAM;AAAA;AAAA;AAAA,QAIN;AAAA,MACD,CAAE;AAEF,UAAK,OAAO,WAAW,YAAa;AACnC,eAAO,GAAI,OAAO,QAAS,IAAI,IAAK,OAAO,QAAS;AAAA,MACrD;AAGA,aAAO;AAAA,QAAM,uEAAuE,MAAO,GAAI;AAAA,QAC9F,SAAU,IAAI,MAAO;AACpB,qBAAY,aAAa,OAAO,GAAI,IAAI,KAAK,YAAY;AAAA,QAC1D;AAAA,MAAE;AAEH,eAAS,YAAa,KAAM;AAM3B,YAAI,SAAS,CAAC,CAAC,OAAO,YAAY,OAAO,IAAI,QAC5C,OAAO,OAAQ,GAAI;AAEpB,YAAK,WAAY,GAAI,KAAK,SAAU,GAAI,GAAI;AAC3C,iBAAO;AAAA,QACR;AAEA,eAAO,SAAS,WAAW,WAAW,KACrC,OAAO,WAAW,YAAY,SAAS,KAAO,SAAS,KAAO;AAAA,MAChE;AAGA,eAAS,SAAU,MAAM,MAAO;AAE/B,eAAO,KAAK,YAAY,KAAK,SAAS,YAAY,MAAM,KAAK,YAAY;AAAA,MAE1E;AACA,UAAI,MAAM,IAAI;AAGd,UAAI,OAAO,IAAI;AAGf,UAAI,SAAS,IAAI;AAGjB,UAAI,aAAa;AAGjB,UAAI,WAAW,IAAI;AAAA,QAClB,MAAM,aAAa,gCAAgC,aAAa;AAAA,QAChE;AAAA,MACD;AAMA,aAAO,WAAW,SAAU,GAAG,GAAI;AAClC,YAAI,MAAM,KAAK,EAAE;AAEjB,eAAO,MAAM,OAAO,CAAC,EAAG,OAAO,IAAI,aAAa;AAAA;AAAA,SAI/C,EAAE,WACD,EAAE,SAAU,GAAI,IAChB,EAAE,2BAA2B,EAAE,wBAAyB,GAAI,IAAI;AAAA,MAEnE;AAOA,UAAI,aAAa;AAEjB,eAAS,WAAY,IAAI,aAAc;AACtC,YAAK,aAAc;AAGlB,cAAK,OAAO,MAAO;AAClB,mBAAO;AAAA,UACR;AAGA,iBAAO,GAAG,MAAO,GAAG,EAAG,IAAI,OAAO,GAAG,WAAY,GAAG,SAAS,CAAE,EAAE,SAAU,EAAG,IAAI;AAAA,QACnF;AAGA,eAAO,OAAO;AAAA,MACf;AAEA,aAAO,iBAAiB,SAAU,KAAM;AACvC,gBAAS,MAAM,IAAK,QAAS,YAAY,UAAW;AAAA,MACrD;AAKA,UAAI,eAAe,UAClB,aAAa;AAEd,OAAE,WAAW;AAEb,YAAI,GACH,MACA,kBACA,WACA,cACAC,QAAO,YAGPC,WACAC,kBACA,gBACA,WACA,SAGA,UAAU,OAAO,SACjB,UAAU,GACV,OAAO,GACP,aAAa,YAAY,GACzB,aAAa,YAAY,GACzB,gBAAgB,YAAY,GAC5B,yBAAyB,YAAY,GACrC,YAAY,SAAU,GAAG,GAAI;AAC5B,cAAK,MAAM,GAAI;AACd,2BAAe;AAAA,UAChB;AACA,iBAAO;AAAA,QACR,GAEA,WAAW,8HAMX,aAAa,4BAA4B,aACxC,2CAGD,aAAa,QAAQ,aAAa,OAAO,aAAa,SAAS;AAAA,QAG9D,kBAAkB;AAAA,QAGlB,0DAA6D,aAAa,SAC1E,aAAa,QAEd,UAAU,OAAO,aAAa,uFAOA,aAAa,gBAO3C,cAAc,IAAI,OAAQ,aAAa,KAAK,GAAI,GAEhD,SAAS,IAAI,OAAQ,MAAM,aAAa,OAAO,aAAa,GAAI,GAChE,qBAAqB,IAAI,OAAQ,MAAM,aAAa,aAAa,aAAa,MAC7E,aAAa,GAAI,GAClB,WAAW,IAAI,OAAQ,aAAa,IAAK,GAEzC,UAAU,IAAI,OAAQ,OAAQ,GAC9B,cAAc,IAAI,OAAQ,MAAM,aAAa,GAAI,GAEjD,YAAY;AAAA,UACX,IAAI,IAAI,OAAQ,QAAQ,aAAa,GAAI;AAAA,UACzC,OAAO,IAAI,OAAQ,UAAU,aAAa,GAAI;AAAA,UAC9C,KAAK,IAAI,OAAQ,OAAO,aAAa,OAAQ;AAAA,UAC7C,MAAM,IAAI,OAAQ,MAAM,UAAW;AAAA,UACnC,QAAQ,IAAI,OAAQ,MAAM,OAAQ;AAAA,UAClC,OAAO,IAAI;AAAA,YACV,2DACC,aAAa,iCAAiC,aAAa,gBAC3D,aAAa,eAAe,aAAa;AAAA,YAAU;AAAA,UAAI;AAAA,UACzD,MAAM,IAAI,OAAQ,SAAS,WAAW,MAAM,GAAI;AAAA;AAAA;AAAA,UAIhD,cAAc,IAAI,OAAQ,MAAM,aAC/B,qDAAqD,aACrD,qBAAqB,aAAa,oBAAoB,GAAI;AAAA,QAC5D,GAEA,UAAU,uCACV,UAAU,UAGVC,cAAa,oCAEb,WAAW,QAIX,YAAY,IAAI,OAAQ,yBAAyB,aAChD,wBAAwB,GAAI,GAC7B,YAAY,SAAU,QAAQ,QAAS;AACtC,cAAI,OAAO,OAAO,OAAO,MAAO,CAAE,IAAI;AAEtC,cAAK,QAAS;AAGb,mBAAO;AAAA,UACR;AAMA,iBAAO,OAAO,IACb,OAAO,aAAc,OAAO,KAAQ,IACpC,OAAO,aAAc,QAAQ,KAAK,OAAQ,OAAO,OAAQ,KAAO;AAAA,QAClE,GAMA,gBAAgB,WAAW;AAC1B,sBAAY;AAAA,QACb,GAEA,qBAAqB;AAAA,UACpB,SAAU,MAAO;AAChB,mBAAO,KAAK,aAAa,QAAQ,SAAU,MAAM,UAAW;AAAA,UAC7D;AAAA,UACA,EAAE,KAAK,cAAc,MAAM,SAAS;AAAA,QACrC;AAKD,iBAAS,oBAAoB;AAC5B,cAAI;AACH,mBAAOF,UAAS;AAAA,UACjB,SAAU,KAAM;AAAA,UAAE;AAAA,QACnB;AAGA,YAAI;AACH,UAAAD,MAAK;AAAA,YACF,MAAM,MAAM,KAAM,aAAa,UAAW;AAAA,YAC5C,aAAa;AAAA,UACd;AAKA,cAAK,aAAa,WAAW,MAAO,EAAE;AAAA,QACvC,SAAU,GAAI;AACb,UAAAA,QAAO;AAAA,YACN,OAAO,SAAU,QAAQ,KAAM;AAC9B,yBAAW,MAAO,QAAQ,MAAM,KAAM,GAAI,CAAE;AAAA,YAC7C;AAAA,YACA,MAAM,SAAU,QAAS;AACxB,yBAAW,MAAO,QAAQ,MAAM,KAAM,WAAW,CAAE,CAAE;AAAA,YACtD;AAAA,UACD;AAAA,QACD;AAEA,iBAAS,KAAM,UAAU,SAAS,SAAS,MAAO;AACjD,cAAI,GAAGI,IAAG,MAAM,KAAK,OAAO,QAAQ,aACnC,aAAa,WAAW,QAAQ,eAGhC,WAAW,UAAU,QAAQ,WAAW;AAEzC,oBAAU,WAAW,CAAC;AAGtB,cAAK,OAAO,aAAa,YAAY,CAAC,YACrC,aAAa,KAAK,aAAa,KAAK,aAAa,IAAK;AAEtD,mBAAO;AAAA,UACR;AAGA,cAAK,CAAC,MAAO;AACZ,wBAAa,OAAQ;AACrB,sBAAU,WAAWH;AAErB,gBAAK,gBAAiB;AAIrB,kBAAK,aAAa,OAAQ,QAAQE,YAAW,KAAM,QAAS,IAAM;AAGjE,oBAAO,IAAI,MAAO,CAAE,GAAM;AAGzB,sBAAK,aAAa,GAAI;AACrB,wBAAO,OAAO,QAAQ,eAAgB,CAAE,GAAM;AAI7C,0BAAK,KAAK,OAAO,GAAI;AACpB,wBAAAH,MAAK,KAAM,SAAS,IAAK;AACzB,+BAAO;AAAA,sBACR;AAAA,oBACD,OAAO;AACN,6BAAO;AAAA,oBACR;AAAA,kBAGD,OAAO;AAIN,wBAAK,eAAgB,OAAO,WAAW,eAAgB,CAAE,MACxD,KAAK,SAAU,SAAS,IAAK,KAC7B,KAAK,OAAO,GAAI;AAEhB,sBAAAA,MAAK,KAAM,SAAS,IAAK;AACzB,6BAAO;AAAA,oBACR;AAAA,kBACD;AAAA,gBAGD,WAAY,MAAO,CAAE,GAAI;AACxB,kBAAAA,MAAK,MAAO,SAAS,QAAQ,qBAAsB,QAAS,CAAE;AAC9D,yBAAO;AAAA,gBAGR,YAAc,IAAI,MAAO,CAAE,MAAO,QAAQ,wBAAyB;AAClE,kBAAAA,MAAK,MAAO,SAAS,QAAQ,uBAAwB,CAAE,CAAE;AACzD,yBAAO;AAAA,gBACR;AAAA,cACD;AAGA,kBAAK,CAAC,uBAAwB,WAAW,GAAI,MAC1C,CAAC,aAAa,CAAC,UAAU,KAAM,QAAS,IAAM;AAEhD,8BAAc;AACd,6BAAa;AASb,oBAAK,aAAa,MACf,SAAS,KAAM,QAAS,KAAK,mBAAmB,KAAM,QAAS,IAAM;AAGvE,+BAAa,SAAS,KAAM,QAAS,KAAK,YAAa,QAAQ,UAAW,KACzE;AAQD,sBAAK,cAAc,WAAW,CAAC,QAAQ,OAAQ;AAG9C,wBAAO,MAAM,QAAQ,aAAc,IAAK,GAAM;AAC7C,4BAAM,OAAO,eAAgB,GAAI;AAAA,oBAClC,OAAO;AACN,8BAAQ,aAAc,MAAQ,MAAM,OAAU;AAAA,oBAC/C;AAAA,kBACD;AAGA,2BAAS,SAAU,QAAS;AAC5B,kBAAAI,KAAI,OAAO;AACX,yBAAQA,MAAM;AACb,2BAAQA,EAAE,KAAM,MAAM,MAAM,MAAM,YAAa,MAC9C,WAAY,OAAQA,EAAE,CAAE;AAAA,kBAC1B;AACA,gCAAc,OAAO,KAAM,GAAI;AAAA,gBAChC;AAEA,oBAAI;AACH,kBAAAJ,MAAK;AAAA,oBAAO;AAAA,oBACX,WAAW,iBAAkB,WAAY;AAAA,kBAC1C;AACA,yBAAO;AAAA,gBACR,SAAU,UAAW;AACpB,yCAAwB,UAAU,IAAK;AAAA,gBACxC,UAAE;AACD,sBAAK,QAAQ,SAAU;AACtB,4BAAQ,gBAAiB,IAAK;AAAA,kBAC/B;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAGA,iBAAO,OAAQ,SAAS,QAAS,UAAU,IAAK,GAAG,SAAS,SAAS,IAAK;AAAA,QAC3E;AAQA,iBAAS,cAAc;AACtB,cAAI,OAAO,CAAC;AAEZ,mBAAS,MAAO,KAAK,OAAQ;AAI5B,gBAAK,KAAK,KAAM,MAAM,GAAI,IAAI,KAAK,aAAc;AAGhD,qBAAO,MAAO,KAAK,MAAM,CAAE;AAAA,YAC5B;AACA,mBAAS,MAAO,MAAM,GAAI,IAAI;AAAA,UAC/B;AACA,iBAAO;AAAA,QACR;AAMA,iBAAS,aAAc,IAAK;AAC3B,aAAI,OAAQ,IAAI;AAChB,iBAAO;AAAA,QACR;AAMA,iBAAS,OAAQ,IAAK;AACrB,cAAI,KAAKC,UAAS,cAAe,UAAW;AAE5C,cAAI;AACH,mBAAO,CAAC,CAAC,GAAI,EAAG;AAAA,UACjB,SAAU,GAAI;AACb,mBAAO;AAAA,UACR,UAAE;AAGD,gBAAK,GAAG,YAAa;AACpB,iBAAG,WAAW,YAAa,EAAG;AAAA,YAC/B;AAGA,iBAAK;AAAA,UACN;AAAA,QACD;AAMA,iBAAS,kBAAmB,MAAO;AAClC,iBAAO,SAAU,MAAO;AACvB,mBAAO,SAAU,MAAM,OAAQ,KAAK,KAAK,SAAS;AAAA,UACnD;AAAA,QACD;AAMA,iBAAS,mBAAoB,MAAO;AACnC,iBAAO,SAAU,MAAO;AACvB,oBAAS,SAAU,MAAM,OAAQ,KAAK,SAAU,MAAM,QAAS,MAC9D,KAAK,SAAS;AAAA,UAChB;AAAA,QACD;AAMA,iBAAS,qBAAsB,UAAW;AAGzC,iBAAO,SAAU,MAAO;AAKvB,gBAAK,UAAU,MAAO;AASrB,kBAAK,KAAK,cAAc,KAAK,aAAa,OAAQ;AAGjD,oBAAK,WAAW,MAAO;AACtB,sBAAK,WAAW,KAAK,YAAa;AACjC,2BAAO,KAAK,WAAW,aAAa;AAAA,kBACrC,OAAO;AACN,2BAAO,KAAK,aAAa;AAAA,kBAC1B;AAAA,gBACD;AAIA,uBAAO,KAAK,eAAe;AAAA,gBAG1B,KAAK,eAAe,CAAC,YACpB,mBAAoB,IAAK,MAAM;AAAA,cAClC;AAEA,qBAAO,KAAK,aAAa;AAAA,YAK1B,WAAY,WAAW,MAAO;AAC7B,qBAAO,KAAK,aAAa;AAAA,YAC1B;AAGA,mBAAO;AAAA,UACR;AAAA,QACD;AAMA,iBAAS,uBAAwB,IAAK;AACrC,iBAAO,aAAc,SAAU,UAAW;AACzC,uBAAW,CAAC;AACZ,mBAAO,aAAc,SAAU,MAAMI,UAAU;AAC9C,kBAAI,GACH,eAAe,GAAI,CAAC,GAAG,KAAK,QAAQ,QAAS,GAC7CD,KAAI,aAAa;AAGlB,qBAAQA,MAAM;AACb,oBAAK,KAAQ,IAAI,aAAcA,EAAE,CAAI,GAAI;AACxC,uBAAM,CAAE,IAAI,EAAGC,SAAS,CAAE,IAAI,KAAM,CAAE;AAAA,gBACvC;AAAA,cACD;AAAA,YACD,CAAE;AAAA,UACH,CAAE;AAAA,QACH;AAOA,iBAAS,YAAa,SAAU;AAC/B,iBAAO,WAAW,OAAO,QAAQ,yBAAyB,eAAe;AAAA,QAC1E;AAOA,iBAAS,YAAa,MAAO;AAC5B,cAAI,WACH,MAAM,OAAO,KAAK,iBAAiB,OAAO;AAO3C,cAAK,OAAOJ,aAAY,IAAI,aAAa,KAAK,CAAC,IAAI,iBAAkB;AACpE,mBAAOA;AAAA,UACR;AAGA,UAAAA,YAAW;AACX,UAAAC,mBAAkBD,UAAS;AAC3B,2BAAiB,CAAC,OAAO,SAAUA,SAAS;AAI5C,oBAAUC,iBAAgB,WACzBA,iBAAgB,yBAChBA,iBAAgB;AAOjB,cAAKA,iBAAgB;AAAA;AAAA;AAAA;AAAA,UAMpB,gBAAgBD,cACd,YAAYA,UAAS,gBAAiB,UAAU,QAAQ,WAAY;AAGtE,sBAAU,iBAAkB,UAAU,aAAc;AAAA,UACrD;AAMA,kBAAQ,UAAU,OAAQ,SAAU,IAAK;AACxC,YAAAC,iBAAgB,YAAa,EAAG,EAAE,KAAK,OAAO;AAC9C,mBAAO,CAACD,UAAS,qBAChB,CAACA,UAAS,kBAAmB,OAAO,OAAQ,EAAE;AAAA,UAChD,CAAE;AAKF,kBAAQ,oBAAoB,OAAQ,SAAU,IAAK;AAClD,mBAAO,QAAQ,KAAM,IAAI,GAAI;AAAA,UAC9B,CAAE;AAIF,kBAAQ,QAAQ,OAAQ,WAAW;AAClC,mBAAOA,UAAS,iBAAkB,QAAS;AAAA,UAC5C,CAAE;AAWF,kBAAQ,SAAS,OAAQ,WAAW;AACnC,gBAAI;AACH,cAAAA,UAAS,cAAe,iBAAkB;AAC1C,qBAAO;AAAA,YACR,SAAU,GAAI;AACb,qBAAO;AAAA,YACR;AAAA,UACD,CAAE;AAGF,cAAK,QAAQ,SAAU;AACtB,iBAAK,OAAO,KAAK,SAAU,IAAK;AAC/B,kBAAI,SAAS,GAAG,QAAS,WAAW,SAAU;AAC9C,qBAAO,SAAU,MAAO;AACvB,uBAAO,KAAK,aAAc,IAAK,MAAM;AAAA,cACtC;AAAA,YACD;AACA,iBAAK,KAAK,KAAK,SAAU,IAAI,SAAU;AACtC,kBAAK,OAAO,QAAQ,mBAAmB,eAAe,gBAAiB;AACtE,oBAAI,OAAO,QAAQ,eAAgB,EAAG;AACtC,uBAAO,OAAO,CAAE,IAAK,IAAI,CAAC;AAAA,cAC3B;AAAA,YACD;AAAA,UACD,OAAO;AACN,iBAAK,OAAO,KAAM,SAAU,IAAK;AAChC,kBAAI,SAAS,GAAG,QAAS,WAAW,SAAU;AAC9C,qBAAO,SAAU,MAAO;AACvB,oBAAIK,QAAO,OAAO,KAAK,qBAAqB,eAC3C,KAAK,iBAAkB,IAAK;AAC7B,uBAAOA,SAAQA,MAAK,UAAU;AAAA,cAC/B;AAAA,YACD;AAIA,iBAAK,KAAK,KAAK,SAAU,IAAI,SAAU;AACtC,kBAAK,OAAO,QAAQ,mBAAmB,eAAe,gBAAiB;AACtE,oBAAIA,OAAMF,IAAG,OACZ,OAAO,QAAQ,eAAgB,EAAG;AAEnC,oBAAK,MAAO;AAGX,kBAAAE,QAAO,KAAK,iBAAkB,IAAK;AACnC,sBAAKA,SAAQA,MAAK,UAAU,IAAK;AAChC,2BAAO,CAAE,IAAK;AAAA,kBACf;AAGA,0BAAQ,QAAQ,kBAAmB,EAAG;AACtC,kBAAAF,KAAI;AACJ,yBAAU,OAAO,MAAOA,IAAI,GAAM;AACjC,oBAAAE,QAAO,KAAK,iBAAkB,IAAK;AACnC,wBAAKA,SAAQA,MAAK,UAAU,IAAK;AAChC,6BAAO,CAAE,IAAK;AAAA,oBACf;AAAA,kBACD;AAAA,gBACD;AAEA,uBAAO,CAAC;AAAA,cACT;AAAA,YACD;AAAA,UACD;AAGA,eAAK,KAAK,MAAM,SAAU,KAAK,SAAU;AACxC,gBAAK,OAAO,QAAQ,yBAAyB,aAAc;AAC1D,qBAAO,QAAQ,qBAAsB,GAAI;AAAA,YAG1C,OAAO;AACN,qBAAO,QAAQ,iBAAkB,GAAI;AAAA,YACtC;AAAA,UACD;AAGA,eAAK,KAAK,QAAQ,SAAU,WAAW,SAAU;AAChD,gBAAK,OAAO,QAAQ,2BAA2B,eAAe,gBAAiB;AAC9E,qBAAO,QAAQ,uBAAwB,SAAU;AAAA,YAClD;AAAA,UACD;AAOA,sBAAY,CAAC;AAIb,iBAAQ,SAAU,IAAK;AAEtB,gBAAI;AAEJ,YAAAJ,iBAAgB,YAAa,EAAG,EAAE,YACjC,YAAY,UAAU,mDACL,UAAU;AAK5B,gBAAK,CAAC,GAAG,iBAAkB,YAAa,EAAE,QAAS;AAClD,wBAAU,KAAM,QAAQ,aAAa,eAAe,WAAW,GAAI;AAAA,YACpE;AAGA,gBAAK,CAAC,GAAG,iBAAkB,UAAU,UAAU,IAAK,EAAE,QAAS;AAC9D,wBAAU,KAAM,IAAK;AAAA,YACtB;AAKA,gBAAK,CAAC,GAAG,iBAAkB,OAAO,UAAU,IAAK,EAAE,QAAS;AAC3D,wBAAU,KAAM,UAAW;AAAA,YAC5B;AAMA,gBAAK,CAAC,GAAG,iBAAkB,UAAW,EAAE,QAAS;AAChD,wBAAU,KAAM,UAAW;AAAA,YAC5B;AAIA,oBAAQD,UAAS,cAAe,OAAQ;AACxC,kBAAM,aAAc,QAAQ,QAAS;AACrC,eAAG,YAAa,KAAM,EAAE,aAAc,QAAQ,GAAI;AAQlD,YAAAC,iBAAgB,YAAa,EAAG,EAAE,WAAW;AAC7C,gBAAK,GAAG,iBAAkB,WAAY,EAAE,WAAW,GAAI;AACtD,wBAAU,KAAM,YAAY,WAAY;AAAA,YACzC;AAOA,oBAAQD,UAAS,cAAe,OAAQ;AACxC,kBAAM,aAAc,QAAQ,EAAG;AAC/B,eAAG,YAAa,KAAM;AACtB,gBAAK,CAAC,GAAG,iBAAkB,WAAY,EAAE,QAAS;AACjD,wBAAU,KAAM,QAAQ,aAAa,UAAU,aAAa,OAC3D,aAAa,YAAe;AAAA,YAC9B;AAAA,UACD,CAAE;AAEF,cAAK,CAAC,QAAQ,QAAS;AAQtB,sBAAU,KAAM,MAAO;AAAA,UACxB;AAEA,sBAAY,UAAU,UAAU,IAAI,OAAQ,UAAU,KAAM,GAAI,CAAE;AAMlE,sBAAY,SAAU,GAAG,GAAI;AAG5B,gBAAK,MAAM,GAAI;AACd,6BAAe;AACf,qBAAO;AAAA,YACR;AAGA,gBAAI,UAAU,CAAC,EAAE,0BAA0B,CAAC,EAAE;AAC9C,gBAAK,SAAU;AACd,qBAAO;AAAA,YACR;AAOA,uBAAY,EAAE,iBAAiB,OAAS,EAAE,iBAAiB,KAC1D,EAAE,wBAAyB,CAAE;AAAA;AAAA,cAG7B;AAAA;AAGD,gBAAK,UAAU,KACZ,CAAC,QAAQ,gBAAgB,EAAE,wBAAyB,CAAE,MAAM,SAAY;AAO1E,kBAAK,MAAMA,aAAY,EAAE,iBAAiB,gBACzC,KAAK,SAAU,cAAc,CAAE,GAAI;AACnC,uBAAO;AAAA,cACR;AAMA,kBAAK,MAAMA,aAAY,EAAE,iBAAiB,gBACzC,KAAK,SAAU,cAAc,CAAE,GAAI;AACnC,uBAAO;AAAA,cACR;AAGA,qBAAO,YACJ,QAAQ,KAAM,WAAW,CAAE,IAAI,QAAQ,KAAM,WAAW,CAAE,IAC5D;AAAA,YACF;AAEA,mBAAO,UAAU,IAAI,KAAK;AAAA,UAC3B;AAEA,iBAAOA;AAAA,QACR;AAEA,aAAK,UAAU,SAAU,MAAM,UAAW;AACzC,iBAAO,KAAM,MAAM,MAAM,MAAM,QAAS;AAAA,QACzC;AAEA,aAAK,kBAAkB,SAAU,MAAM,MAAO;AAC7C,sBAAa,IAAK;AAElB,cAAK,kBACJ,CAAC,uBAAwB,OAAO,GAAI,MAClC,CAAC,aAAa,CAAC,UAAU,KAAM,IAAK,IAAM;AAE5C,gBAAI;AACH,kBAAI,MAAM,QAAQ,KAAM,MAAM,IAAK;AAGnC,kBAAK,OAAO,QAAQ;AAAA;AAAA,cAIlB,KAAK,YAAY,KAAK,SAAS,aAAa,IAAK;AAClD,uBAAO;AAAA,cACR;AAAA,YACD,SAAU,GAAI;AACb,qCAAwB,MAAM,IAAK;AAAA,YACpC;AAAA,UACD;AAEA,iBAAO,KAAM,MAAMA,WAAU,MAAM,CAAE,IAAK,CAAE,EAAE,SAAS;AAAA,QACxD;AAEA,aAAK,WAAW,SAAU,SAAS,MAAO;AAOzC,eAAO,QAAQ,iBAAiB,YAAaA,WAAW;AACvD,wBAAa,OAAQ;AAAA,UACtB;AACA,iBAAO,OAAO,SAAU,SAAS,IAAK;AAAA,QACvC;AAGA,aAAK,OAAO,SAAU,MAAM,MAAO;AAOlC,eAAO,KAAK,iBAAiB,SAAUA,WAAW;AACjD,wBAAa,IAAK;AAAA,UACnB;AAEA,cAAI,KAAK,KAAK,WAAY,KAAK,YAAY,CAAE,GAG5C,MAAM,MAAM,OAAO,KAAM,KAAK,YAAY,KAAK,YAAY,CAAE,IAC5D,GAAI,MAAM,MAAM,CAAC,cAAe,IAChC;AAEF,cAAK,QAAQ,QAAY;AACxB,mBAAO;AAAA,UACR;AAEA,iBAAO,KAAK,aAAc,IAAK;AAAA,QAChC;AAEA,aAAK,QAAQ,SAAU,KAAM;AAC5B,gBAAM,IAAI,MAAO,4CAA4C,GAAI;AAAA,QAClE;AAMA,eAAO,aAAa,SAAU,SAAU;AACvC,cAAI,MACH,aAAa,CAAC,GACd,IAAI,GACJG,KAAI;AAOL,yBAAe,CAAC,QAAQ;AACxB,sBAAY,CAAC,QAAQ,cAAc,MAAM,KAAM,SAAS,CAAE;AAC1D,eAAK,KAAM,SAAS,SAAU;AAE9B,cAAK,cAAe;AACnB,mBAAU,OAAO,QAASA,IAAI,GAAM;AACnC,kBAAK,SAAS,QAASA,EAAE,GAAI;AAC5B,oBAAI,WAAW,KAAMA,EAAE;AAAA,cACxB;AAAA,YACD;AACA,mBAAQ,KAAM;AACb,qBAAO,KAAM,SAAS,WAAY,CAAE,GAAG,CAAE;AAAA,YAC1C;AAAA,UACD;AAIA,sBAAY;AAEZ,iBAAO;AAAA,QACR;AAEA,eAAO,GAAG,aAAa,WAAW;AACjC,iBAAO,KAAK,UAAW,OAAO,WAAY,MAAM,MAAO,IAAK,CAAE,CAAE;AAAA,QACjE;AAEA,eAAO,OAAO,OAAO;AAAA;AAAA,UAGpB,aAAa;AAAA,UAEb,cAAc;AAAA,UAEd,OAAO;AAAA,UAEP,YAAY,CAAC;AAAA,UAEb,MAAM,CAAC;AAAA,UAEP,UAAU;AAAA,YACT,KAAK,EAAE,KAAK,cAAc,OAAO,KAAK;AAAA,YACtC,KAAK,EAAE,KAAK,aAAa;AAAA,YACzB,KAAK,EAAE,KAAK,mBAAmB,OAAO,KAAK;AAAA,YAC3C,KAAK,EAAE,KAAK,kBAAkB;AAAA,UAC/B;AAAA,UAEA,WAAW;AAAA,YACV,MAAM,SAAU,OAAQ;AACvB,oBAAO,CAAE,IAAI,MAAO,CAAE,EAAE,QAAS,WAAW,SAAU;AAGtD,oBAAO,CAAE,KAAM,MAAO,CAAE,KAAK,MAAO,CAAE,KAAK,MAAO,CAAE,KAAK,IACvD,QAAS,WAAW,SAAU;AAEhC,kBAAK,MAAO,CAAE,MAAM,MAAO;AAC1B,sBAAO,CAAE,IAAI,MAAM,MAAO,CAAE,IAAI;AAAA,cACjC;AAEA,qBAAO,MAAM,MAAO,GAAG,CAAE;AAAA,YAC1B;AAAA,YAEA,OAAO,SAAU,OAAQ;AAYxB,oBAAO,CAAE,IAAI,MAAO,CAAE,EAAE,YAAY;AAEpC,kBAAK,MAAO,CAAE,EAAE,MAAO,GAAG,CAAE,MAAM,OAAQ;AAGzC,oBAAK,CAAC,MAAO,CAAE,GAAI;AAClB,uBAAK,MAAO,MAAO,CAAE,CAAE;AAAA,gBACxB;AAIA,sBAAO,CAAE,IAAI,EAAG,MAAO,CAAE,IACxB,MAAO,CAAE,KAAM,MAAO,CAAE,KAAK,KAC7B,KAAM,MAAO,CAAE,MAAM,UAAU,MAAO,CAAE,MAAM;AAE/C,sBAAO,CAAE,IAAI,EAAK,MAAO,CAAE,IAAI,MAAO,CAAE,KAAO,MAAO,CAAE,MAAM;AAAA,cAG/D,WAAY,MAAO,CAAE,GAAI;AACxB,qBAAK,MAAO,MAAO,CAAE,CAAE;AAAA,cACxB;AAEA,qBAAO;AAAA,YACR;AAAA,YAEA,QAAQ,SAAU,OAAQ;AACzB,kBAAI,QACH,WAAW,CAAC,MAAO,CAAE,KAAK,MAAO,CAAE;AAEpC,kBAAK,UAAU,MAAM,KAAM,MAAO,CAAE,CAAE,GAAI;AACzC,uBAAO;AAAA,cACR;AAGA,kBAAK,MAAO,CAAE,GAAI;AACjB,sBAAO,CAAE,IAAI,MAAO,CAAE,KAAK,MAAO,CAAE,KAAK;AAAA,cAG1C,WAAY,YAAY,QAAQ,KAAM,QAAS;AAAA,eAG5C,SAAS,SAAU,UAAU,IAAK;AAAA,eAGlC,SAAS,SAAS,QAAS,KAAK,SAAS,SAAS,MAAO,IAAI,SAAS,SAAW;AAGnF,sBAAO,CAAE,IAAI,MAAO,CAAE,EAAE,MAAO,GAAG,MAAO;AACzC,sBAAO,CAAE,IAAI,SAAS,MAAO,GAAG,MAAO;AAAA,cACxC;AAGA,qBAAO,MAAM,MAAO,GAAG,CAAE;AAAA,YAC1B;AAAA,UACD;AAAA,UAEA,QAAQ;AAAA,YAEP,KAAK,SAAU,kBAAmB;AACjC,kBAAI,mBAAmB,iBAAiB,QAAS,WAAW,SAAU,EAAE,YAAY;AACpF,qBAAO,qBAAqB,MAC3B,WAAW;AACV,uBAAO;AAAA,cACR,IACA,SAAU,MAAO;AAChB,uBAAO,SAAU,MAAM,gBAAiB;AAAA,cACzC;AAAA,YACF;AAAA,YAEA,OAAO,SAAU,WAAY;AAC5B,kBAAI,UAAU,WAAY,YAAY,GAAI;AAE1C,qBAAO,YACJ,UAAU,IAAI,OAAQ,QAAQ,aAAa,MAAM,YAClD,MAAM,aAAa,KAAM,MAC1B,WAAY,WAAW,SAAU,MAAO;AACvC,uBAAO,QAAQ;AAAA,kBACd,OAAO,KAAK,cAAc,YAAY,KAAK,aAC1C,OAAO,KAAK,iBAAiB,eAC5B,KAAK,aAAc,OAAQ,KAC5B;AAAA,gBACF;AAAA,cACD,CAAE;AAAA,YACJ;AAAA,YAEA,MAAM,SAAU,MAAM,UAAU,OAAQ;AACvC,qBAAO,SAAU,MAAO;AACvB,oBAAI,SAAS,KAAK,KAAM,MAAM,IAAK;AAEnC,oBAAK,UAAU,MAAO;AACrB,yBAAO,aAAa;AAAA,gBACrB;AACA,oBAAK,CAAC,UAAW;AAChB,yBAAO;AAAA,gBACR;AAEA,0BAAU;AAEV,oBAAK,aAAa,KAAM;AACvB,yBAAO,WAAW;AAAA,gBACnB;AACA,oBAAK,aAAa,MAAO;AACxB,yBAAO,WAAW;AAAA,gBACnB;AACA,oBAAK,aAAa,MAAO;AACxB,yBAAO,SAAS,OAAO,QAAS,KAAM,MAAM;AAAA,gBAC7C;AACA,oBAAK,aAAa,MAAO;AACxB,yBAAO,SAAS,OAAO,QAAS,KAAM,IAAI;AAAA,gBAC3C;AACA,oBAAK,aAAa,MAAO;AACxB,yBAAO,SAAS,OAAO,MAAO,CAAC,MAAM,MAAO,MAAM;AAAA,gBACnD;AACA,oBAAK,aAAa,MAAO;AACxB,0BAAS,MAAM,OAAO,QAAS,aAAa,GAAI,IAAI,KAClD,QAAS,KAAM,IAAI;AAAA,gBACtB;AACA,oBAAK,aAAa,MAAO;AACxB,yBAAO,WAAW,SAAS,OAAO,MAAO,GAAG,MAAM,SAAS,CAAE,MAAM,QAAQ;AAAA,gBAC5E;AAEA,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,YAEA,OAAO,SAAU,MAAM,MAAM,WAAW,OAAO,MAAO;AACrD,kBAAI,SAAS,KAAK,MAAO,GAAG,CAAE,MAAM,OACnC,UAAU,KAAK,MAAO,EAAG,MAAM,QAC/B,SAAS,SAAS;AAEnB,qBAAO,UAAU,KAAK,SAAS;AAAA;AAAA,gBAG9B,SAAU,MAAO;AAChB,yBAAO,CAAC,CAAC,KAAK;AAAA,gBACf;AAAA,kBAEA,SAAU,MAAM,UAAU,KAAM;AAC/B,oBAAI,OAAO,YAAY,MAAM,WAAW,OACvCG,OAAM,WAAW,UAAU,gBAAgB,mBAC3C,SAAS,KAAK,YACd,OAAO,UAAU,KAAK,SAAS,YAAY,GAC3C,WAAW,CAAC,OAAO,CAAC,QACpB,OAAO;AAER,oBAAK,QAAS;AAGb,sBAAK,QAAS;AACb,2BAAQA,MAAM;AACb,6BAAO;AACP,6BAAU,OAAO,KAAMA,IAAI,GAAM;AAChC,4BAAK,SACJ,SAAU,MAAM,IAAK,IACrB,KAAK,aAAa,GAAI;AAEtB,iCAAO;AAAA,wBACR;AAAA,sBACD;AAGA,8BAAQA,OAAM,SAAS,UAAU,CAAC,SAAS;AAAA,oBAC5C;AACA,2BAAO;AAAA,kBACR;AAEA,0BAAQ,CAAE,UAAU,OAAO,aAAa,OAAO,SAAU;AAGzD,sBAAK,WAAW,UAAW;AAG1B,iCAAa,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,IAAI,CAAC;AACzD,4BAAQ,WAAY,IAAK,KAAK,CAAC;AAC/B,gCAAY,MAAO,CAAE,MAAM,WAAW,MAAO,CAAE;AAC/C,2BAAO,aAAa,MAAO,CAAE;AAC7B,2BAAO,aAAa,OAAO,WAAY,SAAU;AAEjD,2BAAU,OAAO,EAAE,aAAa,QAAQ,KAAMA,IAAI;AAAA,qBAG/C,OAAO,YAAY,MAAO,MAAM,IAAI,GAAM;AAG5C,0BAAK,KAAK,aAAa,KAAK,EAAE,QAAQ,SAAS,MAAO;AACrD,mCAAY,IAAK,IAAI,CAAE,SAAS,WAAW,IAAK;AAChD;AAAA,sBACD;AAAA,oBACD;AAAA,kBAED,OAAO;AAGN,wBAAK,UAAW;AACf,mCAAa,KAAM,OAAQ,MAAO,KAAM,OAAQ,IAAI,CAAC;AACrD,8BAAQ,WAAY,IAAK,KAAK,CAAC;AAC/B,kCAAY,MAAO,CAAE,MAAM,WAAW,MAAO,CAAE;AAC/C,6BAAO;AAAA,oBACR;AAIA,wBAAK,SAAS,OAAQ;AAGrB,6BAAU,OAAO,EAAE,aAAa,QAAQ,KAAMA,IAAI,MAC/C,OAAO,YAAY,MAAO,MAAM,IAAI,GAAM;AAE5C,6BAAO,SACN,SAAU,MAAM,IAAK,IACrB,KAAK,aAAa,MAClB,EAAE,MAAO;AAGT,8BAAK,UAAW;AACf,yCAAa,KAAM,OAAQ,MACxB,KAAM,OAAQ,IAAI,CAAC;AACtB,uCAAY,IAAK,IAAI,CAAE,SAAS,IAAK;AAAA,0BACtC;AAEA,8BAAK,SAAS,MAAO;AACpB;AAAA,0BACD;AAAA,wBACD;AAAA,sBACD;AAAA,oBACD;AAAA,kBACD;AAGA,0BAAQ;AACR,yBAAO,SAAS,SAAW,OAAO,UAAU,KAAK,OAAO,SAAS;AAAA,gBAClE;AAAA,cACD;AAAA,YACF;AAAA,YAEA,QAAQ,SAAU,QAAQ,UAAW;AAMpC,kBAAI,MACH,KAAK,KAAK,QAAS,MAAO,KAAK,KAAK,WAAY,OAAO,YAAY,CAAE,KACpE,KAAK,MAAO,yBAAyB,MAAO;AAK9C,kBAAK,GAAI,OAAQ,GAAI;AACpB,uBAAO,GAAI,QAAS;AAAA,cACrB;AAGA,kBAAK,GAAG,SAAS,GAAI;AACpB,uBAAO,CAAE,QAAQ,QAAQ,IAAI,QAAS;AACtC,uBAAO,KAAK,WAAW,eAAgB,OAAO,YAAY,CAAE,IAC3D,aAAc,SAAU,MAAMF,UAAU;AACvC,sBAAI,KACH,UAAU,GAAI,MAAM,QAAS,GAC7BD,KAAI,QAAQ;AACb,yBAAQA,MAAM;AACb,0BAAM,QAAQ,KAAM,MAAM,QAASA,EAAE,CAAE;AACvC,yBAAM,GAAI,IAAI,EAAGC,SAAS,GAAI,IAAI,QAASD,EAAE;AAAA,kBAC9C;AAAA,gBACD,CAAE,IACF,SAAU,MAAO;AAChB,yBAAO,GAAI,MAAM,GAAG,IAAK;AAAA,gBAC1B;AAAA,cACF;AAEA,qBAAO;AAAA,YACR;AAAA,UACD;AAAA,UAEA,SAAS;AAAA;AAAA,YAGR,KAAK,aAAc,SAAU,UAAW;AAKvC,kBAAI,QAAQ,CAAC,GACZ,UAAU,CAAC,GACX,UAAU,QAAS,SAAS,QAAS,UAAU,IAAK,CAAE;AAEvD,qBAAO,QAAS,OAAQ,IACvB,aAAc,SAAU,MAAMC,UAAS,UAAU,KAAM;AACtD,oBAAI,MACH,YAAY,QAAS,MAAM,MAAM,KAAK,CAAC,CAAE,GACzCD,KAAI,KAAK;AAGV,uBAAQA,MAAM;AACb,sBAAO,OAAO,UAAWA,EAAE,GAAM;AAChC,yBAAMA,EAAE,IAAI,EAAGC,SAASD,EAAE,IAAI;AAAA,kBAC/B;AAAA,gBACD;AAAA,cACD,CAAE,IACF,SAAU,MAAM,UAAU,KAAM;AAC/B,sBAAO,CAAE,IAAI;AACb,wBAAS,OAAO,MAAM,KAAK,OAAQ;AAInC,sBAAO,CAAE,IAAI;AACb,uBAAO,CAAC,QAAQ,IAAI;AAAA,cACrB;AAAA,YACF,CAAE;AAAA,YAEF,KAAK,aAAc,SAAU,UAAW;AACvC,qBAAO,SAAU,MAAO;AACvB,uBAAO,KAAM,UAAU,IAAK,EAAE,SAAS;AAAA,cACxC;AAAA,YACD,CAAE;AAAA,YAEF,UAAU,aAAc,SAAU,MAAO;AACxC,qBAAO,KAAK,QAAS,WAAW,SAAU;AAC1C,qBAAO,SAAU,MAAO;AACvB,wBAAS,KAAK,eAAe,OAAO,KAAM,IAAK,GAAI,QAAS,IAAK,IAAI;AAAA,cACtE;AAAA,YACD,CAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YASF,MAAM,aAAc,SAAU,MAAO;AAGpC,kBAAK,CAAC,YAAY,KAAM,QAAQ,EAAG,GAAI;AACtC,qBAAK,MAAO,uBAAuB,IAAK;AAAA,cACzC;AACA,qBAAO,KAAK,QAAS,WAAW,SAAU,EAAE,YAAY;AACxD,qBAAO,SAAU,MAAO;AACvB,oBAAI;AACJ,mBAAG;AACF,sBAAO,WAAW,iBACjB,KAAK,OACL,KAAK,aAAc,UAAW,KAAK,KAAK,aAAc,MAAO,GAAM;AAEnE,+BAAW,SAAS,YAAY;AAChC,2BAAO,aAAa,QAAQ,SAAS,QAAS,OAAO,GAAI,MAAM;AAAA,kBAChE;AAAA,gBACD,UAAY,OAAO,KAAK,eAAgB,KAAK,aAAa;AAC1D,uBAAO;AAAA,cACR;AAAA,YACD,CAAE;AAAA;AAAA,YAGF,QAAQ,SAAU,MAAO;AACxB,kBAAI,OAAOR,QAAO,YAAYA,QAAO,SAAS;AAC9C,qBAAO,QAAQ,KAAK,MAAO,CAAE,MAAM,KAAK;AAAA,YACzC;AAAA,YAEA,MAAM,SAAU,MAAO;AACtB,qBAAO,SAASM;AAAA,YACjB;AAAA,YAEA,OAAO,SAAU,MAAO;AACvB,qBAAO,SAAS,kBAAkB,KACjCD,UAAS,SAAS,KAClB,CAAC,EAAG,KAAK,QAAQ,KAAK,QAAQ,CAAC,KAAK;AAAA,YACtC;AAAA;AAAA,YAGA,SAAS,qBAAsB,KAAM;AAAA,YACrC,UAAU,qBAAsB,IAAK;AAAA,YAErC,SAAS,SAAU,MAAO;AAIzB,qBAAS,SAAU,MAAM,OAAQ,KAAK,CAAC,CAAC,KAAK,WAC1C,SAAU,MAAM,QAAS,KAAK,CAAC,CAAC,KAAK;AAAA,YACzC;AAAA,YAEA,UAAU,SAAU,MAAO;AAM1B,kBAAK,KAAK,YAAa;AAEtB,qBAAK,WAAW;AAAA,cACjB;AAEA,qBAAO,KAAK,aAAa;AAAA,YAC1B;AAAA;AAAA,YAGA,OAAO,SAAU,MAAO;AAMvB,mBAAM,OAAO,KAAK,YAAY,MAAM,OAAO,KAAK,aAAc;AAC7D,oBAAK,KAAK,WAAW,GAAI;AACxB,yBAAO;AAAA,gBACR;AAAA,cACD;AACA,qBAAO;AAAA,YACR;AAAA,YAEA,QAAQ,SAAU,MAAO;AACxB,qBAAO,CAAC,KAAK,QAAQ,MAAO,IAAK;AAAA,YAClC;AAAA;AAAA,YAGA,QAAQ,SAAU,MAAO;AACxB,qBAAO,QAAQ,KAAM,KAAK,QAAS;AAAA,YACpC;AAAA,YAEA,OAAO,SAAU,MAAO;AACvB,qBAAO,QAAQ,KAAM,KAAK,QAAS;AAAA,YACpC;AAAA,YAEA,QAAQ,SAAU,MAAO;AACxB,qBAAO,SAAU,MAAM,OAAQ,KAAK,KAAK,SAAS,YACjD,SAAU,MAAM,QAAS;AAAA,YAC3B;AAAA,YAEA,MAAM,SAAU,MAAO;AACtB,kBAAI;AACJ,qBAAO,SAAU,MAAM,OAAQ,KAAK,KAAK,SAAS;AAAA;AAAA;AAAA,gBAK7C,OAAO,KAAK,aAAc,MAAO,MAAO,QAC3C,KAAK,YAAY,MAAM;AAAA,YAC1B;AAAA;AAAA,YAGA,OAAO,uBAAwB,WAAW;AACzC,qBAAO,CAAE,CAAE;AAAA,YACZ,CAAE;AAAA,YAEF,MAAM,uBAAwB,SAAU,eAAe,QAAS;AAC/D,qBAAO,CAAE,SAAS,CAAE;AAAA,YACrB,CAAE;AAAA,YAEF,IAAI,uBAAwB,SAAU,eAAe,QAAQ,UAAW;AACvE,qBAAO,CAAE,WAAW,IAAI,WAAW,SAAS,QAAS;AAAA,YACtD,CAAE;AAAA,YAEF,MAAM,uBAAwB,SAAU,cAAc,QAAS;AAC9D,kBAAIG,KAAI;AACR,qBAAQA,KAAI,QAAQA,MAAK,GAAI;AAC5B,6BAAa,KAAMA,EAAE;AAAA,cACtB;AACA,qBAAO;AAAA,YACR,CAAE;AAAA,YAEF,KAAK,uBAAwB,SAAU,cAAc,QAAS;AAC7D,kBAAIA,KAAI;AACR,qBAAQA,KAAI,QAAQA,MAAK,GAAI;AAC5B,6BAAa,KAAMA,EAAE;AAAA,cACtB;AACA,qBAAO;AAAA,YACR,CAAE;AAAA,YAEF,IAAI,uBAAwB,SAAU,cAAc,QAAQ,UAAW;AACtE,kBAAIA;AAEJ,kBAAK,WAAW,GAAI;AACnB,gBAAAA,KAAI,WAAW;AAAA,cAChB,WAAY,WAAW,QAAS;AAC/B,gBAAAA,KAAI;AAAA,cACL,OAAO;AACN,gBAAAA,KAAI;AAAA,cACL;AAEA,qBAAQ,EAAEA,MAAK,KAAK;AACnB,6BAAa,KAAMA,EAAE;AAAA,cACtB;AACA,qBAAO;AAAA,YACR,CAAE;AAAA,YAEF,IAAI,uBAAwB,SAAU,cAAc,QAAQ,UAAW;AACtE,kBAAIA,KAAI,WAAW,IAAI,WAAW,SAAS;AAC3C,qBAAQ,EAAEA,KAAI,UAAU;AACvB,6BAAa,KAAMA,EAAE;AAAA,cACtB;AACA,qBAAO;AAAA,YACR,CAAE;AAAA,UACH;AAAA,QACD;AAEA,aAAK,QAAQ,MAAM,KAAK,QAAQ;AAGhC,aAAM,KAAK,EAAE,OAAO,MAAM,UAAU,MAAM,MAAM,MAAM,UAAU,MAAM,OAAO,KAAK,GAAI;AACrF,eAAK,QAAS,CAAE,IAAI,kBAAmB,CAAE;AAAA,QAC1C;AACA,aAAM,KAAK,EAAE,QAAQ,MAAM,OAAO,KAAK,GAAI;AAC1C,eAAK,QAAS,CAAE,IAAI,mBAAoB,CAAE;AAAA,QAC3C;AAGA,iBAAS,aAAa;AAAA,QAAC;AACvB,mBAAW,YAAY,KAAK,UAAU,KAAK;AAC3C,aAAK,aAAa,IAAI,WAAW;AAEjC,iBAAS,SAAU,UAAU,WAAY;AACxC,cAAI,SAAS,OAAO,QAAQ,MAC3B,OAAO,QAAQ,YACf,SAAS,WAAY,WAAW,GAAI;AAErC,cAAK,QAAS;AACb,mBAAO,YAAY,IAAI,OAAO,MAAO,CAAE;AAAA,UACxC;AAEA,kBAAQ;AACR,mBAAS,CAAC;AACV,uBAAa,KAAK;AAElB,iBAAQ,OAAQ;AAGf,gBAAK,CAAC,YAAa,QAAQ,OAAO,KAAM,KAAM,IAAM;AACnD,kBAAK,OAAQ;AAGZ,wBAAQ,MAAM,MAAO,MAAO,CAAE,EAAE,MAAO,KAAK;AAAA,cAC7C;AACA,qBAAO,KAAQ,SAAS,CAAC,CAAI;AAAA,YAC9B;AAEA,sBAAU;AAGV,gBAAO,QAAQ,mBAAmB,KAAM,KAAM,GAAM;AACnD,wBAAU,MAAM,MAAM;AACtB,qBAAO,KAAM;AAAA,gBACZ,OAAO;AAAA;AAAA,gBAGP,MAAM,MAAO,CAAE,EAAE,QAAS,UAAU,GAAI;AAAA,cACzC,CAAE;AACF,sBAAQ,MAAM,MAAO,QAAQ,MAAO;AAAA,YACrC;AAGA,iBAAM,QAAQ,KAAK,QAAS;AAC3B,mBAAO,QAAQ,UAAW,IAAK,EAAE,KAAM,KAAM,OAAS,CAAC,WAAY,IAAK,MACrE,QAAQ,WAAY,IAAK,EAAG,KAAM,KAAQ;AAC5C,0BAAU,MAAM,MAAM;AACtB,uBAAO,KAAM;AAAA,kBACZ,OAAO;AAAA,kBACP;AAAA,kBACA,SAAS;AAAA,gBACV,CAAE;AACF,wBAAQ,MAAM,MAAO,QAAQ,MAAO;AAAA,cACrC;AAAA,YACD;AAEA,gBAAK,CAAC,SAAU;AACf;AAAA,YACD;AAAA,UACD;AAKA,cAAK,WAAY;AAChB,mBAAO,MAAM;AAAA,UACd;AAEA,iBAAO,QACN,KAAK,MAAO,QAAS;AAAA;AAAA,YAGrB,WAAY,UAAU,MAAO,EAAE,MAAO,CAAE;AAAA;AAAA,QAC1C;AAEA,iBAAS,WAAY,QAAS;AAC7B,cAAIA,KAAI,GACP,MAAM,OAAO,QACb,WAAW;AACZ,iBAAQA,KAAI,KAAKA,MAAM;AACtB,wBAAY,OAAQA,EAAE,EAAE;AAAA,UACzB;AACA,iBAAO;AAAA,QACR;AAEA,iBAAS,cAAe,SAAS,YAAY,MAAO;AACnD,cAAIG,OAAM,WAAW,KACpB,OAAO,WAAW,MAClB,MAAM,QAAQA,MACd,mBAAmB,QAAQ,QAAQ,cACnC,WAAW;AAEZ,iBAAO,WAAW;AAAA;AAAA,YAGjB,SAAU,MAAM,SAAS,KAAM;AAC9B,qBAAU,OAAO,KAAMA,IAAI,GAAM;AAChC,oBAAK,KAAK,aAAa,KAAK,kBAAmB;AAC9C,yBAAO,QAAS,MAAM,SAAS,GAAI;AAAA,gBACpC;AAAA,cACD;AACA,qBAAO;AAAA,YACR;AAAA;AAAA;AAAA,YAGA,SAAU,MAAM,SAAS,KAAM;AAC9B,kBAAI,UAAU,YACb,WAAW,CAAE,SAAS,QAAS;AAGhC,kBAAK,KAAM;AACV,uBAAU,OAAO,KAAMA,IAAI,GAAM;AAChC,sBAAK,KAAK,aAAa,KAAK,kBAAmB;AAC9C,wBAAK,QAAS,MAAM,SAAS,GAAI,GAAI;AACpC,6BAAO;AAAA,oBACR;AAAA,kBACD;AAAA,gBACD;AAAA,cACD,OAAO;AACN,uBAAU,OAAO,KAAMA,IAAI,GAAM;AAChC,sBAAK,KAAK,aAAa,KAAK,kBAAmB;AAC9C,iCAAa,KAAM,OAAQ,MAAO,KAAM,OAAQ,IAAI,CAAC;AAErD,wBAAK,QAAQ,SAAU,MAAM,IAAK,GAAI;AACrC,6BAAO,KAAMA,IAAI,KAAK;AAAA,oBACvB,YAAc,WAAW,WAAY,GAAI,MACxC,SAAU,CAAE,MAAM,WAAW,SAAU,CAAE,MAAM,UAAW;AAG1D,6BAAS,SAAU,CAAE,IAAI,SAAU,CAAE;AAAA,oBACtC,OAAO;AAGN,iCAAY,GAAI,IAAI;AAGpB,0BAAO,SAAU,CAAE,IAAI,QAAS,MAAM,SAAS,GAAI,GAAM;AACxD,+BAAO;AAAA,sBACR;AAAA,oBACD;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AACA,qBAAO;AAAA,YACR;AAAA;AAAA,QACF;AAEA,iBAAS,eAAgB,UAAW;AACnC,iBAAO,SAAS,SAAS,IACxB,SAAU,MAAM,SAAS,KAAM;AAC9B,gBAAIH,KAAI,SAAS;AACjB,mBAAQA,MAAM;AACb,kBAAK,CAAC,SAAUA,EAAE,EAAG,MAAM,SAAS,GAAI,GAAI;AAC3C,uBAAO;AAAA,cACR;AAAA,YACD;AACA,mBAAO;AAAA,UACR,IACA,SAAU,CAAE;AAAA,QACd;AAEA,iBAAS,iBAAkB,UAAU,UAAU,SAAU;AACxD,cAAIA,KAAI,GACP,MAAM,SAAS;AAChB,iBAAQA,KAAI,KAAKA,MAAM;AACtB,iBAAM,UAAU,SAAUA,EAAE,GAAG,OAAQ;AAAA,UACxC;AACA,iBAAO;AAAA,QACR;AAEA,iBAAS,SAAU,WAAW,KAAK,QAAQ,SAAS,KAAM;AACzD,cAAI,MACH,eAAe,CAAC,GAChBA,KAAI,GACJ,MAAM,UAAU,QAChB,SAAS,OAAO;AAEjB,iBAAQA,KAAI,KAAKA,MAAM;AACtB,gBAAO,OAAO,UAAWA,EAAE,GAAM;AAChC,kBAAK,CAAC,UAAU,OAAQ,MAAM,SAAS,GAAI,GAAI;AAC9C,6BAAa,KAAM,IAAK;AACxB,oBAAK,QAAS;AACb,sBAAI,KAAMA,EAAE;AAAA,gBACb;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAEA,iBAAS,WAAY,WAAW,UAAU,SAAS,YAAY,YAAY,cAAe;AACzF,cAAK,cAAc,CAAC,WAAY,OAAQ,GAAI;AAC3C,yBAAa,WAAY,UAAW;AAAA,UACrC;AACA,cAAK,cAAc,CAAC,WAAY,OAAQ,GAAI;AAC3C,yBAAa,WAAY,YAAY,YAAa;AAAA,UACnD;AACA,iBAAO,aAAc,SAAU,MAAM,SAAS,SAAS,KAAM;AAC5D,gBAAI,MAAMA,IAAG,MAAM,YAClB,SAAS,CAAC,GACV,UAAU,CAAC,GACX,cAAc,QAAQ,QAGtB,QAAQ,QACP;AAAA,cAAkB,YAAY;AAAA,cAC7B,QAAQ,WAAW,CAAE,OAAQ,IAAI;AAAA,cAAS,CAAC;AAAA,YAAE,GAG/C,YAAY,cAAe,QAAQ,CAAC,YACnC,SAAU,OAAO,QAAQ,WAAW,SAAS,GAAI,IACjD;AAEF,gBAAK,SAAU;AAId,2BAAa,eAAgB,OAAO,YAAY,eAAe;AAAA;AAAA,gBAG9D,CAAC;AAAA;AAAA;AAAA,gBAGD;AAAA;AAGD,sBAAS,WAAW,YAAY,SAAS,GAAI;AAAA,YAC9C,OAAO;AACN,2BAAa;AAAA,YACd;AAGA,gBAAK,YAAa;AACjB,qBAAO,SAAU,YAAY,OAAQ;AACrC,yBAAY,MAAM,CAAC,GAAG,SAAS,GAAI;AAGnC,cAAAA,KAAI,KAAK;AACT,qBAAQA,MAAM;AACb,oBAAO,OAAO,KAAMA,EAAE,GAAM;AAC3B,6BAAY,QAASA,EAAE,CAAE,IAAI,EAAG,UAAW,QAASA,EAAE,CAAE,IAAI;AAAA,gBAC7D;AAAA,cACD;AAAA,YACD;AAEA,gBAAK,MAAO;AACX,kBAAK,cAAc,WAAY;AAC9B,oBAAK,YAAa;AAGjB,yBAAO,CAAC;AACR,kBAAAA,KAAI,WAAW;AACf,yBAAQA,MAAM;AACb,wBAAO,OAAO,WAAYA,EAAE,GAAM;AAGjC,2BAAK,KAAQ,UAAWA,EAAE,IAAI,IAAO;AAAA,oBACtC;AAAA,kBACD;AACA,6BAAY,MAAQ,aAAa,CAAC,GAAK,MAAM,GAAI;AAAA,gBAClD;AAGA,gBAAAA,KAAI,WAAW;AACf,uBAAQA,MAAM;AACb,uBAAO,OAAO,WAAYA,EAAE,OACzB,OAAO,aAAa,QAAQ,KAAM,MAAM,IAAK,IAAI,OAAQA,EAAE,KAAM,IAAK;AAExE,yBAAM,IAAK,IAAI,EAAG,QAAS,IAAK,IAAI;AAAA,kBACrC;AAAA,gBACD;AAAA,cACD;AAAA,YAGD,OAAO;AACN,2BAAa;AAAA,gBACZ,eAAe,UACd,WAAW,OAAQ,aAAa,WAAW,MAAO,IAClD;AAAA,cACF;AACA,kBAAK,YAAa;AACjB,2BAAY,MAAM,SAAS,YAAY,GAAI;AAAA,cAC5C,OAAO;AACN,gBAAAJ,MAAK,MAAO,SAAS,UAAW;AAAA,cACjC;AAAA,YACD;AAAA,UACD,CAAE;AAAA,QACH;AAEA,iBAAS,kBAAmB,QAAS;AACpC,cAAI,cAAc,SAAS,GAC1B,MAAM,OAAO,QACb,kBAAkB,KAAK,SAAU,OAAQ,CAAE,EAAE,IAAK,GAClD,mBAAmB,mBAAmB,KAAK,SAAU,GAAI,GACzDI,KAAI,kBAAkB,IAAI,GAG1B,eAAe,cAAe,SAAU,MAAO;AAC9C,mBAAO,SAAS;AAAA,UACjB,GAAG,kBAAkB,IAAK,GAC1B,kBAAkB,cAAe,SAAU,MAAO;AACjD,mBAAO,QAAQ,KAAM,cAAc,IAAK,IAAI;AAAA,UAC7C,GAAG,kBAAkB,IAAK,GAC1B,WAAW,CAAE,SAAU,MAAM,SAAS,KAAM;AAM3C,gBAAI,MAAQ,CAAC,oBAAqB,OAAO,WAAW,uBACjD,eAAe,SAAU,WAC1B,aAAc,MAAM,SAAS,GAAI,IACjC,gBAAiB,MAAM,SAAS,GAAI;AAItC,2BAAe;AACf,mBAAO;AAAA,UACR,CAAE;AAEH,iBAAQA,KAAI,KAAKA,MAAM;AACtB,gBAAO,UAAU,KAAK,SAAU,OAAQA,EAAE,EAAE,IAAK,GAAM;AACtD,yBAAW,CAAE,cAAe,eAAgB,QAAS,GAAG,OAAQ,CAAE;AAAA,YACnE,OAAO;AACN,wBAAU,KAAK,OAAQ,OAAQA,EAAE,EAAE,IAAK,EAAE,MAAO,MAAM,OAAQA,EAAE,EAAE,OAAQ;AAG3E,kBAAK,QAAS,OAAQ,GAAI;AAGzB,oBAAI,EAAEA;AACN,uBAAQ,IAAI,KAAK,KAAM;AACtB,sBAAK,KAAK,SAAU,OAAQ,CAAE,EAAE,IAAK,GAAI;AACxC;AAAA,kBACD;AAAA,gBACD;AACA,uBAAO;AAAA,kBACNA,KAAI,KAAK,eAAgB,QAAS;AAAA,kBAClCA,KAAI,KAAK;AAAA;AAAA,oBAGR,OAAO,MAAO,GAAGA,KAAI,CAAE,EACrB,OAAQ,EAAE,OAAO,OAAQA,KAAI,CAAE,EAAE,SAAS,MAAM,MAAM,GAAG,CAAE;AAAA,kBAC9D,EAAE,QAAS,UAAU,IAAK;AAAA,kBAC1B;AAAA,kBACAA,KAAI,KAAK,kBAAmB,OAAO,MAAOA,IAAG,CAAE,CAAE;AAAA,kBACjD,IAAI,OAAO,kBAAqB,SAAS,OAAO,MAAO,CAAE,CAAI;AAAA,kBAC7D,IAAI,OAAO,WAAY,MAAO;AAAA,gBAC/B;AAAA,cACD;AACA,uBAAS,KAAM,OAAQ;AAAA,YACxB;AAAA,UACD;AAEA,iBAAO,eAAgB,QAAS;AAAA,QACjC;AAEA,iBAAS,yBAA0B,iBAAiB,aAAc;AACjE,cAAI,QAAQ,YAAY,SAAS,GAChC,YAAY,gBAAgB,SAAS,GACrC,eAAe,SAAU,MAAM,SAAS,KAAK,SAAS,WAAY;AACjE,gBAAI,MAAM,GAAG,SACZ,eAAe,GACfA,KAAI,KACJ,YAAY,QAAQ,CAAC,GACrB,aAAa,CAAC,GACd,gBAAgB,kBAGhB,QAAQ,QAAQ,aAAa,KAAK,KAAK,IAAK,KAAK,SAAU,GAG3D,gBAAkB,WAAW,iBAAiB,OAAO,IAAI,KAAK,OAAO,KAAK,KAC1E,MAAM,MAAM;AAEb,gBAAK,WAAY;AAMhB,iCAAmB,WAAWH,aAAY,WAAW;AAAA,YACtD;AAMA,mBAAQG,OAAM,QAAS,OAAO,MAAOA,EAAE,MAAO,MAAMA,MAAM;AACzD,kBAAK,aAAa,MAAO;AACxB,oBAAI;AAMJ,oBAAK,CAAC,WAAW,KAAK,iBAAiBH,WAAW;AACjD,8BAAa,IAAK;AAClB,wBAAM,CAAC;AAAA,gBACR;AACA,uBAAU,UAAU,gBAAiB,GAAI,GAAM;AAC9C,sBAAK,QAAS,MAAM,WAAWA,WAAU,GAAI,GAAI;AAChD,oBAAAD,MAAK,KAAM,SAAS,IAAK;AACzB;AAAA,kBACD;AAAA,gBACD;AACA,oBAAK,WAAY;AAChB,4BAAU;AAAA,gBACX;AAAA,cACD;AAGA,kBAAK,OAAQ;AAGZ,oBAAO,OAAO,CAAC,WAAW,MAAS;AAClC;AAAA,gBACD;AAGA,oBAAK,MAAO;AACX,4BAAU,KAAM,IAAK;AAAA,gBACtB;AAAA,cACD;AAAA,YACD;AAIA,4BAAgBI;AAShB,gBAAK,SAASA,OAAM,cAAe;AAClC,kBAAI;AACJ,qBAAU,UAAU,YAAa,GAAI,GAAM;AAC1C,wBAAS,WAAW,YAAY,SAAS,GAAI;AAAA,cAC9C;AAEA,kBAAK,MAAO;AAGX,oBAAK,eAAe,GAAI;AACvB,yBAAQA,MAAM;AACb,wBAAK,EAAG,UAAWA,EAAE,KAAK,WAAYA,EAAE,IAAM;AAC7C,iCAAYA,EAAE,IAAI,IAAI,KAAM,OAAQ;AAAA,oBACrC;AAAA,kBACD;AAAA,gBACD;AAGA,6BAAa,SAAU,UAAW;AAAA,cACnC;AAGA,cAAAJ,MAAK,MAAO,SAAS,UAAW;AAGhC,kBAAK,aAAa,CAAC,QAAQ,WAAW,SAAS,KAC5C,eAAe,YAAY,SAAW,GAAI;AAE5C,uBAAO,WAAY,OAAQ;AAAA,cAC5B;AAAA,YACD;AAGA,gBAAK,WAAY;AAChB,wBAAU;AACV,iCAAmB;AAAA,YACpB;AAEA,mBAAO;AAAA,UACR;AAED,iBAAO,QACN,aAAc,YAAa,IAC3B;AAAA,QACF;AAEA,iBAAS,QAAS,UAAU,OAAgC;AAC3D,cAAII,IACH,cAAc,CAAC,GACf,kBAAkB,CAAC,GACnB,SAAS,cAAe,WAAW,GAAI;AAExC,cAAK,CAAC,QAAS;AAGd,gBAAK,CAAC,OAAQ;AACb,sBAAQ,SAAU,QAAS;AAAA,YAC5B;AACA,YAAAA,KAAI,MAAM;AACV,mBAAQA,MAAM;AACb,uBAAS,kBAAmB,MAAOA,EAAE,CAAE;AACvC,kBAAK,OAAQ,OAAQ,GAAI;AACxB,4BAAY,KAAM,MAAO;AAAA,cAC1B,OAAO;AACN,gCAAgB,KAAM,MAAO;AAAA,cAC9B;AAAA,YACD;AAGA,qBAAS;AAAA,cAAe;AAAA,cACvB,yBAA0B,iBAAiB,WAAY;AAAA,YAAE;AAG1D,mBAAO,WAAW;AAAA,UACnB;AACA,iBAAO;AAAA,QACR;AAWA,iBAAS,OAAQ,UAAU,SAAS,SAAS,MAAO;AACnD,cAAIA,IAAG,QAAQ,OAAO,MAAMI,OAC3B,WAAW,OAAO,aAAa,cAAc,UAC7C,QAAQ,CAAC,QAAQ,SAAY,WAAW,SAAS,YAAY,QAAW;AAEzE,oBAAU,WAAW,CAAC;AAItB,cAAK,MAAM,WAAW,GAAI;AAGzB,qBAAS,MAAO,CAAE,IAAI,MAAO,CAAE,EAAE,MAAO,CAAE;AAC1C,gBAAK,OAAO,SAAS,MAAO,QAAQ,OAAQ,CAAE,GAAI,SAAS,QACzD,QAAQ,aAAa,KAAK,kBAAkB,KAAK,SAAU,OAAQ,CAAE,EAAE,IAAK,GAAI;AAEjF,yBAAY,KAAK,KAAK;AAAA,gBACrB,MAAM,QAAS,CAAE,EAAE,QAAS,WAAW,SAAU;AAAA,gBACjD;AAAA,cACD,KAAK,CAAC,GAAK,CAAE;AACb,kBAAK,CAAC,SAAU;AACf,uBAAO;AAAA,cAGR,WAAY,UAAW;AACtB,0BAAU,QAAQ;AAAA,cACnB;AAEA,yBAAW,SAAS,MAAO,OAAO,MAAM,EAAE,MAAM,MAAO;AAAA,YACxD;AAGA,YAAAJ,KAAI,UAAU,aAAa,KAAM,QAAS,IAAI,IAAI,OAAO;AACzD,mBAAQA,MAAM;AACb,sBAAQ,OAAQA,EAAE;AAGlB,kBAAK,KAAK,SAAY,OAAO,MAAM,IAAO,GAAI;AAC7C;AAAA,cACD;AACA,kBAAOI,QAAO,KAAK,KAAM,IAAK,GAAM;AAGnC,oBAAO,OAAOA;AAAA,kBACb,MAAM,QAAS,CAAE,EAAE,QAAS,WAAW,SAAU;AAAA,kBACjD,SAAS,KAAM,OAAQ,CAAE,EAAE,IAAK,KAC/B,YAAa,QAAQ,UAAW,KAAK;AAAA,gBACvC,GAAM;AAGL,yBAAO,OAAQJ,IAAG,CAAE;AACpB,6BAAW,KAAK,UAAU,WAAY,MAAO;AAC7C,sBAAK,CAAC,UAAW;AAChB,oBAAAJ,MAAK,MAAO,SAAS,IAAK;AAC1B,2BAAO;AAAA,kBACR;AAEA;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAIA,WAAE,YAAY,QAAS,UAAU,KAAM;AAAA,YACtC;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD;AAAA,YACA,CAAC,WAAW,SAAS,KAAM,QAAS,KAAK,YAAa,QAAQ,UAAW,KAAK;AAAA,UAC/E;AACA,iBAAO;AAAA,QACR;AAMA,gBAAQ,aAAa,QAAQ,MAAO,EAAG,EAAE,KAAM,SAAU,EAAE,KAAM,EAAG,MAAM;AAG1E,oBAAY;AAIZ,gBAAQ,eAAe,OAAQ,SAAU,IAAK;AAG7C,iBAAO,GAAG,wBAAyBC,UAAS,cAAe,UAAW,CAAE,IAAI;AAAA,QAC7E,CAAE;AAEF,eAAO,OAAO;AAGd,eAAO,KAAM,GAAI,IAAI,OAAO,KAAK;AACjC,eAAO,SAAS,OAAO;AAIvB,aAAK,UAAU;AACf,aAAK,SAAS;AACd,aAAK,cAAc;AACnB,aAAK,WAAW;AAEhB,aAAK,SAAS,OAAO;AACrB,aAAK,UAAU,OAAO;AACtB,aAAK,QAAQ,OAAO;AACpB,aAAK,YAAY,OAAO;AACxB,aAAK,UAAU,OAAO;AACtB,aAAK,aAAa,OAAO;AAAA,MAIzB,GAAI;AAGJ,UAAI,MAAM,SAAU,MAAMM,MAAK,OAAQ;AACtC,YAAI,UAAU,CAAC,GACd,WAAW,UAAU;AAEtB,gBAAU,OAAO,KAAMA,IAAI,MAAO,KAAK,aAAa,GAAI;AACvD,cAAK,KAAK,aAAa,GAAI;AAC1B,gBAAK,YAAY,OAAQ,IAAK,EAAE,GAAI,KAAM,GAAI;AAC7C;AAAA,YACD;AACA,oBAAQ,KAAM,IAAK;AAAA,UACpB;AAAA,QACD;AACA,eAAO;AAAA,MACR;AAGA,UAAI,WAAW,SAAU,GAAG,MAAO;AAClC,YAAI,UAAU,CAAC;AAEf,eAAQ,GAAG,IAAI,EAAE,aAAc;AAC9B,cAAK,EAAE,aAAa,KAAK,MAAM,MAAO;AACrC,oBAAQ,KAAM,CAAE;AAAA,UACjB;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAGA,UAAI,gBAAgB,OAAO,KAAK,MAAM;AAEtC,UAAI,aAAe;AAKnB,eAAS,OAAQ,UAAU,WAAW,KAAM;AAC3C,YAAK,WAAY,SAAU,GAAI;AAC9B,iBAAO,OAAO,KAAM,UAAU,SAAU,MAAM,GAAI;AACjD,mBAAO,CAAC,CAAC,UAAU,KAAM,MAAM,GAAG,IAAK,MAAM;AAAA,UAC9C,CAAE;AAAA,QACH;AAGA,YAAK,UAAU,UAAW;AACzB,iBAAO,OAAO,KAAM,UAAU,SAAU,MAAO;AAC9C,mBAAS,SAAS,cAAgB;AAAA,UACnC,CAAE;AAAA,QACH;AAGA,YAAK,OAAO,cAAc,UAAW;AACpC,iBAAO,OAAO,KAAM,UAAU,SAAU,MAAO;AAC9C,mBAAS,QAAQ,KAAM,WAAW,IAAK,IAAI,OAAS;AAAA,UACrD,CAAE;AAAA,QACH;AAGA,eAAO,OAAO,OAAQ,WAAW,UAAU,GAAI;AAAA,MAChD;AAEA,aAAO,SAAS,SAAU,MAAM,OAAO,KAAM;AAC5C,YAAI,OAAO,MAAO,CAAE;AAEpB,YAAK,KAAM;AACV,iBAAO,UAAU,OAAO;AAAA,QACzB;AAEA,YAAK,MAAM,WAAW,KAAK,KAAK,aAAa,GAAI;AAChD,iBAAO,OAAO,KAAK,gBAAiB,MAAM,IAAK,IAAI,CAAE,IAAK,IAAI,CAAC;AAAA,QAChE;AAEA,eAAO,OAAO,KAAK,QAAS,MAAM,OAAO,KAAM,OAAO,SAAUE,OAAO;AACtE,iBAAOA,MAAK,aAAa;AAAA,QAC1B,CAAE,CAAE;AAAA,MACL;AAEA,aAAO,GAAG,OAAQ;AAAA,QACjB,MAAM,SAAU,UAAW;AAC1B,cAAI,GAAG,KACN,MAAM,KAAK,QACX,OAAO;AAER,cAAK,OAAO,aAAa,UAAW;AACnC,mBAAO,KAAK,UAAW,OAAQ,QAAS,EAAE,OAAQ,WAAW;AAC5D,mBAAM,IAAI,GAAG,IAAI,KAAK,KAAM;AAC3B,oBAAK,OAAO,SAAU,KAAM,CAAE,GAAG,IAAK,GAAI;AACzC,yBAAO;AAAA,gBACR;AAAA,cACD;AAAA,YACD,CAAE,CAAE;AAAA,UACL;AAEA,gBAAM,KAAK,UAAW,CAAC,CAAE;AAEzB,eAAM,IAAI,GAAG,IAAI,KAAK,KAAM;AAC3B,mBAAO,KAAM,UAAU,KAAM,CAAE,GAAG,GAAI;AAAA,UACvC;AAEA,iBAAO,MAAM,IAAI,OAAO,WAAY,GAAI,IAAI;AAAA,QAC7C;AAAA,QACA,QAAQ,SAAU,UAAW;AAC5B,iBAAO,KAAK,UAAW,OAAQ,MAAM,YAAY,CAAC,GAAG,KAAM,CAAE;AAAA,QAC9D;AAAA,QACA,KAAK,SAAU,UAAW;AACzB,iBAAO,KAAK,UAAW,OAAQ,MAAM,YAAY,CAAC,GAAG,IAAK,CAAE;AAAA,QAC7D;AAAA,QACA,IAAI,SAAU,UAAW;AACxB,iBAAO,CAAC,CAAC;AAAA,YACR;AAAA;AAAA;AAAA,YAIA,OAAO,aAAa,YAAY,cAAc,KAAM,QAAS,IAC5D,OAAQ,QAAS,IACjB,YAAY,CAAC;AAAA,YACd;AAAA,UACD,EAAE;AAAA,QACH;AAAA,MACD,CAAE;AAOF,UAAI,YAMH,aAAa,uCAEb,OAAO,OAAO,GAAG,OAAO,SAAU,UAAU,SAAS,MAAO;AAC3D,YAAI,OAAO;AAGX,YAAK,CAAC,UAAW;AAChB,iBAAO;AAAA,QACR;AAIA,eAAO,QAAQ;AAGf,YAAK,OAAO,aAAa,UAAW;AACnC,cAAK,SAAU,CAAE,MAAM,OACtB,SAAU,SAAS,SAAS,CAAE,MAAM,OACpC,SAAS,UAAU,GAAI;AAGvB,oBAAQ,CAAE,MAAM,UAAU,IAAK;AAAA,UAEhC,OAAO;AACN,oBAAQ,WAAW,KAAM,QAAS;AAAA,UACnC;AAGA,cAAK,UAAW,MAAO,CAAE,KAAK,CAAC,UAAY;AAG1C,gBAAK,MAAO,CAAE,GAAI;AACjB,wBAAU,mBAAmB,SAAS,QAAS,CAAE,IAAI;AAIrD,qBAAO,MAAO,MAAM,OAAO;AAAA,gBAC1B,MAAO,CAAE;AAAA,gBACT,WAAW,QAAQ,WAAW,QAAQ,iBAAiB,UAAU;AAAA,gBACjE;AAAA,cACD,CAAE;AAGF,kBAAK,WAAW,KAAM,MAAO,CAAE,CAAE,KAAK,OAAO,cAAe,OAAQ,GAAI;AACvE,qBAAM,SAAS,SAAU;AAGxB,sBAAK,WAAY,KAAM,KAAM,CAAE,GAAI;AAClC,yBAAM,KAAM,EAAG,QAAS,KAAM,CAAE;AAAA,kBAGjC,OAAO;AACN,yBAAK,KAAM,OAAO,QAAS,KAAM,CAAE;AAAA,kBACpC;AAAA,gBACD;AAAA,cACD;AAEA,qBAAO;AAAA,YAGR,OAAO;AACN,qBAAO,SAAS,eAAgB,MAAO,CAAE,CAAE;AAE3C,kBAAK,MAAO;AAGX,qBAAM,CAAE,IAAI;AACZ,qBAAK,SAAS;AAAA,cACf;AACA,qBAAO;AAAA,YACR;AAAA,UAGD,WAAY,CAAC,WAAW,QAAQ,QAAS;AACxC,oBAAS,WAAW,MAAO,KAAM,QAAS;AAAA,UAI3C,OAAO;AACN,mBAAO,KAAK,YAAa,OAAQ,EAAE,KAAM,QAAS;AAAA,UACnD;AAAA,QAGD,WAAY,SAAS,UAAW;AAC/B,eAAM,CAAE,IAAI;AACZ,eAAK,SAAS;AACd,iBAAO;AAAA,QAIR,WAAY,WAAY,QAAS,GAAI;AACpC,iBAAO,KAAK,UAAU,SACrB,KAAK,MAAO,QAAS;AAAA;AAAA,YAGrB,SAAU,MAAO;AAAA;AAAA,QACnB;AAEA,eAAO,OAAO,UAAW,UAAU,IAAK;AAAA,MACzC;AAGD,WAAK,YAAY,OAAO;AAGxB,mBAAa,OAAQ,QAAS;AAG9B,UAAI,eAAe,kCAGlB,mBAAmB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAED,aAAO,GAAG,OAAQ;AAAA,QACjB,KAAK,SAAU,QAAS;AACvB,cAAI,UAAU,OAAQ,QAAQ,IAAK,GAClC,IAAI,QAAQ;AAEb,iBAAO,KAAK,OAAQ,WAAW;AAC9B,gBAAI,IAAI;AACR,mBAAQ,IAAI,GAAG,KAAM;AACpB,kBAAK,OAAO,SAAU,MAAM,QAAS,CAAE,CAAE,GAAI;AAC5C,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD,CAAE;AAAA,QACH;AAAA,QAEA,SAAS,SAAU,WAAW,SAAU;AACvC,cAAI,KACH,IAAI,GACJ,IAAI,KAAK,QACT,UAAU,CAAC,GACX,UAAU,OAAO,cAAc,YAAY,OAAQ,SAAU;AAG9D,cAAK,CAAC,cAAc,KAAM,SAAU,GAAI;AACvC,mBAAQ,IAAI,GAAG,KAAM;AACpB,mBAAM,MAAM,KAAM,CAAE,GAAG,OAAO,QAAQ,SAAS,MAAM,IAAI,YAAa;AAGrE,oBAAK,IAAI,WAAW,OAAQ,UAC3B,QAAQ,MAAO,GAAI,IAAI;AAAA;AAAA,kBAGvB,IAAI,aAAa,KAChB,OAAO,KAAK,gBAAiB,KAAK,SAAU;AAAA,oBAAM;AAEnD,0BAAQ,KAAM,GAAI;AAClB;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,iBAAO,KAAK,UAAW,QAAQ,SAAS,IAAI,OAAO,WAAY,OAAQ,IAAI,OAAQ;AAAA,QACpF;AAAA;AAAA,QAGA,OAAO,SAAU,MAAO;AAGvB,cAAK,CAAC,MAAO;AACZ,mBAAS,KAAM,CAAE,KAAK,KAAM,CAAE,EAAE,aAAe,KAAK,MAAM,EAAE,QAAQ,EAAE,SAAS;AAAA,UAChF;AAGA,cAAK,OAAO,SAAS,UAAW;AAC/B,mBAAO,QAAQ,KAAM,OAAQ,IAAK,GAAG,KAAM,CAAE,CAAE;AAAA,UAChD;AAGA,iBAAO,QAAQ;AAAA,YAAM;AAAA;AAAA,YAGpB,KAAK,SAAS,KAAM,CAAE,IAAI;AAAA,UAC3B;AAAA,QACD;AAAA,QAEA,KAAK,SAAU,UAAU,SAAU;AAClC,iBAAO,KAAK;AAAA,YACX,OAAO;AAAA,cACN,OAAO,MAAO,KAAK,IAAI,GAAG,OAAQ,UAAU,OAAQ,CAAE;AAAA,YACvD;AAAA,UACD;AAAA,QACD;AAAA,QAEA,SAAS,SAAU,UAAW;AAC7B,iBAAO,KAAK;AAAA,YAAK,YAAY,OAC5B,KAAK,aAAa,KAAK,WAAW,OAAQ,QAAS;AAAA,UACpD;AAAA,QACD;AAAA,MACD,CAAE;AAEF,eAAS,QAAS,KAAKF,MAAM;AAC5B,gBAAU,MAAM,IAAKA,IAAI,MAAO,IAAI,aAAa,GAAI;AAAA,QAAC;AACtD,eAAO;AAAA,MACR;AAEA,aAAO,KAAM;AAAA,QACZ,QAAQ,SAAU,MAAO;AACxB,cAAI,SAAS,KAAK;AAClB,iBAAO,UAAU,OAAO,aAAa,KAAK,SAAS;AAAA,QACpD;AAAA,QACA,SAAS,SAAU,MAAO;AACzB,iBAAO,IAAK,MAAM,YAAa;AAAA,QAChC;AAAA,QACA,cAAc,SAAU,MAAM,IAAI,OAAQ;AACzC,iBAAO,IAAK,MAAM,cAAc,KAAM;AAAA,QACvC;AAAA,QACA,MAAM,SAAU,MAAO;AACtB,iBAAO,QAAS,MAAM,aAAc;AAAA,QACrC;AAAA,QACA,MAAM,SAAU,MAAO;AACtB,iBAAO,QAAS,MAAM,iBAAkB;AAAA,QACzC;AAAA,QACA,SAAS,SAAU,MAAO;AACzB,iBAAO,IAAK,MAAM,aAAc;AAAA,QACjC;AAAA,QACA,SAAS,SAAU,MAAO;AACzB,iBAAO,IAAK,MAAM,iBAAkB;AAAA,QACrC;AAAA,QACA,WAAW,SAAU,MAAM,IAAI,OAAQ;AACtC,iBAAO,IAAK,MAAM,eAAe,KAAM;AAAA,QACxC;AAAA,QACA,WAAW,SAAU,MAAM,IAAI,OAAQ;AACtC,iBAAO,IAAK,MAAM,mBAAmB,KAAM;AAAA,QAC5C;AAAA,QACA,UAAU,SAAU,MAAO;AAC1B,iBAAO,UAAY,KAAK,cAAc,CAAC,GAAI,YAAY,IAAK;AAAA,QAC7D;AAAA,QACA,UAAU,SAAU,MAAO;AAC1B,iBAAO,SAAU,KAAK,UAAW;AAAA,QAClC;AAAA,QACA,UAAU,SAAU,MAAO;AAC1B,cAAK,KAAK,mBAAmB;AAAA;AAAA;AAAA,UAK5B,SAAU,KAAK,eAAgB,GAAI;AAEnC,mBAAO,KAAK;AAAA,UACb;AAKA,cAAK,SAAU,MAAM,UAAW,GAAI;AACnC,mBAAO,KAAK,WAAW;AAAA,UACxB;AAEA,iBAAO,OAAO,MAAO,CAAC,GAAG,KAAK,UAAW;AAAA,QAC1C;AAAA,MACD,GAAG,SAAU,MAAM,IAAK;AACvB,eAAO,GAAI,IAAK,IAAI,SAAU,OAAO,UAAW;AAC/C,cAAI,UAAU,OAAO,IAAK,MAAM,IAAI,KAAM;AAE1C,cAAK,KAAK,MAAO,EAAG,MAAM,SAAU;AACnC,uBAAW;AAAA,UACZ;AAEA,cAAK,YAAY,OAAO,aAAa,UAAW;AAC/C,sBAAU,OAAO,OAAQ,UAAU,OAAQ;AAAA,UAC5C;AAEA,cAAK,KAAK,SAAS,GAAI;AAGtB,gBAAK,CAAC,iBAAkB,IAAK,GAAI;AAChC,qBAAO,WAAY,OAAQ;AAAA,YAC5B;AAGA,gBAAK,aAAa,KAAM,IAAK,GAAI;AAChC,sBAAQ,QAAQ;AAAA,YACjB;AAAA,UACD;AAEA,iBAAO,KAAK,UAAW,OAAQ;AAAA,QAChC;AAAA,MACD,CAAE;AACF,UAAI,gBAAkB;AAKtB,eAAS,cAAe,SAAU;AACjC,YAAI,SAAS,CAAC;AACd,eAAO,KAAM,QAAQ,MAAO,aAAc,KAAK,CAAC,GAAG,SAAU,GAAG,MAAO;AACtE,iBAAQ,IAAK,IAAI;AAAA,QAClB,CAAE;AACF,eAAO;AAAA,MACR;AAwBA,aAAO,YAAY,SAAU,SAAU;AAItC,kBAAU,OAAO,YAAY,WAC5B,cAAe,OAAQ,IACvB,OAAO,OAAQ,CAAC,GAAG,OAAQ;AAE5B,YACC,QAGA,QAGA,OAGA,QAGA,OAAO,CAAC,GAGR,QAAQ,CAAC,GAGT,cAAc,IAGd,OAAO,WAAW;AAGjB,mBAAS,UAAU,QAAQ;AAI3B,kBAAQ,SAAS;AACjB,iBAAQ,MAAM,QAAQ,cAAc,IAAK;AACxC,qBAAS,MAAM,MAAM;AACrB,mBAAQ,EAAE,cAAc,KAAK,QAAS;AAGrC,kBAAK,KAAM,WAAY,EAAE,MAAO,OAAQ,CAAE,GAAG,OAAQ,CAAE,CAAE,MAAM,SAC9D,QAAQ,aAAc;AAGtB,8BAAc,KAAK;AACnB,yBAAS;AAAA,cACV;AAAA,YACD;AAAA,UACD;AAGA,cAAK,CAAC,QAAQ,QAAS;AACtB,qBAAS;AAAA,UACV;AAEA,mBAAS;AAGT,cAAK,QAAS;AAGb,gBAAK,QAAS;AACb,qBAAO,CAAC;AAAA,YAGT,OAAO;AACN,qBAAO;AAAA,YACR;AAAA,UACD;AAAA,QACD,GAGA,OAAO;AAAA;AAAA,UAGN,KAAK,WAAW;AACf,gBAAK,MAAO;AAGX,kBAAK,UAAU,CAAC,QAAS;AACxB,8BAAc,KAAK,SAAS;AAC5B,sBAAM,KAAM,MAAO;AAAA,cACpB;AAEA,eAAE,SAAS,IAAK,MAAO;AACtB,uBAAO,KAAM,MAAM,SAAU,GAAG,KAAM;AACrC,sBAAK,WAAY,GAAI,GAAI;AACxB,wBAAK,CAAC,QAAQ,UAAU,CAAC,KAAK,IAAK,GAAI,GAAI;AAC1C,2BAAK,KAAM,GAAI;AAAA,oBAChB;AAAA,kBACD,WAAY,OAAO,IAAI,UAAU,OAAQ,GAAI,MAAM,UAAW;AAG7D,wBAAK,GAAI;AAAA,kBACV;AAAA,gBACD,CAAE;AAAA,cACH,GAAK,SAAU;AAEf,kBAAK,UAAU,CAAC,QAAS;AACxB,qBAAK;AAAA,cACN;AAAA,YACD;AACA,mBAAO;AAAA,UACR;AAAA;AAAA,UAGA,QAAQ,WAAW;AAClB,mBAAO,KAAM,WAAW,SAAU,GAAG,KAAM;AAC1C,kBAAI;AACJ,sBAAU,QAAQ,OAAO,QAAS,KAAK,MAAM,KAAM,KAAM,IAAK;AAC7D,qBAAK,OAAQ,OAAO,CAAE;AAGtB,oBAAK,SAAS,aAAc;AAC3B;AAAA,gBACD;AAAA,cACD;AAAA,YACD,CAAE;AACF,mBAAO;AAAA,UACR;AAAA;AAAA;AAAA,UAIA,KAAK,SAAU,IAAK;AACnB,mBAAO,KACN,OAAO,QAAS,IAAI,IAAK,IAAI,KAC7B,KAAK,SAAS;AAAA,UAChB;AAAA;AAAA,UAGA,OAAO,WAAW;AACjB,gBAAK,MAAO;AACX,qBAAO,CAAC;AAAA,YACT;AACA,mBAAO;AAAA,UACR;AAAA;AAAA;AAAA;AAAA,UAKA,SAAS,WAAW;AACnB,qBAAS,QAAQ,CAAC;AAClB,mBAAO,SAAS;AAChB,mBAAO;AAAA,UACR;AAAA,UACA,UAAU,WAAW;AACpB,mBAAO,CAAC;AAAA,UACT;AAAA;AAAA;AAAA;AAAA,UAKA,MAAM,WAAW;AAChB,qBAAS,QAAQ,CAAC;AAClB,gBAAK,CAAC,UAAU,CAAC,QAAS;AACzB,qBAAO,SAAS;AAAA,YACjB;AACA,mBAAO;AAAA,UACR;AAAA,UACA,QAAQ,WAAW;AAClB,mBAAO,CAAC,CAAC;AAAA,UACV;AAAA;AAAA,UAGA,UAAU,SAAU,SAAS,MAAO;AACnC,gBAAK,CAAC,QAAS;AACd,qBAAO,QAAQ,CAAC;AAChB,qBAAO,CAAE,SAAS,KAAK,QAAQ,KAAK,MAAM,IAAI,IAAK;AACnD,oBAAM,KAAM,IAAK;AACjB,kBAAK,CAAC,QAAS;AACd,qBAAK;AAAA,cACN;AAAA,YACD;AACA,mBAAO;AAAA,UACR;AAAA;AAAA,UAGA,MAAM,WAAW;AAChB,iBAAK,SAAU,MAAM,SAAU;AAC/B,mBAAO;AAAA,UACR;AAAA;AAAA,UAGA,OAAO,WAAW;AACjB,mBAAO,CAAC,CAAC;AAAA,UACV;AAAA,QACD;AAED,eAAO;AAAA,MACR;AAGA,eAAS,SAAU,GAAI;AACtB,eAAO;AAAA,MACR;AACA,eAAS,QAAS,IAAK;AACtB,cAAM;AAAA,MACP;AAEA,eAAS,WAAY,OAAO,SAAS,QAAQ,SAAU;AACtD,YAAI;AAEJ,YAAI;AAGH,cAAK,SAAS,WAAc,SAAS,MAAM,OAAU,GAAI;AACxD,mBAAO,KAAM,KAAM,EAAE,KAAM,OAAQ,EAAE,KAAM,MAAO;AAAA,UAGnD,WAAY,SAAS,WAAc,SAAS,MAAM,IAAO,GAAI;AAC5D,mBAAO,KAAM,OAAO,SAAS,MAAO;AAAA,UAGrC,OAAO;AAKN,oBAAQ,MAAO,QAAW,CAAE,KAAM,EAAE,MAAO,OAAQ,CAAE;AAAA,UACtD;AAAA,QAKD,SAAUG,QAAQ;AAIjB,iBAAO,MAAO,QAAW,CAAEA,MAAM,CAAE;AAAA,QACpC;AAAA,MACD;AAEA,aAAO,OAAQ;AAAA,QAEd,UAAU,SAAU,MAAO;AAC1B,cAAI,SAAS;AAAA;AAAA;AAAA,YAIX;AAAA,cAAE;AAAA,cAAU;AAAA,cAAY,OAAO,UAAW,QAAS;AAAA,cAClD,OAAO,UAAW,QAAS;AAAA,cAAG;AAAA,YAAE;AAAA,YACjC;AAAA,cAAE;AAAA,cAAW;AAAA,cAAQ,OAAO,UAAW,aAAc;AAAA,cACpD,OAAO,UAAW,aAAc;AAAA,cAAG;AAAA,cAAG;AAAA,YAAW;AAAA,YAClD;AAAA,cAAE;AAAA,cAAU;AAAA,cAAQ,OAAO,UAAW,aAAc;AAAA,cACnD,OAAO,UAAW,aAAc;AAAA,cAAG;AAAA,cAAG;AAAA,YAAW;AAAA,UACnD,GACA,QAAQ,WACR,UAAU;AAAA,YACT,OAAO,WAAW;AACjB,qBAAO;AAAA,YACR;AAAA,YACA,QAAQ,WAAW;AAClB,uBAAS,KAAM,SAAU,EAAE,KAAM,SAAU;AAC3C,qBAAO;AAAA,YACR;AAAA,YACA,SAAS,SAAU,IAAK;AACvB,qBAAO,QAAQ,KAAM,MAAM,EAAG;AAAA,YAC/B;AAAA;AAAA,YAGA,MAAM,WAA6C;AAClD,kBAAI,MAAM;AAEV,qBAAO,OAAO,SAAU,SAAU,UAAW;AAC5C,uBAAO,KAAM,QAAQ,SAAU,IAAI,OAAQ;AAG1C,sBAAI,KAAK,WAAY,IAAK,MAAO,CAAE,CAAE,CAAE,KAAK,IAAK,MAAO,CAAE,CAAE;AAK5D,2BAAU,MAAO,CAAE,CAAE,EAAG,WAAW;AAClC,wBAAI,WAAW,MAAM,GAAG,MAAO,MAAM,SAAU;AAC/C,wBAAK,YAAY,WAAY,SAAS,OAAQ,GAAI;AACjD,+BAAS,QAAQ,EACf,SAAU,SAAS,MAAO,EAC1B,KAAM,SAAS,OAAQ,EACvB,KAAM,SAAS,MAAO;AAAA,oBACzB,OAAO;AACN,+BAAU,MAAO,CAAE,IAAI,MAAO;AAAA,wBAC7B;AAAA,wBACA,KAAK,CAAE,QAAS,IAAI;AAAA,sBACrB;AAAA,oBACD;AAAA,kBACD,CAAE;AAAA,gBACH,CAAE;AACF,sBAAM;AAAA,cACP,CAAE,EAAE,QAAQ;AAAA,YACb;AAAA,YACA,MAAM,SAAU,aAAa,YAAY,YAAa;AACrD,kBAAI,WAAW;AACf,uBAAS,QAAS,OAAOC,WAAU,SAAS,SAAU;AACrD,uBAAO,WAAW;AACjB,sBAAI,OAAO,MACV,OAAO,WACP,aAAa,WAAW;AACvB,wBAAI,UAAU;AAKd,wBAAK,QAAQ,UAAW;AACvB;AAAA,oBACD;AAEA,+BAAW,QAAQ,MAAO,MAAM,IAAK;AAIrC,wBAAK,aAAaA,UAAS,QAAQ,GAAI;AACtC,4BAAM,IAAI,UAAW,0BAA2B;AAAA,oBACjD;AAMA,2BAAO;AAAA;AAAA;AAAA,qBAKJ,OAAO,aAAa,YACrB,OAAO,aAAa,eACrB,SAAS;AAGV,wBAAK,WAAY,IAAK,GAAI;AAGzB,0BAAK,SAAU;AACd,6BAAK;AAAA,0BACJ;AAAA,0BACA,QAAS,UAAUA,WAAU,UAAU,OAAQ;AAAA,0BAC/C,QAAS,UAAUA,WAAU,SAAS,OAAQ;AAAA,wBAC/C;AAAA,sBAGD,OAAO;AAGN;AAEA,6BAAK;AAAA,0BACJ;AAAA,0BACA,QAAS,UAAUA,WAAU,UAAU,OAAQ;AAAA,0BAC/C,QAAS,UAAUA,WAAU,SAAS,OAAQ;AAAA,0BAC9C;AAAA,4BAAS;AAAA,4BAAUA;AAAA,4BAAU;AAAA,4BAC5BA,UAAS;AAAA,0BAAW;AAAA,wBACtB;AAAA,sBACD;AAAA,oBAGD,OAAO;AAIN,0BAAK,YAAY,UAAW;AAC3B,+BAAO;AACP,+BAAO,CAAE,QAAS;AAAA,sBACnB;AAIA,uBAAE,WAAWA,UAAS,aAAe,MAAM,IAAK;AAAA,oBACjD;AAAA,kBACD,GAGA,UAAU,UACT,aACA,WAAW;AACV,wBAAI;AACH,iCAAW;AAAA,oBACZ,SAAU,GAAI;AAEb,0BAAK,OAAO,SAAS,eAAgB;AACpC,+BAAO,SAAS;AAAA,0BAAe;AAAA,0BAC9B,QAAQ;AAAA,wBAAM;AAAA,sBAChB;AAKA,0BAAK,QAAQ,KAAK,UAAW;AAI5B,4BAAK,YAAY,SAAU;AAC1B,iCAAO;AACP,iCAAO,CAAE,CAAE;AAAA,wBACZ;AAEA,wBAAAA,UAAS,WAAY,MAAM,IAAK;AAAA,sBACjC;AAAA,oBACD;AAAA,kBACD;AAMF,sBAAK,OAAQ;AACZ,4BAAQ;AAAA,kBACT,OAAO;AAIN,wBAAK,OAAO,SAAS,cAAe;AACnC,8BAAQ,QAAQ,OAAO,SAAS,aAAa;AAAA,oBAM9C,WAAY,OAAO,SAAS,cAAe;AAC1C,8BAAQ,QAAQ,OAAO,SAAS,aAAa;AAAA,oBAC9C;AACA,oBAAAf,QAAO,WAAY,OAAQ;AAAA,kBAC5B;AAAA,gBACD;AAAA,cACD;AAEA,qBAAO,OAAO,SAAU,SAAU,UAAW;AAG5C,uBAAQ,CAAE,EAAG,CAAE,EAAE;AAAA,kBAChB;AAAA,oBACC;AAAA,oBACA;AAAA,oBACA,WAAY,UAAW,IACtB,aACA;AAAA,oBACD,SAAS;AAAA,kBACV;AAAA,gBACD;AAGA,uBAAQ,CAAE,EAAG,CAAE,EAAE;AAAA,kBAChB;AAAA,oBACC;AAAA,oBACA;AAAA,oBACA,WAAY,WAAY,IACvB,cACA;AAAA,kBACF;AAAA,gBACD;AAGA,uBAAQ,CAAE,EAAG,CAAE,EAAE;AAAA,kBAChB;AAAA,oBACC;AAAA,oBACA;AAAA,oBACA,WAAY,UAAW,IACtB,aACA;AAAA,kBACF;AAAA,gBACD;AAAA,cACD,CAAE,EAAE,QAAQ;AAAA,YACb;AAAA;AAAA;AAAA,YAIA,SAAS,SAAU,KAAM;AACxB,qBAAO,OAAO,OAAO,OAAO,OAAQ,KAAK,OAAQ,IAAI;AAAA,YACtD;AAAA,UACD,GACA,WAAW,CAAC;AAGb,iBAAO,KAAM,QAAQ,SAAU,GAAG,OAAQ;AACzC,gBAAI,OAAO,MAAO,CAAE,GACnB,cAAc,MAAO,CAAE;AAKxB,oBAAS,MAAO,CAAE,CAAE,IAAI,KAAK;AAG7B,gBAAK,aAAc;AAClB,mBAAK;AAAA,gBACJ,WAAW;AAIV,0BAAQ;AAAA,gBACT;AAAA;AAAA;AAAA,gBAIA,OAAQ,IAAI,CAAE,EAAG,CAAE,EAAE;AAAA;AAAA;AAAA,gBAIrB,OAAQ,IAAI,CAAE,EAAG,CAAE,EAAE;AAAA;AAAA,gBAGrB,OAAQ,CAAE,EAAG,CAAE,EAAE;AAAA;AAAA,gBAGjB,OAAQ,CAAE,EAAG,CAAE,EAAE;AAAA,cAClB;AAAA,YACD;AAKA,iBAAK,IAAK,MAAO,CAAE,EAAE,IAAK;AAK1B,qBAAU,MAAO,CAAE,CAAE,IAAI,WAAW;AACnC,uBAAU,MAAO,CAAE,IAAI,MAAO,EAAG,SAAS,WAAW,SAAY,MAAM,SAAU;AACjF,qBAAO;AAAA,YACR;AAKA,qBAAU,MAAO,CAAE,IAAI,MAAO,IAAI,KAAK;AAAA,UACxC,CAAE;AAGF,kBAAQ,QAAS,QAAS;AAG1B,cAAK,MAAO;AACX,iBAAK,KAAM,UAAU,QAAS;AAAA,UAC/B;AAGA,iBAAO;AAAA,QACR;AAAA;AAAA,QAGA,MAAM,SAAU,aAAc;AAC7B,cAGC,YAAY,UAAU,QAGtB,IAAI,WAGJ,kBAAkB,MAAO,CAAE,GAC3B,gBAAgB,MAAM,KAAM,SAAU,GAGtC,UAAU,OAAO,SAAS,GAG1B,aAAa,SAAUQ,IAAI;AAC1B,mBAAO,SAAU,OAAQ;AACxB,8BAAiBA,EAAE,IAAI;AACvB,4BAAeA,EAAE,IAAI,UAAU,SAAS,IAAI,MAAM,KAAM,SAAU,IAAI;AACtE,kBAAK,CAAG,EAAE,WAAc;AACvB,wBAAQ,YAAa,iBAAiB,aAAc;AAAA,cACrD;AAAA,YACD;AAAA,UACD;AAGD,cAAK,aAAa,GAAI;AACrB;AAAA,cAAY;AAAA,cAAa,QAAQ,KAAM,WAAY,CAAE,CAAE,EAAE;AAAA,cAAS,QAAQ;AAAA,cACzE,CAAC;AAAA,YAAU;AAGZ,gBAAK,QAAQ,MAAM,MAAM,aACxB,WAAY,cAAe,CAAE,KAAK,cAAe,CAAE,EAAE,IAAK,GAAI;AAE9D,qBAAO,QAAQ,KAAK;AAAA,YACrB;AAAA,UACD;AAGA,iBAAQ,KAAM;AACb,uBAAY,cAAe,CAAE,GAAG,WAAY,CAAE,GAAG,QAAQ,MAAO;AAAA,UACjE;AAEA,iBAAO,QAAQ,QAAQ;AAAA,QACxB;AAAA,MACD,CAAE;AAKF,UAAI,cAAc;AAKlB,aAAO,SAAS,gBAAgB,SAAU,OAAO,YAAa;AAI7D,YAAKR,QAAO,WAAWA,QAAO,QAAQ,QAAQ,SAAS,YAAY,KAAM,MAAM,IAAK,GAAI;AACvF,UAAAA,QAAO,QAAQ;AAAA,YAAM,gCAAgC,MAAM;AAAA,YAC1D,MAAM;AAAA,YAAO;AAAA,UAAW;AAAA,QAC1B;AAAA,MACD;AAKA,aAAO,iBAAiB,SAAU,OAAQ;AACzC,QAAAA,QAAO,WAAY,WAAW;AAC7B,gBAAM;AAAA,QACP,CAAE;AAAA,MACH;AAMA,UAAI,YAAY,OAAO,SAAS;AAEhC,aAAO,GAAG,QAAQ,SAAU,IAAK;AAEhC,kBACE,KAAM,EAAG,EAKT,MAAO,SAAU,OAAQ;AACzB,iBAAO,eAAgB,KAAM;AAAA,QAC9B,CAAE;AAEH,eAAO;AAAA,MACR;AAEA,aAAO,OAAQ;AAAA;AAAA,QAGd,SAAS;AAAA;AAAA;AAAA,QAIT,WAAW;AAAA;AAAA,QAGX,OAAO,SAAU,MAAO;AAGvB,cAAK,SAAS,OAAO,EAAE,OAAO,YAAY,OAAO,SAAU;AAC1D;AAAA,UACD;AAGA,iBAAO,UAAU;AAGjB,cAAK,SAAS,QAAQ,EAAE,OAAO,YAAY,GAAI;AAC9C;AAAA,UACD;AAGA,oBAAU,YAAa,UAAU,CAAE,MAAO,CAAE;AAAA,QAC7C;AAAA,MACD,CAAE;AAEF,aAAO,MAAM,OAAO,UAAU;AAG9B,eAAS,YAAY;AACpB,iBAAS,oBAAqB,oBAAoB,SAAU;AAC5D,QAAAA,QAAO,oBAAqB,QAAQ,SAAU;AAC9C,eAAO,MAAM;AAAA,MACd;AAMA,UAAK,SAAS,eAAe,cAC1B,SAAS,eAAe,aAAa,CAAC,SAAS,gBAAgB,UAAa;AAG9E,QAAAA,QAAO,WAAY,OAAO,KAAM;AAAA,MAEjC,OAAO;AAGN,iBAAS,iBAAkB,oBAAoB,SAAU;AAGzD,QAAAA,QAAO,iBAAkB,QAAQ,SAAU;AAAA,MAC5C;AAOA,UAAI,SAAS,SAAU,OAAO,IAAI,KAAK,OAAO,WAAW,UAAU,KAAM;AACxE,YAAI,IAAI,GACP,MAAM,MAAM,QACZ,OAAO,OAAO;AAGf,YAAK,OAAQ,GAAI,MAAM,UAAW;AACjC,sBAAY;AACZ,eAAM,KAAK,KAAM;AAChB,mBAAQ,OAAO,IAAI,GAAG,IAAK,CAAE,GAAG,MAAM,UAAU,GAAI;AAAA,UACrD;AAAA,QAGD,WAAY,UAAU,QAAY;AACjC,sBAAY;AAEZ,cAAK,CAAC,WAAY,KAAM,GAAI;AAC3B,kBAAM;AAAA,UACP;AAEA,cAAK,MAAO;AAGX,gBAAK,KAAM;AACV,iBAAG,KAAM,OAAO,KAAM;AACtB,mBAAK;AAAA,YAGN,OAAO;AACN,qBAAO;AACP,mBAAK,SAAU,MAAM,MAAMc,QAAQ;AAClC,uBAAO,KAAK,KAAM,OAAQ,IAAK,GAAGA,MAAM;AAAA,cACzC;AAAA,YACD;AAAA,UACD;AAEA,cAAK,IAAK;AACT,mBAAQ,IAAI,KAAK,KAAM;AACtB;AAAA,gBACC,MAAO,CAAE;AAAA,gBAAG;AAAA,gBAAK,MAChB,QACA,MAAM,KAAM,MAAO,CAAE,GAAG,GAAG,GAAI,MAAO,CAAE,GAAG,GAAI,CAAE;AAAA,cACnD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,YAAK,WAAY;AAChB,iBAAO;AAAA,QACR;AAGA,YAAK,MAAO;AACX,iBAAO,GAAG,KAAM,KAAM;AAAA,QACvB;AAEA,eAAO,MAAM,GAAI,MAAO,CAAE,GAAG,GAAI,IAAI;AAAA,MACtC;AAIA,UAAI,YAAY,SACf,aAAa;AAGd,eAAS,WAAY,MAAM,QAAS;AACnC,eAAO,OAAO,YAAY;AAAA,MAC3B;AAKA,eAAS,UAAW,QAAS;AAC5B,eAAO,OAAO,QAAS,WAAW,KAAM,EAAE,QAAS,YAAY,UAAW;AAAA,MAC3E;AACA,UAAI,aAAa,SAAU,OAAQ;AAQlC,eAAO,MAAM,aAAa,KAAK,MAAM,aAAa,KAAK,CAAG,CAAC,MAAM;AAAA,MAClE;AAKA,eAAS,OAAO;AACf,aAAK,UAAU,OAAO,UAAU,KAAK;AAAA,MACtC;AAEA,WAAK,MAAM;AAEX,WAAK,YAAY;AAAA,QAEhB,OAAO,SAAU,OAAQ;AAGxB,cAAI,QAAQ,MAAO,KAAK,OAAQ;AAGhC,cAAK,CAAC,OAAQ;AACb,oBAAQ,CAAC;AAKT,gBAAK,WAAY,KAAM,GAAI;AAI1B,kBAAK,MAAM,UAAW;AACrB,sBAAO,KAAK,OAAQ,IAAI;AAAA,cAKzB,OAAO;AACN,uBAAO,eAAgB,OAAO,KAAK,SAAS;AAAA,kBAC3C;AAAA,kBACA,cAAc;AAAA,gBACf,CAAE;AAAA,cACH;AAAA,YACD;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,QACA,KAAK,SAAU,OAAO,MAAM,OAAQ;AACnC,cAAI,MACH,QAAQ,KAAK,MAAO,KAAM;AAI3B,cAAK,OAAO,SAAS,UAAW;AAC/B,kBAAO,UAAW,IAAK,CAAE,IAAI;AAAA,UAG9B,OAAO;AAGN,iBAAM,QAAQ,MAAO;AACpB,oBAAO,UAAW,IAAK,CAAE,IAAI,KAAM,IAAK;AAAA,YACzC;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAAA,QACA,KAAK,SAAU,OAAO,KAAM;AAC3B,iBAAO,QAAQ,SACd,KAAK,MAAO,KAAM;AAAA;AAAA,YAGlB,MAAO,KAAK,OAAQ,KAAK,MAAO,KAAK,OAAQ,EAAG,UAAW,GAAI,CAAE;AAAA;AAAA,QACnE;AAAA,QACA,QAAQ,SAAU,OAAO,KAAK,OAAQ;AAarC,cAAK,QAAQ,UACP,OAAO,OAAO,QAAQ,YAAc,UAAU,QAAc;AAEjE,mBAAO,KAAK,IAAK,OAAO,GAAI;AAAA,UAC7B;AAQA,eAAK,IAAK,OAAO,KAAK,KAAM;AAI5B,iBAAO,UAAU,SAAY,QAAQ;AAAA,QACtC;AAAA,QACA,QAAQ,SAAU,OAAO,KAAM;AAC9B,cAAI,GACH,QAAQ,MAAO,KAAK,OAAQ;AAE7B,cAAK,UAAU,QAAY;AAC1B;AAAA,UACD;AAEA,cAAK,QAAQ,QAAY;AAGxB,gBAAK,MAAM,QAAS,GAAI,GAAI;AAI3B,oBAAM,IAAI,IAAK,SAAU;AAAA,YAC1B,OAAO;AACN,oBAAM,UAAW,GAAI;AAIrB,oBAAM,OAAO,QACZ,CAAE,GAAI,IACJ,IAAI,MAAO,aAAc,KAAK,CAAC;AAAA,YACnC;AAEA,gBAAI,IAAI;AAER,mBAAQ,KAAM;AACb,qBAAO,MAAO,IAAK,CAAE,CAAE;AAAA,YACxB;AAAA,UACD;AAGA,cAAK,QAAQ,UAAa,OAAO,cAAe,KAAM,GAAI;AAMzD,gBAAK,MAAM,UAAW;AACrB,oBAAO,KAAK,OAAQ,IAAI;AAAA,YACzB,OAAO;AACN,qBAAO,MAAO,KAAK,OAAQ;AAAA,YAC5B;AAAA,UACD;AAAA,QACD;AAAA,QACA,SAAS,SAAU,OAAQ;AAC1B,cAAI,QAAQ,MAAO,KAAK,OAAQ;AAChC,iBAAO,UAAU,UAAa,CAAC,OAAO,cAAe,KAAM;AAAA,QAC5D;AAAA,MACD;AACA,UAAI,WAAW,IAAI,KAAK;AAExB,UAAI,WAAW,IAAI,KAAK;AAcxB,UAAI,SAAS,iCACZ,aAAa;AAEd,eAAS,QAAS,MAAO;AACxB,YAAK,SAAS,QAAS;AACtB,iBAAO;AAAA,QACR;AAEA,YAAK,SAAS,SAAU;AACvB,iBAAO;AAAA,QACR;AAEA,YAAK,SAAS,QAAS;AACtB,iBAAO;AAAA,QACR;AAGA,YAAK,SAAS,CAAC,OAAO,IAAK;AAC1B,iBAAO,CAAC;AAAA,QACT;AAEA,YAAK,OAAO,KAAM,IAAK,GAAI;AAC1B,iBAAO,KAAK,MAAO,IAAK;AAAA,QACzB;AAEA,eAAO;AAAA,MACR;AAEA,eAAS,SAAU,MAAM,KAAK,MAAO;AACpC,YAAI;AAIJ,YAAK,SAAS,UAAa,KAAK,aAAa,GAAI;AAChD,iBAAO,UAAU,IAAI,QAAS,YAAY,KAAM,EAAE,YAAY;AAC9D,iBAAO,KAAK,aAAc,IAAK;AAE/B,cAAK,OAAO,SAAS,UAAW;AAC/B,gBAAI;AACH,qBAAO,QAAS,IAAK;AAAA,YACtB,SAAU,GAAI;AAAA,YAAC;AAGf,qBAAS,IAAK,MAAM,KAAK,IAAK;AAAA,UAC/B,OAAO;AACN,mBAAO;AAAA,UACR;AAAA,QACD;AACA,eAAO;AAAA,MACR;AAEA,aAAO,OAAQ;AAAA,QACd,SAAS,SAAU,MAAO;AACzB,iBAAO,SAAS,QAAS,IAAK,KAAK,SAAS,QAAS,IAAK;AAAA,QAC3D;AAAA,QAEA,MAAM,SAAU,MAAM,MAAM,MAAO;AAClC,iBAAO,SAAS,OAAQ,MAAM,MAAM,IAAK;AAAA,QAC1C;AAAA,QAEA,YAAY,SAAU,MAAM,MAAO;AAClC,mBAAS,OAAQ,MAAM,IAAK;AAAA,QAC7B;AAAA;AAAA;AAAA,QAIA,OAAO,SAAU,MAAM,MAAM,MAAO;AACnC,iBAAO,SAAS,OAAQ,MAAM,MAAM,IAAK;AAAA,QAC1C;AAAA,QAEA,aAAa,SAAU,MAAM,MAAO;AACnC,mBAAS,OAAQ,MAAM,IAAK;AAAA,QAC7B;AAAA,MACD,CAAE;AAEF,aAAO,GAAG,OAAQ;AAAA,QACjB,MAAM,SAAU,KAAK,OAAQ;AAC5B,cAAI,GAAG,MAAM,MACZ,OAAO,KAAM,CAAE,GACf,QAAQ,QAAQ,KAAK;AAGtB,cAAK,QAAQ,QAAY;AACxB,gBAAK,KAAK,QAAS;AAClB,qBAAO,SAAS,IAAK,IAAK;AAE1B,kBAAK,KAAK,aAAa,KAAK,CAAC,SAAS,IAAK,MAAM,cAAe,GAAI;AACnE,oBAAI,MAAM;AACV,uBAAQ,KAAM;AAIb,sBAAK,MAAO,CAAE,GAAI;AACjB,2BAAO,MAAO,CAAE,EAAE;AAClB,wBAAK,KAAK,QAAS,OAAQ,MAAM,GAAI;AACpC,6BAAO,UAAW,KAAK,MAAO,CAAE,CAAE;AAClC,+BAAU,MAAM,MAAM,KAAM,IAAK,CAAE;AAAA,oBACpC;AAAA,kBACD;AAAA,gBACD;AACA,yBAAS,IAAK,MAAM,gBAAgB,IAAK;AAAA,cAC1C;AAAA,YACD;AAEA,mBAAO;AAAA,UACR;AAGA,cAAK,OAAO,QAAQ,UAAW;AAC9B,mBAAO,KAAK,KAAM,WAAW;AAC5B,uBAAS,IAAK,MAAM,GAAI;AAAA,YACzB,CAAE;AAAA,UACH;AAEA,iBAAO,OAAQ,MAAM,SAAUA,QAAQ;AACtC,gBAAIE;AAOJ,gBAAK,QAAQF,WAAU,QAAY;AAIlC,cAAAE,QAAO,SAAS,IAAK,MAAM,GAAI;AAC/B,kBAAKA,UAAS,QAAY;AACzB,uBAAOA;AAAA,cACR;AAIA,cAAAA,QAAO,SAAU,MAAM,GAAI;AAC3B,kBAAKA,UAAS,QAAY;AACzB,uBAAOA;AAAA,cACR;AAGA;AAAA,YACD;AAGA,iBAAK,KAAM,WAAW;AAGrB,uBAAS,IAAK,MAAM,KAAKF,MAAM;AAAA,YAChC,CAAE;AAAA,UACH,GAAG,MAAM,OAAO,UAAU,SAAS,GAAG,MAAM,IAAK;AAAA,QAClD;AAAA,QAEA,YAAY,SAAU,KAAM;AAC3B,iBAAO,KAAK,KAAM,WAAW;AAC5B,qBAAS,OAAQ,MAAM,GAAI;AAAA,UAC5B,CAAE;AAAA,QACH;AAAA,MACD,CAAE;AAGF,aAAO,OAAQ;AAAA,QACd,OAAO,SAAU,MAAM,MAAM,MAAO;AACnC,cAAI;AAEJ,cAAK,MAAO;AACX,oBAAS,QAAQ,QAAS;AAC1B,oBAAQ,SAAS,IAAK,MAAM,IAAK;AAGjC,gBAAK,MAAO;AACX,kBAAK,CAAC,SAAS,MAAM,QAAS,IAAK,GAAI;AACtC,wBAAQ,SAAS,OAAQ,MAAM,MAAM,OAAO,UAAW,IAAK,CAAE;AAAA,cAC/D,OAAO;AACN,sBAAM,KAAM,IAAK;AAAA,cAClB;AAAA,YACD;AACA,mBAAO,SAAS,CAAC;AAAA,UAClB;AAAA,QACD;AAAA,QAEA,SAAS,SAAU,MAAM,MAAO;AAC/B,iBAAO,QAAQ;AAEf,cAAI,QAAQ,OAAO,MAAO,MAAM,IAAK,GACpC,cAAc,MAAM,QACpB,KAAK,MAAM,MAAM,GACjB,QAAQ,OAAO,YAAa,MAAM,IAAK,GACvC,OAAO,WAAW;AACjB,mBAAO,QAAS,MAAM,IAAK;AAAA,UAC5B;AAGD,cAAK,OAAO,cAAe;AAC1B,iBAAK,MAAM,MAAM;AACjB;AAAA,UACD;AAEA,cAAK,IAAK;AAIT,gBAAK,SAAS,MAAO;AACpB,oBAAM,QAAS,YAAa;AAAA,YAC7B;AAGA,mBAAO,MAAM;AACb,eAAG,KAAM,MAAM,MAAM,KAAM;AAAA,UAC5B;AAEA,cAAK,CAAC,eAAe,OAAQ;AAC5B,kBAAM,MAAM,KAAK;AAAA,UAClB;AAAA,QACD;AAAA;AAAA,QAGA,aAAa,SAAU,MAAM,MAAO;AACnC,cAAI,MAAM,OAAO;AACjB,iBAAO,SAAS,IAAK,MAAM,GAAI,KAAK,SAAS,OAAQ,MAAM,KAAK;AAAA,YAC/D,OAAO,OAAO,UAAW,aAAc,EAAE,IAAK,WAAW;AACxD,uBAAS,OAAQ,MAAM,CAAE,OAAO,SAAS,GAAI,CAAE;AAAA,YAChD,CAAE;AAAA,UACH,CAAE;AAAA,QACH;AAAA,MACD,CAAE;AAEF,aAAO,GAAG,OAAQ;AAAA,QACjB,OAAO,SAAU,MAAM,MAAO;AAC7B,cAAI,SAAS;AAEb,cAAK,OAAO,SAAS,UAAW;AAC/B,mBAAO;AACP,mBAAO;AACP;AAAA,UACD;AAEA,cAAK,UAAU,SAAS,QAAS;AAChC,mBAAO,OAAO,MAAO,KAAM,CAAE,GAAG,IAAK;AAAA,UACtC;AAEA,iBAAO,SAAS,SACf,OACA,KAAK,KAAM,WAAW;AACrB,gBAAI,QAAQ,OAAO,MAAO,MAAM,MAAM,IAAK;AAG3C,mBAAO,YAAa,MAAM,IAAK;AAE/B,gBAAK,SAAS,QAAQ,MAAO,CAAE,MAAM,cAAe;AACnD,qBAAO,QAAS,MAAM,IAAK;AAAA,YAC5B;AAAA,UACD,CAAE;AAAA,QACJ;AAAA,QACA,SAAS,SAAU,MAAO;AACzB,iBAAO,KAAK,KAAM,WAAW;AAC5B,mBAAO,QAAS,MAAM,IAAK;AAAA,UAC5B,CAAE;AAAA,QACH;AAAA,QACA,YAAY,SAAU,MAAO;AAC5B,iBAAO,KAAK,MAAO,QAAQ,MAAM,CAAC,CAAE;AAAA,QACrC;AAAA;AAAA;AAAA,QAIA,SAAS,SAAU,MAAM,KAAM;AAC9B,cAAI,KACH,QAAQ,GACR,QAAQ,OAAO,SAAS,GACxB,WAAW,MACX,IAAI,KAAK,QACT,UAAU,WAAW;AACpB,gBAAK,CAAG,EAAE,OAAU;AACnB,oBAAM,YAAa,UAAU,CAAE,QAAS,CAAE;AAAA,YAC3C;AAAA,UACD;AAED,cAAK,OAAO,SAAS,UAAW;AAC/B,kBAAM;AACN,mBAAO;AAAA,UACR;AACA,iBAAO,QAAQ;AAEf,iBAAQ,KAAM;AACb,kBAAM,SAAS,IAAK,SAAU,CAAE,GAAG,OAAO,YAAa;AACvD,gBAAK,OAAO,IAAI,OAAQ;AACvB;AACA,kBAAI,MAAM,IAAK,OAAQ;AAAA,YACxB;AAAA,UACD;AACA,kBAAQ;AACR,iBAAO,MAAM,QAAS,GAAI;AAAA,QAC3B;AAAA,MACD,CAAE;AACF,UAAI,OAAS,sCAAwC;AAErD,UAAI,UAAU,IAAI,OAAQ,mBAAmB,OAAO,eAAe,GAAI;AAGvE,UAAI,YAAY,CAAE,OAAO,SAAS,UAAU,MAAO;AAEnD,UAAI,kBAAkB,SAAS;AAI9B,UAAI,aAAa,SAAU,MAAO;AAChC,eAAO,OAAO,SAAU,KAAK,eAAe,IAAK;AAAA,MAClD,GACA,WAAW,EAAE,UAAU,KAAK;AAO7B,UAAK,gBAAgB,aAAc;AAClC,qBAAa,SAAU,MAAO;AAC7B,iBAAO,OAAO,SAAU,KAAK,eAAe,IAAK,KAChD,KAAK,YAAa,QAAS,MAAM,KAAK;AAAA,QACxC;AAAA,MACD;AACD,UAAI,qBAAqB,SAAU,MAAM,IAAK;AAI5C,eAAO,MAAM;AAGb,eAAO,KAAK,MAAM,YAAY,UAC7B,KAAK,MAAM,YAAY;AAAA;AAAA;AAAA;AAAA,QAMvB,WAAY,IAAK,KAEjB,OAAO,IAAK,MAAM,SAAU,MAAM;AAAA,MACpC;AAID,eAAS,UAAW,MAAM,MAAM,YAAY,OAAQ;AACnD,YAAI,UAAU,OACb,gBAAgB,IAChB,eAAe,QACd,WAAW;AACV,iBAAO,MAAM,IAAI;AAAA,QAClB,IACA,WAAW;AACV,iBAAO,OAAO,IAAK,MAAM,MAAM,EAAG;AAAA,QACnC,GACD,UAAU,aAAa,GACvB,OAAO,cAAc,WAAY,CAAE,MAAO,OAAO,UAAW,IAAK,IAAI,KAAK,OAG1E,gBAAgB,KAAK,aAClB,OAAO,UAAW,IAAK,KAAK,SAAS,QAAQ,CAAC,YAChD,QAAQ,KAAM,OAAO,IAAK,MAAM,IAAK,CAAE;AAEzC,YAAK,iBAAiB,cAAe,CAAE,MAAM,MAAO;AAInD,oBAAU,UAAU;AAGpB,iBAAO,QAAQ,cAAe,CAAE;AAGhC,0BAAgB,CAAC,WAAW;AAE5B,iBAAQ,iBAAkB;AAIzB,mBAAO,MAAO,MAAM,MAAM,gBAAgB,IAAK;AAC/C,iBAAO,IAAI,UAAY,KAAM,QAAQ,aAAa,IAAI,WAAW,SAAW,GAAI;AAC/E,8BAAgB;AAAA,YACjB;AACA,4BAAgB,gBAAgB;AAAA,UAEjC;AAEA,0BAAgB,gBAAgB;AAChC,iBAAO,MAAO,MAAM,MAAM,gBAAgB,IAAK;AAG/C,uBAAa,cAAc,CAAC;AAAA,QAC7B;AAEA,YAAK,YAAa;AACjB,0BAAgB,CAAC,iBAAiB,CAAC,WAAW;AAG9C,qBAAW,WAAY,CAAE,IACxB,iBAAkB,WAAY,CAAE,IAAI,KAAM,WAAY,CAAE,IACxD,CAAC,WAAY,CAAE;AAChB,cAAK,OAAQ;AACZ,kBAAM,OAAO;AACb,kBAAM,QAAQ;AACd,kBAAM,MAAM;AAAA,UACb;AAAA,QACD;AACA,eAAO;AAAA,MACR;AAGA,UAAI,oBAAoB,CAAC;AAEzB,eAAS,kBAAmB,MAAO;AAClC,YAAI,MACH,MAAM,KAAK,eACXG,YAAW,KAAK,UAChB,UAAU,kBAAmBA,SAAS;AAEvC,YAAK,SAAU;AACd,iBAAO;AAAA,QACR;AAEA,eAAO,IAAI,KAAK,YAAa,IAAI,cAAeA,SAAS,CAAE;AAC3D,kBAAU,OAAO,IAAK,MAAM,SAAU;AAEtC,aAAK,WAAW,YAAa,IAAK;AAElC,YAAK,YAAY,QAAS;AACzB,oBAAU;AAAA,QACX;AACA,0BAAmBA,SAAS,IAAI;AAEhC,eAAO;AAAA,MACR;AAEA,eAAS,SAAU,UAAU,MAAO;AACnC,YAAI,SAAS,MACZ,SAAS,CAAC,GACV,QAAQ,GACR,SAAS,SAAS;AAGnB,eAAQ,QAAQ,QAAQ,SAAU;AACjC,iBAAO,SAAU,KAAM;AACvB,cAAK,CAAC,KAAK,OAAQ;AAClB;AAAA,UACD;AAEA,oBAAU,KAAK,MAAM;AACrB,cAAK,MAAO;AAKX,gBAAK,YAAY,QAAS;AACzB,qBAAQ,KAAM,IAAI,SAAS,IAAK,MAAM,SAAU,KAAK;AACrD,kBAAK,CAAC,OAAQ,KAAM,GAAI;AACvB,qBAAK,MAAM,UAAU;AAAA,cACtB;AAAA,YACD;AACA,gBAAK,KAAK,MAAM,YAAY,MAAM,mBAAoB,IAAK,GAAI;AAC9D,qBAAQ,KAAM,IAAI,kBAAmB,IAAK;AAAA,YAC3C;AAAA,UACD,OAAO;AACN,gBAAK,YAAY,QAAS;AACzB,qBAAQ,KAAM,IAAI;AAGlB,uBAAS,IAAK,MAAM,WAAW,OAAQ;AAAA,YACxC;AAAA,UACD;AAAA,QACD;AAGA,aAAM,QAAQ,GAAG,QAAQ,QAAQ,SAAU;AAC1C,cAAK,OAAQ,KAAM,KAAK,MAAO;AAC9B,qBAAU,KAAM,EAAE,MAAM,UAAU,OAAQ,KAAM;AAAA,UACjD;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAEA,aAAO,GAAG,OAAQ;AAAA,QACjB,MAAM,WAAW;AAChB,iBAAO,SAAU,MAAM,IAAK;AAAA,QAC7B;AAAA,QACA,MAAM,WAAW;AAChB,iBAAO,SAAU,IAAK;AAAA,QACvB;AAAA,QACA,QAAQ,SAAU,OAAQ;AACzB,cAAK,OAAO,UAAU,WAAY;AACjC,mBAAO,QAAQ,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,UACxC;AAEA,iBAAO,KAAK,KAAM,WAAW;AAC5B,gBAAK,mBAAoB,IAAK,GAAI;AACjC,qBAAQ,IAAK,EAAE,KAAK;AAAA,YACrB,OAAO;AACN,qBAAQ,IAAK,EAAE,KAAK;AAAA,YACrB;AAAA,UACD,CAAE;AAAA,QACH;AAAA,MACD,CAAE;AACF,UAAI,iBAAmB;AAEvB,UAAI,WAAa;AAEjB,UAAI,cAAgB;AAIpB,OAAE,WAAW;AACZ,YAAI,WAAW,SAAS,uBAAuB,GAC9C,MAAM,SAAS,YAAa,SAAS,cAAe,KAAM,CAAE,GAC5D,QAAQ,SAAS,cAAe,OAAQ;AAMzC,cAAM,aAAc,QAAQ,OAAQ;AACpC,cAAM,aAAc,WAAW,SAAU;AACzC,cAAM,aAAc,QAAQ,GAAI;AAEhC,YAAI,YAAa,KAAM;AAIvB,gBAAQ,aAAa,IAAI,UAAW,IAAK,EAAE,UAAW,IAAK,EAAE,UAAU;AAIvE,YAAI,YAAY;AAChB,gBAAQ,iBAAiB,CAAC,CAAC,IAAI,UAAW,IAAK,EAAE,UAAU;AAK3D,YAAI,YAAY;AAChB,gBAAQ,SAAS,CAAC,CAAC,IAAI;AAAA,MACxB,GAAI;AAIJ,UAAI,UAAU;AAAA;AAAA;AAAA;AAAA,QAKb,OAAO,CAAE,GAAG,WAAW,UAAW;AAAA,QAClC,KAAK,CAAE,GAAG,qBAAqB,qBAAsB;AAAA,QACrD,IAAI,CAAE,GAAG,kBAAkB,kBAAmB;AAAA,QAC9C,IAAI,CAAE,GAAG,sBAAsB,uBAAwB;AAAA,QAEvD,UAAU,CAAE,GAAG,IAAI,EAAG;AAAA,MACvB;AAEA,cAAQ,QAAQ,QAAQ,QAAQ,QAAQ,WAAW,QAAQ,UAAU,QAAQ;AAC7E,cAAQ,KAAK,QAAQ;AAGrB,UAAK,CAAC,QAAQ,QAAS;AACtB,gBAAQ,WAAW,QAAQ,SAAS,CAAE,GAAG,gCAAgC,WAAY;AAAA,MACtF;AAGA,eAAS,OAAQ,SAAS,KAAM;AAI/B,YAAI;AAEJ,YAAK,OAAO,QAAQ,yBAAyB,aAAc;AAC1D,gBAAM,QAAQ,qBAAsB,OAAO,GAAI;AAAA,QAEhD,WAAY,OAAO,QAAQ,qBAAqB,aAAc;AAC7D,gBAAM,QAAQ,iBAAkB,OAAO,GAAI;AAAA,QAE5C,OAAO;AACN,gBAAM,CAAC;AAAA,QACR;AAEA,YAAK,QAAQ,UAAa,OAAO,SAAU,SAAS,GAAI,GAAI;AAC3D,iBAAO,OAAO,MAAO,CAAE,OAAQ,GAAG,GAAI;AAAA,QACvC;AAEA,eAAO;AAAA,MACR;AAIA,eAAS,cAAe,OAAO,aAAc;AAC5C,YAAI,IAAI,GACP,IAAI,MAAM;AAEX,eAAQ,IAAI,GAAG,KAAM;AACpB,mBAAS;AAAA,YACR,MAAO,CAAE;AAAA,YACT;AAAA,YACA,CAAC,eAAe,SAAS,IAAK,YAAa,CAAE,GAAG,YAAa;AAAA,UAC9D;AAAA,QACD;AAAA,MACD;AAGA,UAAI,QAAQ;AAEZ,eAAS,cAAe,OAAO,SAAS,SAAS,WAAW,SAAU;AACrE,YAAI,MAAM,KAAK,KAAK,MAAM,UAAU,GACnC,WAAW,QAAQ,uBAAuB,GAC1C,QAAQ,CAAC,GACT,IAAI,GACJ,IAAI,MAAM;AAEX,eAAQ,IAAI,GAAG,KAAM;AACpB,iBAAO,MAAO,CAAE;AAEhB,cAAK,QAAQ,SAAS,GAAI;AAGzB,gBAAK,OAAQ,IAAK,MAAM,UAAW;AAIlC,qBAAO,MAAO,OAAO,KAAK,WAAW,CAAE,IAAK,IAAI,IAAK;AAAA,YAGtD,WAAY,CAAC,MAAM,KAAM,IAAK,GAAI;AACjC,oBAAM,KAAM,QAAQ,eAAgB,IAAK,CAAE;AAAA,YAG5C,OAAO;AACN,oBAAM,OAAO,SAAS,YAAa,QAAQ,cAAe,KAAM,CAAE;AAGlE,qBAAQ,SAAS,KAAM,IAAK,KAAK,CAAE,IAAI,EAAG,GAAK,CAAE,EAAE,YAAY;AAC/D,qBAAO,QAAS,GAAI,KAAK,QAAQ;AACjC,kBAAI,YAAY,KAAM,CAAE,IAAI,OAAO,cAAe,IAAK,IAAI,KAAM,CAAE;AAGnE,kBAAI,KAAM,CAAE;AACZ,qBAAQ,KAAM;AACb,sBAAM,IAAI;AAAA,cACX;AAIA,qBAAO,MAAO,OAAO,IAAI,UAAW;AAGpC,oBAAM,SAAS;AAGf,kBAAI,cAAc;AAAA,YACnB;AAAA,UACD;AAAA,QACD;AAGA,iBAAS,cAAc;AAEvB,YAAI;AACJ,eAAU,OAAO,MAAO,GAAI,GAAM;AAGjC,cAAK,aAAa,OAAO,QAAS,MAAM,SAAU,IAAI,IAAK;AAC1D,gBAAK,SAAU;AACd,sBAAQ,KAAM,IAAK;AAAA,YACpB;AACA;AAAA,UACD;AAEA,qBAAW,WAAY,IAAK;AAG5B,gBAAM,OAAQ,SAAS,YAAa,IAAK,GAAG,QAAS;AAGrD,cAAK,UAAW;AACf,0BAAe,GAAI;AAAA,UACpB;AAGA,cAAK,SAAU;AACd,gBAAI;AACJ,mBAAU,OAAO,IAAK,GAAI,GAAM;AAC/B,kBAAK,YAAY,KAAM,KAAK,QAAQ,EAAG,GAAI;AAC1C,wBAAQ,KAAM,IAAK;AAAA,cACpB;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAGA,UAAI,iBAAiB;AAErB,eAAS,aAAa;AACrB,eAAO;AAAA,MACR;AAEA,eAAS,cAAc;AACtB,eAAO;AAAA,MACR;AAEA,eAAS,GAAI,MAAM,OAAO,UAAU,MAAM,IAAI,KAAM;AACnD,YAAI,QAAQ;AAGZ,YAAK,OAAO,UAAU,UAAW;AAGhC,cAAK,OAAO,aAAa,UAAW;AAGnC,mBAAO,QAAQ;AACf,uBAAW;AAAA,UACZ;AACA,eAAM,QAAQ,OAAQ;AACrB,eAAI,MAAM,MAAM,UAAU,MAAM,MAAO,IAAK,GAAG,GAAI;AAAA,UACpD;AACA,iBAAO;AAAA,QACR;AAEA,YAAK,QAAQ,QAAQ,MAAM,MAAO;AAGjC,eAAK;AACL,iBAAO,WAAW;AAAA,QACnB,WAAY,MAAM,MAAO;AACxB,cAAK,OAAO,aAAa,UAAW;AAGnC,iBAAK;AACL,mBAAO;AAAA,UACR,OAAO;AAGN,iBAAK;AACL,mBAAO;AACP,uBAAW;AAAA,UACZ;AAAA,QACD;AACA,YAAK,OAAO,OAAQ;AACnB,eAAK;AAAA,QACN,WAAY,CAAC,IAAK;AACjB,iBAAO;AAAA,QACR;AAEA,YAAK,QAAQ,GAAI;AAChB,mBAAS;AACT,eAAK,SAAU,OAAQ;AAGtB,mBAAO,EAAE,IAAK,KAAM;AACpB,mBAAO,OAAO,MAAO,MAAM,SAAU;AAAA,UACtC;AAGA,aAAG,OAAO,OAAO,SAAU,OAAO,OAAO,OAAO;AAAA,QACjD;AACA,eAAO,KAAK,KAAM,WAAW;AAC5B,iBAAO,MAAM,IAAK,MAAM,OAAO,IAAI,MAAM,QAAS;AAAA,QACnD,CAAE;AAAA,MACH;AAMA,aAAO,QAAQ;AAAA,QAEd,QAAQ,CAAC;AAAA,QAET,KAAK,SAAU,MAAM,OAAO,SAAS,MAAM,UAAW;AAErD,cAAI,aAAa,aAAa,KAC7B,QAAQ,GAAG,WACX,SAAS,UAAU,MAAM,YAAY,UACrC,WAAW,SAAS,IAAK,IAAK;AAG/B,cAAK,CAAC,WAAY,IAAK,GAAI;AAC1B;AAAA,UACD;AAGA,cAAK,QAAQ,SAAU;AACtB,0BAAc;AACd,sBAAU,YAAY;AACtB,uBAAW,YAAY;AAAA,UACxB;AAIA,cAAK,UAAW;AACf,mBAAO,KAAK,gBAAiB,iBAAiB,QAAS;AAAA,UACxD;AAGA,cAAK,CAAC,QAAQ,MAAO;AACpB,oBAAQ,OAAO,OAAO;AAAA,UACvB;AAGA,cAAK,EAAG,SAAS,SAAS,SAAW;AACpC,qBAAS,SAAS,SAAS,uBAAO,OAAQ,IAAK;AAAA,UAChD;AACA,cAAK,EAAG,cAAc,SAAS,SAAW;AACzC,0BAAc,SAAS,SAAS,SAAU,GAAI;AAI7C,qBAAO,OAAO,WAAW,eAAe,OAAO,MAAM,cAAc,EAAE,OACpE,OAAO,MAAM,SAAS,MAAO,MAAM,SAAU,IAAI;AAAA,YACnD;AAAA,UACD;AAGA,mBAAU,SAAS,IAAK,MAAO,aAAc,KAAK,CAAE,EAAG;AACvD,cAAI,MAAM;AACV,iBAAQ,KAAM;AACb,kBAAM,eAAe,KAAM,MAAO,CAAE,CAAE,KAAK,CAAC;AAC5C,mBAAO,WAAW,IAAK,CAAE;AACzB,0BAAe,IAAK,CAAE,KAAK,IAAK,MAAO,GAAI,EAAE,KAAK;AAGlD,gBAAK,CAAC,MAAO;AACZ;AAAA,YACD;AAGA,sBAAU,OAAO,MAAM,QAAS,IAAK,KAAK,CAAC;AAG3C,oBAAS,WAAW,QAAQ,eAAe,QAAQ,aAAc;AAGjE,sBAAU,OAAO,MAAM,QAAS,IAAK,KAAK,CAAC;AAG3C,wBAAY,OAAO,OAAQ;AAAA,cAC1B;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,MAAM,QAAQ;AAAA,cACd;AAAA,cACA,cAAc,YAAY,OAAO,KAAK,MAAM,aAAa,KAAM,QAAS;AAAA,cACxE,WAAW,WAAW,KAAM,GAAI;AAAA,YACjC,GAAG,WAAY;AAGf,gBAAK,EAAG,WAAW,OAAQ,IAAK,IAAM;AACrC,yBAAW,OAAQ,IAAK,IAAI,CAAC;AAC7B,uBAAS,gBAAgB;AAGzB,kBAAK,CAAC,QAAQ,SACb,QAAQ,MAAM,KAAM,MAAM,MAAM,YAAY,WAAY,MAAM,OAAQ;AAEtE,oBAAK,KAAK,kBAAmB;AAC5B,uBAAK,iBAAkB,MAAM,WAAY;AAAA,gBAC1C;AAAA,cACD;AAAA,YACD;AAEA,gBAAK,QAAQ,KAAM;AAClB,sBAAQ,IAAI,KAAM,MAAM,SAAU;AAElC,kBAAK,CAAC,UAAU,QAAQ,MAAO;AAC9B,0BAAU,QAAQ,OAAO,QAAQ;AAAA,cAClC;AAAA,YACD;AAGA,gBAAK,UAAW;AACf,uBAAS,OAAQ,SAAS,iBAAiB,GAAG,SAAU;AAAA,YACzD,OAAO;AACN,uBAAS,KAAM,SAAU;AAAA,YAC1B;AAGA,mBAAO,MAAM,OAAQ,IAAK,IAAI;AAAA,UAC/B;AAAA,QAED;AAAA;AAAA,QAGA,QAAQ,SAAU,MAAM,OAAO,SAAS,UAAU,aAAc;AAE/D,cAAI,GAAG,WAAW,KACjB,QAAQ,GAAG,WACX,SAAS,UAAU,MAAM,YAAY,UACrC,WAAW,SAAS,QAAS,IAAK,KAAK,SAAS,IAAK,IAAK;AAE3D,cAAK,CAAC,YAAY,EAAG,SAAS,SAAS,SAAW;AACjD;AAAA,UACD;AAGA,mBAAU,SAAS,IAAK,MAAO,aAAc,KAAK,CAAE,EAAG;AACvD,cAAI,MAAM;AACV,iBAAQ,KAAM;AACb,kBAAM,eAAe,KAAM,MAAO,CAAE,CAAE,KAAK,CAAC;AAC5C,mBAAO,WAAW,IAAK,CAAE;AACzB,0BAAe,IAAK,CAAE,KAAK,IAAK,MAAO,GAAI,EAAE,KAAK;AAGlD,gBAAK,CAAC,MAAO;AACZ,mBAAM,QAAQ,QAAS;AACtB,uBAAO,MAAM,OAAQ,MAAM,OAAO,MAAO,CAAE,GAAG,SAAS,UAAU,IAAK;AAAA,cACvE;AACA;AAAA,YACD;AAEA,sBAAU,OAAO,MAAM,QAAS,IAAK,KAAK,CAAC;AAC3C,oBAAS,WAAW,QAAQ,eAAe,QAAQ,aAAc;AACjE,uBAAW,OAAQ,IAAK,KAAK,CAAC;AAC9B,kBAAM,IAAK,CAAE,KACZ,IAAI,OAAQ,YAAY,WAAW,KAAM,eAAgB,IAAI,SAAU;AAGxE,wBAAY,IAAI,SAAS;AACzB,mBAAQ,KAAM;AACb,0BAAY,SAAU,CAAE;AAExB,mBAAO,eAAe,aAAa,UAAU,cAC1C,CAAC,WAAW,QAAQ,SAAS,UAAU,UACvC,CAAC,OAAO,IAAI,KAAM,UAAU,SAAU,OACtC,CAAC,YAAY,aAAa,UAAU,YACrC,aAAa,QAAQ,UAAU,WAAa;AAC7C,yBAAS,OAAQ,GAAG,CAAE;AAEtB,oBAAK,UAAU,UAAW;AACzB,2BAAS;AAAA,gBACV;AACA,oBAAK,QAAQ,QAAS;AACrB,0BAAQ,OAAO,KAAM,MAAM,SAAU;AAAA,gBACtC;AAAA,cACD;AAAA,YACD;AAIA,gBAAK,aAAa,CAAC,SAAS,QAAS;AACpC,kBAAK,CAAC,QAAQ,YACb,QAAQ,SAAS,KAAM,MAAM,YAAY,SAAS,MAAO,MAAM,OAAQ;AAEvE,uBAAO,YAAa,MAAM,MAAM,SAAS,MAAO;AAAA,cACjD;AAEA,qBAAO,OAAQ,IAAK;AAAA,YACrB;AAAA,UACD;AAGA,cAAK,OAAO,cAAe,MAAO,GAAI;AACrC,qBAAS,OAAQ,MAAM,eAAgB;AAAA,UACxC;AAAA,QACD;AAAA,QAEA,UAAU,SAAU,aAAc;AAEjC,cAAI,GAAG,GAAG,KAAK,SAAS,WAAW,cAClC,OAAO,IAAI,MAAO,UAAU,MAAO,GAGnC,QAAQ,OAAO,MAAM,IAAK,WAAY,GAEtC,YACC,SAAS,IAAK,MAAM,QAAS,KAAK,uBAAO,OAAQ,IAAK,GACpD,MAAM,IAAK,KAAK,CAAC,GACpB,UAAU,OAAO,MAAM,QAAS,MAAM,IAAK,KAAK,CAAC;AAGlD,eAAM,CAAE,IAAI;AAEZ,eAAM,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAM;AACxC,iBAAM,CAAE,IAAI,UAAW,CAAE;AAAA,UAC1B;AAEA,gBAAM,iBAAiB;AAGvB,cAAK,QAAQ,eAAe,QAAQ,YAAY,KAAM,MAAM,KAAM,MAAM,OAAQ;AAC/E;AAAA,UACD;AAGA,yBAAe,OAAO,MAAM,SAAS,KAAM,MAAM,OAAO,QAAS;AAGjE,cAAI;AACJ,kBAAU,UAAU,aAAc,GAAI,MAAO,CAAC,MAAM,qBAAqB,GAAI;AAC5E,kBAAM,gBAAgB,QAAQ;AAE9B,gBAAI;AACJ,oBAAU,YAAY,QAAQ,SAAU,GAAI,MAC3C,CAAC,MAAM,8BAA8B,GAAI;AAIzC,kBAAK,CAAC,MAAM,cAAc,UAAU,cAAc,SACjD,MAAM,WAAW,KAAM,UAAU,SAAU,GAAI;AAE/C,sBAAM,YAAY;AAClB,sBAAM,OAAO,UAAU;AAEvB,wBAAU,OAAO,MAAM,QAAS,UAAU,QAAS,KAAK,CAAC,GAAI,UAC5D,UAAU,SAAU,MAAO,QAAQ,MAAM,IAAK;AAE/C,oBAAK,QAAQ,QAAY;AACxB,uBAAO,MAAM,SAAS,SAAU,OAAQ;AACvC,0BAAM,eAAe;AACrB,0BAAM,gBAAgB;AAAA,kBACvB;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAGA,cAAK,QAAQ,cAAe;AAC3B,oBAAQ,aAAa,KAAM,MAAM,KAAM;AAAA,UACxC;AAEA,iBAAO,MAAM;AAAA,QACd;AAAA,QAEA,UAAU,SAAU,OAAO,UAAW;AACrC,cAAI,GAAG,WAAW,KAAK,iBAAiB,kBACvC,eAAe,CAAC,GAChB,gBAAgB,SAAS,eACzB,MAAM,MAAM;AAGb,cAAK;AAAA;AAAA,UAIJ,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,UAOJ,EAAG,MAAM,SAAS,WAAW,MAAM,UAAU,IAAM;AAEnD,mBAAQ,QAAQ,MAAM,MAAM,IAAI,cAAc,MAAO;AAIpD,kBAAK,IAAI,aAAa,KAAK,EAAG,MAAM,SAAS,WAAW,IAAI,aAAa,OAAS;AACjF,kCAAkB,CAAC;AACnB,mCAAmB,CAAC;AACpB,qBAAM,IAAI,GAAG,IAAI,eAAe,KAAM;AACrC,8BAAY,SAAU,CAAE;AAGxB,wBAAM,UAAU,WAAW;AAE3B,sBAAK,iBAAkB,GAAI,MAAM,QAAY;AAC5C,qCAAkB,GAAI,IAAI,UAAU,eACnC,OAAQ,KAAK,IAAK,EAAE,MAAO,GAAI,IAAI,KACnC,OAAO,KAAM,KAAK,MAAM,MAAM,CAAE,GAAI,CAAE,EAAE;AAAA,kBAC1C;AACA,sBAAK,iBAAkB,GAAI,GAAI;AAC9B,oCAAgB,KAAM,SAAU;AAAA,kBACjC;AAAA,gBACD;AACA,oBAAK,gBAAgB,QAAS;AAC7B,+BAAa,KAAM,EAAE,MAAM,KAAK,UAAU,gBAAgB,CAAE;AAAA,gBAC7D;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAGA,gBAAM;AACN,cAAK,gBAAgB,SAAS,QAAS;AACtC,yBAAa,KAAM,EAAE,MAAM,KAAK,UAAU,SAAS,MAAO,aAAc,EAAE,CAAE;AAAA,UAC7E;AAEA,iBAAO;AAAA,QACR;AAAA,QAEA,SAAS,SAAU,MAAM,MAAO;AAC/B,iBAAO,eAAgB,OAAO,MAAM,WAAW,MAAM;AAAA,YACpD,YAAY;AAAA,YACZ,cAAc;AAAA,YAEd,KAAK,WAAY,IAAK,IACrB,WAAW;AACV,kBAAK,KAAK,eAAgB;AACzB,uBAAO,KAAM,KAAK,aAAc;AAAA,cACjC;AAAA,YACD,IACA,WAAW;AACV,kBAAK,KAAK,eAAgB;AACzB,uBAAO,KAAK,cAAe,IAAK;AAAA,cACjC;AAAA,YACD;AAAA,YAED,KAAK,SAAU,OAAQ;AACtB,qBAAO,eAAgB,MAAM,MAAM;AAAA,gBAClC,YAAY;AAAA,gBACZ,cAAc;AAAA,gBACd,UAAU;AAAA,gBACV;AAAA,cACD,CAAE;AAAA,YACH;AAAA,UACD,CAAE;AAAA,QACH;AAAA,QAEA,KAAK,SAAU,eAAgB;AAC9B,iBAAO,cAAe,OAAO,OAAQ,IACpC,gBACA,IAAI,OAAO,MAAO,aAAc;AAAA,QAClC;AAAA,QAEA,SAAS;AAAA,UACR,MAAM;AAAA;AAAA,YAGL,UAAU;AAAA,UACX;AAAA,UACA,OAAO;AAAA;AAAA,YAGN,OAAO,SAAU,MAAO;AAIvB,kBAAI,KAAK,QAAQ;AAGjB,kBAAK,eAAe,KAAM,GAAG,IAAK,KACjC,GAAG,SAAS,SAAU,IAAI,OAAQ,GAAI;AAGtC,+BAAgB,IAAI,SAAS,IAAK;AAAA,cACnC;AAGA,qBAAO;AAAA,YACR;AAAA,YACA,SAAS,SAAU,MAAO;AAIzB,kBAAI,KAAK,QAAQ;AAGjB,kBAAK,eAAe,KAAM,GAAG,IAAK,KACjC,GAAG,SAAS,SAAU,IAAI,OAAQ,GAAI;AAEtC,+BAAgB,IAAI,OAAQ;AAAA,cAC7B;AAGA,qBAAO;AAAA,YACR;AAAA;AAAA;AAAA,YAIA,UAAU,SAAU,OAAQ;AAC3B,kBAAI,SAAS,MAAM;AACnB,qBAAO,eAAe,KAAM,OAAO,IAAK,KACvC,OAAO,SAAS,SAAU,QAAQ,OAAQ,KAC1C,SAAS,IAAK,QAAQ,OAAQ,KAC9B,SAAU,QAAQ,GAAI;AAAA,YACxB;AAAA,UACD;AAAA,UAEA,cAAc;AAAA,YACb,cAAc,SAAU,OAAQ;AAI/B,kBAAK,MAAM,WAAW,UAAa,MAAM,eAAgB;AACxD,sBAAM,cAAc,cAAc,MAAM;AAAA,cACzC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAMA,eAAS,eAAgB,IAAI,MAAM,SAAU;AAG5C,YAAK,CAAC,SAAU;AACf,cAAK,SAAS,IAAK,IAAI,IAAK,MAAM,QAAY;AAC7C,mBAAO,MAAM,IAAK,IAAI,MAAM,UAAW;AAAA,UACxC;AACA;AAAA,QACD;AAGA,iBAAS,IAAK,IAAI,MAAM,KAAM;AAC9B,eAAO,MAAM,IAAK,IAAI,MAAM;AAAA,UAC3B,WAAW;AAAA,UACX,SAAS,SAAU,OAAQ;AAC1B,gBAAI,QACH,QAAQ,SAAS,IAAK,MAAM,IAAK;AAElC,gBAAO,MAAM,YAAY,KAAO,KAAM,IAAK,GAAI;AAG9C,kBAAK,CAAC,OAAQ;AAKb,wBAAQ,MAAM,KAAM,SAAU;AAC9B,yBAAS,IAAK,MAAM,MAAM,KAAM;AAGhC,qBAAM,IAAK,EAAE;AACb,yBAAS,SAAS,IAAK,MAAM,IAAK;AAClC,yBAAS,IAAK,MAAM,MAAM,KAAM;AAEhC,oBAAK,UAAU,QAAS;AAGvB,wBAAM,yBAAyB;AAC/B,wBAAM,eAAe;AAErB,yBAAO;AAAA,gBACR;AAAA,cAQD,YAAc,OAAO,MAAM,QAAS,IAAK,KAAK,CAAC,GAAI,cAAe;AACjE,sBAAM,gBAAgB;AAAA,cACvB;AAAA,YAID,WAAY,OAAQ;AAGnB,uBAAS,IAAK,MAAM,MAAM,OAAO,MAAM;AAAA,gBACtC,MAAO,CAAE;AAAA,gBACT,MAAM,MAAO,CAAE;AAAA,gBACf;AAAA,cACD,CAAE;AAUF,oBAAM,gBAAgB;AACtB,oBAAM,gCAAgC;AAAA,YACvC;AAAA,UACD;AAAA,QACD,CAAE;AAAA,MACH;AAEA,aAAO,cAAc,SAAU,MAAM,MAAM,QAAS;AAGnD,YAAK,KAAK,qBAAsB;AAC/B,eAAK,oBAAqB,MAAM,MAAO;AAAA,QACxC;AAAA,MACD;AAEA,aAAO,QAAQ,SAAU,KAAK,OAAQ;AAGrC,YAAK,EAAG,gBAAgB,OAAO,QAAU;AACxC,iBAAO,IAAI,OAAO,MAAO,KAAK,KAAM;AAAA,QACrC;AAGA,YAAK,OAAO,IAAI,MAAO;AACtB,eAAK,gBAAgB;AACrB,eAAK,OAAO,IAAI;AAIhB,eAAK,qBAAqB,IAAI,oBAC5B,IAAI,qBAAqB;AAAA,UAGzB,IAAI,gBAAgB,QACrB,aACA;AAKD,eAAK,SAAW,IAAI,UAAU,IAAI,OAAO,aAAa,IACrD,IAAI,OAAO,aACX,IAAI;AAEL,eAAK,gBAAgB,IAAI;AACzB,eAAK,gBAAgB,IAAI;AAAA,QAG1B,OAAO;AACN,eAAK,OAAO;AAAA,QACb;AAGA,YAAK,OAAQ;AACZ,iBAAO,OAAQ,MAAM,KAAM;AAAA,QAC5B;AAGA,aAAK,YAAY,OAAO,IAAI,aAAa,KAAK,IAAI;AAGlD,aAAM,OAAO,OAAQ,IAAI;AAAA,MAC1B;AAIA,aAAO,MAAM,YAAY;AAAA,QACxB,aAAa,OAAO;AAAA,QACpB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,+BAA+B;AAAA,QAC/B,aAAa;AAAA,QAEb,gBAAgB,WAAW;AAC1B,cAAI,IAAI,KAAK;AAEb,eAAK,qBAAqB;AAE1B,cAAK,KAAK,CAAC,KAAK,aAAc;AAC7B,cAAE,eAAe;AAAA,UAClB;AAAA,QACD;AAAA,QACA,iBAAiB,WAAW;AAC3B,cAAI,IAAI,KAAK;AAEb,eAAK,uBAAuB;AAE5B,cAAK,KAAK,CAAC,KAAK,aAAc;AAC7B,cAAE,gBAAgB;AAAA,UACnB;AAAA,QACD;AAAA,QACA,0BAA0B,WAAW;AACpC,cAAI,IAAI,KAAK;AAEb,eAAK,gCAAgC;AAErC,cAAK,KAAK,CAAC,KAAK,aAAc;AAC7B,cAAE,yBAAyB;AAAA,UAC5B;AAEA,eAAK,gBAAgB;AAAA,QACtB;AAAA,MACD;AAGA,aAAO,KAAM;AAAA,QACZ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,UAAU;AAAA,QACV,KAAK;AAAA,QACL,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,WAAW;AAAA,QACX,aAAa;AAAA,QACb,SAAS;AAAA,QACT,SAAS;AAAA,QACT,eAAe;AAAA,QACf,WAAW;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,MACR,GAAG,OAAO,MAAM,OAAQ;AAExB,aAAO,KAAM,EAAE,OAAO,WAAW,MAAM,WAAW,GAAG,SAAU,MAAM,cAAe;AAEnF,iBAAS,mBAAoB,aAAc;AAC1C,cAAK,SAAS,cAAe;AAS5B,gBAAI,SAAS,SAAS,IAAK,MAAM,QAAS,GACzC,QAAQ,OAAO,MAAM,IAAK,WAAY;AACvC,kBAAM,OAAO,YAAY,SAAS,YAAY,UAAU;AACxD,kBAAM,cAAc;AAGpB,mBAAQ,WAAY;AAMpB,gBAAK,MAAM,WAAW,MAAM,eAAgB;AAK3C,qBAAQ,KAAM;AAAA,YACf;AAAA,UACD,OAAO;AAIN,mBAAO,MAAM;AAAA,cAAU;AAAA,cAAc,YAAY;AAAA,cAChD,OAAO,MAAM,IAAK,WAAY;AAAA,YAAE;AAAA,UAClC;AAAA,QACD;AAEA,eAAO,MAAM,QAAS,IAAK,IAAI;AAAA;AAAA,UAG9B,OAAO,WAAW;AAEjB,gBAAI;AAKJ,2BAAgB,MAAM,MAAM,IAAK;AAEjC,gBAAK,SAAS,cAAe;AAM5B,yBAAW,SAAS,IAAK,MAAM,YAAa;AAC5C,kBAAK,CAAC,UAAW;AAChB,qBAAK,iBAAkB,cAAc,kBAAmB;AAAA,cACzD;AACA,uBAAS,IAAK,MAAM,eAAgB,YAAY,KAAM,CAAE;AAAA,YACzD,OAAO;AAGN,qBAAO;AAAA,YACR;AAAA,UACD;AAAA,UACA,SAAS,WAAW;AAGnB,2BAAgB,MAAM,IAAK;AAG3B,mBAAO;AAAA,UACR;AAAA,UAEA,UAAU,WAAW;AACpB,gBAAI;AAEJ,gBAAK,SAAS,cAAe;AAC5B,yBAAW,SAAS,IAAK,MAAM,YAAa,IAAI;AAChD,kBAAK,CAAC,UAAW;AAChB,qBAAK,oBAAqB,cAAc,kBAAmB;AAC3D,yBAAS,OAAQ,MAAM,YAAa;AAAA,cACrC,OAAO;AACN,yBAAS,IAAK,MAAM,cAAc,QAAS;AAAA,cAC5C;AAAA,YACD,OAAO;AAGN,qBAAO;AAAA,YACR;AAAA,UACD;AAAA;AAAA;AAAA,UAIA,UAAU,SAAU,OAAQ;AAC3B,mBAAO,SAAS,IAAK,MAAM,QAAQ,IAAK;AAAA,UACzC;AAAA,UAEA;AAAA,QACD;AAcA,eAAO,MAAM,QAAS,YAAa,IAAI;AAAA,UACtC,OAAO,WAAW;AAIjB,gBAAI,MAAM,KAAK,iBAAiB,KAAK,YAAY,MAChD,aAAa,SAAS,eAAe,OAAO,KAC5C,WAAW,SAAS,IAAK,YAAY,YAAa;AAMnD,gBAAK,CAAC,UAAW;AAChB,kBAAK,SAAS,cAAe;AAC5B,qBAAK,iBAAkB,cAAc,kBAAmB;AAAA,cACzD,OAAO;AACN,oBAAI,iBAAkB,MAAM,oBAAoB,IAAK;AAAA,cACtD;AAAA,YACD;AACA,qBAAS,IAAK,YAAY,eAAgB,YAAY,KAAM,CAAE;AAAA,UAC/D;AAAA,UACA,UAAU,WAAW;AACpB,gBAAI,MAAM,KAAK,iBAAiB,KAAK,YAAY,MAChD,aAAa,SAAS,eAAe,OAAO,KAC5C,WAAW,SAAS,IAAK,YAAY,YAAa,IAAI;AAEvD,gBAAK,CAAC,UAAW;AAChB,kBAAK,SAAS,cAAe;AAC5B,qBAAK,oBAAqB,cAAc,kBAAmB;AAAA,cAC5D,OAAO;AACN,oBAAI,oBAAqB,MAAM,oBAAoB,IAAK;AAAA,cACzD;AACA,uBAAS,OAAQ,YAAY,YAAa;AAAA,YAC3C,OAAO;AACN,uBAAS,IAAK,YAAY,cAAc,QAAS;AAAA,YAClD;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAE;AAUF,aAAO,KAAM;AAAA,QACZ,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,cAAc;AAAA,MACf,GAAG,SAAU,MAAM,KAAM;AACxB,eAAO,MAAM,QAAS,IAAK,IAAI;AAAA,UAC9B,cAAc;AAAA,UACd,UAAU;AAAA,UAEV,QAAQ,SAAU,OAAQ;AACzB,gBAAI,KACH,SAAS,MACT,UAAU,MAAM,eAChB,YAAY,MAAM;AAInB,gBAAK,CAAC,WAAa,YAAY,UAAU,CAAC,OAAO,SAAU,QAAQ,OAAQ,GAAM;AAChF,oBAAM,OAAO,UAAU;AACvB,oBAAM,UAAU,QAAQ,MAAO,MAAM,SAAU;AAC/C,oBAAM,OAAO;AAAA,YACd;AACA,mBAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD,CAAE;AAEF,aAAO,GAAG,OAAQ;AAAA,QAEjB,IAAI,SAAU,OAAO,UAAU,MAAM,IAAK;AACzC,iBAAO,GAAI,MAAM,OAAO,UAAU,MAAM,EAAG;AAAA,QAC5C;AAAA,QACA,KAAK,SAAU,OAAO,UAAU,MAAM,IAAK;AAC1C,iBAAO,GAAI,MAAM,OAAO,UAAU,MAAM,IAAI,CAAE;AAAA,QAC/C;AAAA,QACA,KAAK,SAAU,OAAO,UAAU,IAAK;AACpC,cAAI,WAAW;AACf,cAAK,SAAS,MAAM,kBAAkB,MAAM,WAAY;AAGvD,wBAAY,MAAM;AAClB,mBAAQ,MAAM,cAAe,EAAE;AAAA,cAC9B,UAAU,YACT,UAAU,WAAW,MAAM,UAAU,YACrC,UAAU;AAAA,cACX,UAAU;AAAA,cACV,UAAU;AAAA,YACX;AACA,mBAAO;AAAA,UACR;AACA,cAAK,OAAO,UAAU,UAAW;AAGhC,iBAAM,QAAQ,OAAQ;AACrB,mBAAK,IAAK,MAAM,UAAU,MAAO,IAAK,CAAE;AAAA,YACzC;AACA,mBAAO;AAAA,UACR;AACA,cAAK,aAAa,SAAS,OAAO,aAAa,YAAa;AAG3D,iBAAK;AACL,uBAAW;AAAA,UACZ;AACA,cAAK,OAAO,OAAQ;AACnB,iBAAK;AAAA,UACN;AACA,iBAAO,KAAK,KAAM,WAAW;AAC5B,mBAAO,MAAM,OAAQ,MAAM,OAAO,IAAI,QAAS;AAAA,UAChD,CAAE;AAAA,QACH;AAAA,MACD,CAAE;AAGF,UAKC,eAAe,yBAGf,WAAW,qCAEX,eAAe;AAGhB,eAAS,mBAAoB,MAAM,SAAU;AAC5C,YAAK,SAAU,MAAM,OAAQ,KAC5B,SAAU,QAAQ,aAAa,KAAK,UAAU,QAAQ,YAAY,IAAK,GAAI;AAE3E,iBAAO,OAAQ,IAAK,EAAE,SAAU,OAAQ,EAAG,CAAE,KAAK;AAAA,QACnD;AAEA,eAAO;AAAA,MACR;AAGA,eAAS,cAAe,MAAO;AAC9B,aAAK,QAAS,KAAK,aAAc,MAAO,MAAM,QAAS,MAAM,KAAK;AAClE,eAAO;AAAA,MACR;AACA,eAAS,cAAe,MAAO;AAC9B,aAAO,KAAK,QAAQ,IAAK,MAAO,GAAG,CAAE,MAAM,SAAU;AACpD,eAAK,OAAO,KAAK,KAAK,MAAO,CAAE;AAAA,QAChC,OAAO;AACN,eAAK,gBAAiB,MAAO;AAAA,QAC9B;AAEA,eAAO;AAAA,MACR;AAEA,eAAS,eAAgB,KAAK,MAAO;AACpC,YAAI,GAAG,GAAG,MAAM,UAAU,UAAU,UAAU;AAE9C,YAAK,KAAK,aAAa,GAAI;AAC1B;AAAA,QACD;AAGA,YAAK,SAAS,QAAS,GAAI,GAAI;AAC9B,qBAAW,SAAS,IAAK,GAAI;AAC7B,mBAAS,SAAS;AAElB,cAAK,QAAS;AACb,qBAAS,OAAQ,MAAM,eAAgB;AAEvC,iBAAM,QAAQ,QAAS;AACtB,mBAAM,IAAI,GAAG,IAAI,OAAQ,IAAK,EAAE,QAAQ,IAAI,GAAG,KAAM;AACpD,uBAAO,MAAM,IAAK,MAAM,MAAM,OAAQ,IAAK,EAAG,CAAE,CAAE;AAAA,cACnD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAGA,YAAK,SAAS,QAAS,GAAI,GAAI;AAC9B,qBAAW,SAAS,OAAQ,GAAI;AAChC,qBAAW,OAAO,OAAQ,CAAC,GAAG,QAAS;AAEvC,mBAAS,IAAK,MAAM,QAAS;AAAA,QAC9B;AAAA,MACD;AAGA,eAAS,SAAU,KAAK,MAAO;AAC9B,YAAIA,YAAW,KAAK,SAAS,YAAY;AAGzC,YAAKA,cAAa,WAAW,eAAe,KAAM,IAAI,IAAK,GAAI;AAC9D,eAAK,UAAU,IAAI;AAAA,QAGpB,WAAYA,cAAa,WAAWA,cAAa,YAAa;AAC7D,eAAK,eAAe,IAAI;AAAA,QACzB;AAAA,MACD;AAEA,eAAS,SAAU,YAAY,MAAM,UAAU,SAAU;AAGxD,eAAO,KAAM,IAAK;AAElB,YAAI,UAAU,OAAO,SAAS,YAAY,MAAM,KAC/C,IAAI,GACJ,IAAI,WAAW,QACf,WAAW,IAAI,GACf,QAAQ,KAAM,CAAE,GAChB,kBAAkB,WAAY,KAAM;AAGrC,YAAK,mBACD,IAAI,KAAK,OAAO,UAAU,YAC3B,CAAC,QAAQ,cAAc,SAAS,KAAM,KAAM,GAAM;AACpD,iBAAO,WAAW,KAAM,SAAU,OAAQ;AACzC,gBAAI,OAAO,WAAW,GAAI,KAAM;AAChC,gBAAK,iBAAkB;AACtB,mBAAM,CAAE,IAAI,MAAM,KAAM,MAAM,OAAO,KAAK,KAAK,CAAE;AAAA,YAClD;AACA,qBAAU,MAAM,MAAM,UAAU,OAAQ;AAAA,UACzC,CAAE;AAAA,QACH;AAEA,YAAK,GAAI;AACR,qBAAW,cAAe,MAAM,WAAY,CAAE,EAAE,eAAe,OAAO,YAAY,OAAQ;AAC1F,kBAAQ,SAAS;AAEjB,cAAK,SAAS,WAAW,WAAW,GAAI;AACvC,uBAAW;AAAA,UACZ;AAGA,cAAK,SAAS,SAAU;AACvB,sBAAU,OAAO,IAAK,OAAQ,UAAU,QAAS,GAAG,aAAc;AAClE,yBAAa,QAAQ;AAKrB,mBAAQ,IAAI,GAAG,KAAM;AACpB,qBAAO;AAEP,kBAAK,MAAM,UAAW;AACrB,uBAAO,OAAO,MAAO,MAAM,MAAM,IAAK;AAGtC,oBAAK,YAAa;AAIjB,yBAAO,MAAO,SAAS,OAAQ,MAAM,QAAS,CAAE;AAAA,gBACjD;AAAA,cACD;AAEA,uBAAS,KAAM,WAAY,CAAE,GAAG,MAAM,CAAE;AAAA,YACzC;AAEA,gBAAK,YAAa;AACjB,oBAAM,QAAS,QAAQ,SAAS,CAAE,EAAE;AAGpC,qBAAO,IAAK,SAAS,aAAc;AAGnC,mBAAM,IAAI,GAAG,IAAI,YAAY,KAAM;AAClC,uBAAO,QAAS,CAAE;AAClB,oBAAK,YAAY,KAAM,KAAK,QAAQ,EAAG,KACtC,CAAC,SAAS,OAAQ,MAAM,YAAa,KACrC,OAAO,SAAU,KAAK,IAAK,GAAI;AAE/B,sBAAK,KAAK,QAAS,KAAK,QAAQ,IAAK,YAAY,MAAO,UAAW;AAGlE,wBAAK,OAAO,YAAY,CAAC,KAAK,UAAW;AACxC,6BAAO,SAAU,KAAK,KAAK;AAAA,wBAC1B,OAAO,KAAK,SAAS,KAAK,aAAc,OAAQ;AAAA,sBACjD,GAAG,GAAI;AAAA,oBACR;AAAA,kBACD,OAAO;AAON,4BAAS,KAAK,YAAY,QAAS,cAAc,EAAG,GAAG,MAAM,GAAI;AAAA,kBAClE;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAEA,eAAS,OAAQ,MAAM,UAAU,UAAW;AAC3C,YAAI,MACH,QAAQ,WAAW,OAAO,OAAQ,UAAU,IAAK,IAAI,MACrD,IAAI;AAEL,gBAAU,OAAO,MAAO,CAAE,MAAO,MAAM,KAAM;AAC5C,cAAK,CAAC,YAAY,KAAK,aAAa,GAAI;AACvC,mBAAO,UAAW,OAAQ,IAAK,CAAE;AAAA,UAClC;AAEA,cAAK,KAAK,YAAa;AACtB,gBAAK,YAAY,WAAY,IAAK,GAAI;AACrC,4BAAe,OAAQ,MAAM,QAAS,CAAE;AAAA,YACzC;AACA,iBAAK,WAAW,YAAa,IAAK;AAAA,UACnC;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAEA,aAAO,OAAQ;AAAA,QACd,eAAe,SAAU,MAAO;AAC/B,iBAAO;AAAA,QACR;AAAA,QAEA,OAAO,SAAU,MAAM,eAAe,mBAAoB;AACzD,cAAI,GAAG,GAAG,aAAa,cACtB,QAAQ,KAAK,UAAW,IAAK,GAC7B,SAAS,WAAY,IAAK;AAG3B,cAAK,CAAC,QAAQ,mBAAoB,KAAK,aAAa,KAAK,KAAK,aAAa,OACzE,CAAC,OAAO,SAAU,IAAK,GAAI;AAI5B,2BAAe,OAAQ,KAAM;AAC7B,0BAAc,OAAQ,IAAK;AAE3B,iBAAM,IAAI,GAAG,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAM;AACjD,uBAAU,YAAa,CAAE,GAAG,aAAc,CAAE,CAAE;AAAA,YAC/C;AAAA,UACD;AAGA,cAAK,eAAgB;AACpB,gBAAK,mBAAoB;AACxB,4BAAc,eAAe,OAAQ,IAAK;AAC1C,6BAAe,gBAAgB,OAAQ,KAAM;AAE7C,mBAAM,IAAI,GAAG,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAM;AACjD,+BAAgB,YAAa,CAAE,GAAG,aAAc,CAAE,CAAE;AAAA,cACrD;AAAA,YACD,OAAO;AACN,6BAAgB,MAAM,KAAM;AAAA,YAC7B;AAAA,UACD;AAGA,yBAAe,OAAQ,OAAO,QAAS;AACvC,cAAK,aAAa,SAAS,GAAI;AAC9B,0BAAe,cAAc,CAAC,UAAU,OAAQ,MAAM,QAAS,CAAE;AAAA,UAClE;AAGA,iBAAO;AAAA,QACR;AAAA,QAEA,WAAW,SAAU,OAAQ;AAC5B,cAAI,MAAM,MAAM,MACf,UAAU,OAAO,MAAM,SACvB,IAAI;AAEL,kBAAU,OAAO,MAAO,CAAE,OAAQ,QAAW,KAAM;AAClD,gBAAK,WAAY,IAAK,GAAI;AACzB,kBAAO,OAAO,KAAM,SAAS,OAAQ,GAAM;AAC1C,oBAAK,KAAK,QAAS;AAClB,uBAAM,QAAQ,KAAK,QAAS;AAC3B,wBAAK,QAAS,IAAK,GAAI;AACtB,6BAAO,MAAM,OAAQ,MAAM,IAAK;AAAA,oBAGjC,OAAO;AACN,6BAAO,YAAa,MAAM,MAAM,KAAK,MAAO;AAAA,oBAC7C;AAAA,kBACD;AAAA,gBACD;AAIA,qBAAM,SAAS,OAAQ,IAAI;AAAA,cAC5B;AACA,kBAAK,KAAM,SAAS,OAAQ,GAAI;AAI/B,qBAAM,SAAS,OAAQ,IAAI;AAAA,cAC5B;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAE;AAEF,aAAO,GAAG,OAAQ;AAAA,QACjB,QAAQ,SAAU,UAAW;AAC5B,iBAAO,OAAQ,MAAM,UAAU,IAAK;AAAA,QACrC;AAAA,QAEA,QAAQ,SAAU,UAAW;AAC5B,iBAAO,OAAQ,MAAM,QAAS;AAAA,QAC/B;AAAA,QAEA,MAAM,SAAU,OAAQ;AACvB,iBAAO,OAAQ,MAAM,SAAUH,QAAQ;AACtC,mBAAOA,WAAU,SAChB,OAAO,KAAM,IAAK,IAClB,KAAK,MAAM,EAAE,KAAM,WAAW;AAC7B,kBAAK,KAAK,aAAa,KAAK,KAAK,aAAa,MAAM,KAAK,aAAa,GAAI;AACzE,qBAAK,cAAcA;AAAA,cACpB;AAAA,YACD,CAAE;AAAA,UACJ,GAAG,MAAM,OAAO,UAAU,MAAO;AAAA,QAClC;AAAA,QAEA,QAAQ,WAAW;AAClB,iBAAO,SAAU,MAAM,WAAW,SAAU,MAAO;AAClD,gBAAK,KAAK,aAAa,KAAK,KAAK,aAAa,MAAM,KAAK,aAAa,GAAI;AACzE,kBAAI,SAAS,mBAAoB,MAAM,IAAK;AAC5C,qBAAO,YAAa,IAAK;AAAA,YAC1B;AAAA,UACD,CAAE;AAAA,QACH;AAAA,QAEA,SAAS,WAAW;AACnB,iBAAO,SAAU,MAAM,WAAW,SAAU,MAAO;AAClD,gBAAK,KAAK,aAAa,KAAK,KAAK,aAAa,MAAM,KAAK,aAAa,GAAI;AACzE,kBAAI,SAAS,mBAAoB,MAAM,IAAK;AAC5C,qBAAO,aAAc,MAAM,OAAO,UAAW;AAAA,YAC9C;AAAA,UACD,CAAE;AAAA,QACH;AAAA,QAEA,QAAQ,WAAW;AAClB,iBAAO,SAAU,MAAM,WAAW,SAAU,MAAO;AAClD,gBAAK,KAAK,YAAa;AACtB,mBAAK,WAAW,aAAc,MAAM,IAAK;AAAA,YAC1C;AAAA,UACD,CAAE;AAAA,QACH;AAAA,QAEA,OAAO,WAAW;AACjB,iBAAO,SAAU,MAAM,WAAW,SAAU,MAAO;AAClD,gBAAK,KAAK,YAAa;AACtB,mBAAK,WAAW,aAAc,MAAM,KAAK,WAAY;AAAA,YACtD;AAAA,UACD,CAAE;AAAA,QACH;AAAA,QAEA,OAAO,WAAW;AACjB,cAAI,MACH,IAAI;AAEL,kBAAU,OAAO,KAAM,CAAE,MAAO,MAAM,KAAM;AAC3C,gBAAK,KAAK,aAAa,GAAI;AAG1B,qBAAO,UAAW,OAAQ,MAAM,KAAM,CAAE;AAGxC,mBAAK,cAAc;AAAA,YACpB;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,QAEA,OAAO,SAAU,eAAe,mBAAoB;AACnD,0BAAgB,iBAAiB,OAAO,QAAQ;AAChD,8BAAoB,qBAAqB,OAAO,gBAAgB;AAEhE,iBAAO,KAAK,IAAK,WAAW;AAC3B,mBAAO,OAAO,MAAO,MAAM,eAAe,iBAAkB;AAAA,UAC7D,CAAE;AAAA,QACH;AAAA,QAEA,MAAM,SAAU,OAAQ;AACvB,iBAAO,OAAQ,MAAM,SAAUA,QAAQ;AACtC,gBAAI,OAAO,KAAM,CAAE,KAAK,CAAC,GACxB,IAAI,GACJ,IAAI,KAAK;AAEV,gBAAKA,WAAU,UAAa,KAAK,aAAa,GAAI;AACjD,qBAAO,KAAK;AAAA,YACb;AAGA,gBAAK,OAAOA,WAAU,YAAY,CAAC,aAAa,KAAMA,MAAM,KAC3D,CAAC,SAAW,SAAS,KAAMA,MAAM,KAAK,CAAE,IAAI,EAAG,GAAK,CAAE,EAAE,YAAY,CAAE,GAAI;AAE1E,cAAAA,SAAQ,OAAO,cAAeA,MAAM;AAEpC,kBAAI;AACH,uBAAQ,IAAI,GAAG,KAAM;AACpB,yBAAO,KAAM,CAAE,KAAK,CAAC;AAGrB,sBAAK,KAAK,aAAa,GAAI;AAC1B,2BAAO,UAAW,OAAQ,MAAM,KAAM,CAAE;AACxC,yBAAK,YAAYA;AAAA,kBAClB;AAAA,gBACD;AAEA,uBAAO;AAAA,cAGR,SAAU,GAAI;AAAA,cAAC;AAAA,YAChB;AAEA,gBAAK,MAAO;AACX,mBAAK,MAAM,EAAE,OAAQA,MAAM;AAAA,YAC5B;AAAA,UACD,GAAG,MAAM,OAAO,UAAU,MAAO;AAAA,QAClC;AAAA,QAEA,aAAa,WAAW;AACvB,cAAI,UAAU,CAAC;AAGf,iBAAO,SAAU,MAAM,WAAW,SAAU,MAAO;AAClD,gBAAI,SAAS,KAAK;AAElB,gBAAK,OAAO,QAAS,MAAM,OAAQ,IAAI,GAAI;AAC1C,qBAAO,UAAW,OAAQ,IAAK,CAAE;AACjC,kBAAK,QAAS;AACb,uBAAO,aAAc,MAAM,IAAK;AAAA,cACjC;AAAA,YACD;AAAA,UAGD,GAAG,OAAQ;AAAA,QACZ;AAAA,MACD,CAAE;AAEF,aAAO,KAAM;AAAA,QACZ,UAAU;AAAA,QACV,WAAW;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,MACb,GAAG,SAAU,MAAM,UAAW;AAC7B,eAAO,GAAI,IAAK,IAAI,SAAU,UAAW;AACxC,cAAI,OACH,MAAM,CAAC,GACP,SAAS,OAAQ,QAAS,GAC1B,OAAO,OAAO,SAAS,GACvB,IAAI;AAEL,iBAAQ,KAAK,MAAM,KAAM;AACxB,oBAAQ,MAAM,OAAO,OAAO,KAAK,MAAO,IAAK;AAC7C,mBAAQ,OAAQ,CAAE,CAAE,EAAG,QAAS,EAAG,KAAM;AAIzC,iBAAK,MAAO,KAAK,MAAM,IAAI,CAAE;AAAA,UAC9B;AAEA,iBAAO,KAAK,UAAW,GAAI;AAAA,QAC5B;AAAA,MACD,CAAE;AACF,UAAI,YAAY,IAAI,OAAQ,OAAO,OAAO,mBAAmB,GAAI;AAEjE,UAAI,cAAc;AAGlB,UAAI,YAAY,SAAU,MAAO;AAK/B,YAAI,OAAO,KAAK,cAAc;AAE9B,YAAK,CAAC,QAAQ,CAAC,KAAK,QAAS;AAC5B,iBAAOd;AAAA,QACR;AAEA,eAAO,KAAK,iBAAkB,IAAK;AAAA,MACpC;AAED,UAAI,OAAO,SAAU,MAAM,SAAS,UAAW;AAC9C,YAAI,KAAK,MACR,MAAM,CAAC;AAGR,aAAM,QAAQ,SAAU;AACvB,cAAK,IAAK,IAAI,KAAK,MAAO,IAAK;AAC/B,eAAK,MAAO,IAAK,IAAI,QAAS,IAAK;AAAA,QACpC;AAEA,cAAM,SAAS,KAAM,IAAK;AAG1B,aAAM,QAAQ,SAAU;AACvB,eAAK,MAAO,IAAK,IAAI,IAAK,IAAK;AAAA,QAChC;AAEA,eAAO;AAAA,MACR;AAGA,UAAI,YAAY,IAAI,OAAQ,UAAU,KAAM,GAAI,GAAG,GAAI;AAIvD,OAAE,WAAW;AAIZ,iBAAS,oBAAoB;AAG5B,cAAK,CAAC,KAAM;AACX;AAAA,UACD;AAEA,oBAAU,MAAM,UAAU;AAE1B,cAAI,MAAM,UACT;AAGD,0BAAgB,YAAa,SAAU,EAAE,YAAa,GAAI;AAE1D,cAAI,WAAWA,QAAO,iBAAkB,GAAI;AAC5C,6BAAmB,SAAS,QAAQ;AAGpC,kCAAwB,mBAAoB,SAAS,UAAW,MAAM;AAItE,cAAI,MAAM,QAAQ;AAClB,8BAAoB,mBAAoB,SAAS,KAAM,MAAM;AAI7D,iCAAuB,mBAAoB,SAAS,KAAM,MAAM;AAMhE,cAAI,MAAM,WAAW;AACrB,6BAAmB,mBAAoB,IAAI,cAAc,CAAE,MAAM;AAEjE,0BAAgB,YAAa,SAAU;AAIvC,gBAAM;AAAA,QACP;AAEA,iBAAS,mBAAoB,SAAU;AACtC,iBAAO,KAAK,MAAO,WAAY,OAAQ,CAAE;AAAA,QAC1C;AAEA,YAAI,kBAAkB,sBAAsB,kBAAkB,mBAC7D,yBAAyB,uBACzB,YAAY,SAAS,cAAe,KAAM,GAC1C,MAAM,SAAS,cAAe,KAAM;AAGrC,YAAK,CAAC,IAAI,OAAQ;AACjB;AAAA,QACD;AAIA,YAAI,MAAM,iBAAiB;AAC3B,YAAI,UAAW,IAAK,EAAE,MAAM,iBAAiB;AAC7C,gBAAQ,kBAAkB,IAAI,MAAM,mBAAmB;AAEvD,eAAO,OAAQ,SAAS;AAAA,UACvB,mBAAmB,WAAW;AAC7B,8BAAkB;AAClB,mBAAO;AAAA,UACR;AAAA,UACA,gBAAgB,WAAW;AAC1B,8BAAkB;AAClB,mBAAO;AAAA,UACR;AAAA,UACA,eAAe,WAAW;AACzB,8BAAkB;AAClB,mBAAO;AAAA,UACR;AAAA,UACA,oBAAoB,WAAW;AAC9B,8BAAkB;AAClB,mBAAO;AAAA,UACR;AAAA,UACA,eAAe,WAAW;AACzB,8BAAkB;AAClB,mBAAO;AAAA,UACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWA,sBAAsB,WAAW;AAChC,gBAAI,OAAO,IAAI,SAAS;AACxB,gBAAK,2BAA2B,MAAO;AACtC,sBAAQ,SAAS,cAAe,OAAQ;AACxC,mBAAK,SAAS,cAAe,IAAK;AAClC,wBAAU,SAAS,cAAe,KAAM;AAExC,oBAAM,MAAM,UAAU;AACtB,iBAAG,MAAM,UAAU;AAKnB,iBAAG,MAAM,SAAS;AAClB,sBAAQ,MAAM,SAAS;AAQvB,sBAAQ,MAAM,UAAU;AAExB,8BACE,YAAa,KAAM,EACnB,YAAa,EAAG,EAChB,YAAa,OAAQ;AAEvB,wBAAUA,QAAO,iBAAkB,EAAG;AACtC,wCAA4B,SAAU,QAAQ,QAAQ,EAAG,IACxD,SAAU,QAAQ,gBAAgB,EAAG,IACrC,SAAU,QAAQ,mBAAmB,EAAG,MAAQ,GAAG;AAEpD,8BAAgB,YAAa,KAAM;AAAA,YACpC;AACA,mBAAO;AAAA,UACR;AAAA,QACD,CAAE;AAAA,MACH,GAAI;AAGJ,eAAS,OAAQ,MAAM,MAAM,UAAW;AACvC,YAAI,OAAO,UAAU,UAAU,KAC9B,eAAe,YAAY,KAAM,IAAK,GAMtC,QAAQ,KAAK;AAEd,mBAAW,YAAY,UAAW,IAAK;AAKvC,YAAK,UAAW;AAWf,gBAAM,SAAS,iBAAkB,IAAK,KAAK,SAAU,IAAK;AAE1D,cAAK,gBAAgB,KAAM;AAkB1B,kBAAM,IAAI,QAAS,UAAU,IAAK,KAAK;AAAA,UACxC;AAEA,cAAK,QAAQ,MAAM,CAAC,WAAY,IAAK,GAAI;AACxC,kBAAM,OAAO,MAAO,MAAM,IAAK;AAAA,UAChC;AAOA,cAAK,CAAC,QAAQ,eAAe,KAAK,UAAU,KAAM,GAAI,KAAK,UAAU,KAAM,IAAK,GAAI;AAGnF,oBAAQ,MAAM;AACd,uBAAW,MAAM;AACjB,uBAAW,MAAM;AAGjB,kBAAM,WAAW,MAAM,WAAW,MAAM,QAAQ;AAChD,kBAAM,SAAS;AAGf,kBAAM,QAAQ;AACd,kBAAM,WAAW;AACjB,kBAAM,WAAW;AAAA,UAClB;AAAA,QACD;AAEA,eAAO,QAAQ;AAAA;AAAA;AAAA,UAId,MAAM;AAAA,YACN;AAAA,MACF;AAGA,eAAS,aAAc,aAAa,QAAS;AAG5C,eAAO;AAAA,UACN,KAAK,WAAW;AACf,gBAAK,YAAY,GAAI;AAIpB,qBAAO,KAAK;AACZ;AAAA,YACD;AAGA,oBAAS,KAAK,MAAM,QAAS,MAAO,MAAM,SAAU;AAAA,UACrD;AAAA,QACD;AAAA,MACD;AAGA,UAAI,cAAc,CAAE,UAAU,OAAO,IAAK,GACzC,aAAa,SAAS,cAAe,KAAM,EAAE,OAC7C,cAAc,CAAC;AAGhB,eAAS,eAAgB,MAAO;AAG/B,YAAI,UAAU,KAAM,CAAE,EAAE,YAAY,IAAI,KAAK,MAAO,CAAE,GACrD,IAAI,YAAY;AAEjB,eAAQ,KAAM;AACb,iBAAO,YAAa,CAAE,IAAI;AAC1B,cAAK,QAAQ,YAAa;AACzB,mBAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAGA,eAAS,cAAe,MAAO;AAC9B,YAAI,QAAQ,OAAO,SAAU,IAAK,KAAK,YAAa,IAAK;AAEzD,YAAK,OAAQ;AACZ,iBAAO;AAAA,QACR;AACA,YAAK,QAAQ,YAAa;AACzB,iBAAO;AAAA,QACR;AACA,eAAO,YAAa,IAAK,IAAI,eAAgB,IAAK,KAAK;AAAA,MACxD;AAGA,UAKC,eAAe,6BACf,UAAU,EAAE,UAAU,YAAY,YAAY,UAAU,SAAS,QAAQ,GACzE,qBAAqB;AAAA,QACpB,eAAe;AAAA,QACf,YAAY;AAAA,MACb;AAED,eAAS,kBAAmB,OAAO,OAAO,UAAW;AAIpD,YAAI,UAAU,QAAQ,KAAM,KAAM;AAClC,eAAO;AAAA;AAAA,UAGN,KAAK,IAAK,GAAG,QAAS,CAAE,KAAM,YAAY,EAAI,KAAM,QAAS,CAAE,KAAK;AAAA,YACpE;AAAA,MACF;AAEA,eAAS,mBAAoB,MAAM,WAAW,KAAK,aAAa,QAAQ,aAAc;AACrF,YAAI,IAAI,cAAc,UAAU,IAAI,GACnC,QAAQ,GACR,QAAQ,GACR,cAAc;AAGf,YAAK,SAAU,cAAc,WAAW,YAAc;AACrD,iBAAO;AAAA,QACR;AAEA,eAAQ,IAAI,GAAG,KAAK,GAAI;AAKvB,cAAK,QAAQ,UAAW;AACvB,2BAAe,OAAO,IAAK,MAAM,MAAM,UAAW,CAAE,GAAG,MAAM,MAAO;AAAA,UACrE;AAGA,cAAK,CAAC,aAAc;AAGnB,qBAAS,OAAO,IAAK,MAAM,YAAY,UAAW,CAAE,GAAG,MAAM,MAAO;AAGpE,gBAAK,QAAQ,WAAY;AACxB,uBAAS,OAAO,IAAK,MAAM,WAAW,UAAW,CAAE,IAAI,SAAS,MAAM,MAAO;AAAA,YAG9E,OAAO;AACN,uBAAS,OAAO,IAAK,MAAM,WAAW,UAAW,CAAE,IAAI,SAAS,MAAM,MAAO;AAAA,YAC9E;AAAA,UAID,OAAO;AAGN,gBAAK,QAAQ,WAAY;AACxB,uBAAS,OAAO,IAAK,MAAM,YAAY,UAAW,CAAE,GAAG,MAAM,MAAO;AAAA,YACrE;AAGA,gBAAK,QAAQ,UAAW;AACvB,uBAAS,OAAO,IAAK,MAAM,WAAW,UAAW,CAAE,IAAI,SAAS,MAAM,MAAO;AAAA,YAC9E;AAAA,UACD;AAAA,QACD;AAGA,YAAK,CAAC,eAAe,eAAe,GAAI;AAIvC,mBAAS,KAAK,IAAK,GAAG,KAAK;AAAA,YAC1B,KAAM,WAAW,UAAW,CAAE,EAAE,YAAY,IAAI,UAAU,MAAO,CAAE,CAAE,IACrE,cACA,QACA,QACA;AAAA;AAAA;AAAA,UAID,CAAE,KAAK;AAAA,QACR;AAEA,eAAO,QAAQ;AAAA,MAChB;AAEA,eAAS,iBAAkB,MAAM,WAAW,OAAQ;AAGnD,YAAI,SAAS,UAAW,IAAK,GAI5B,kBAAkB,CAAC,QAAQ,kBAAkB,KAAK,OAClD,cAAc,mBACb,OAAO,IAAK,MAAM,aAAa,OAAO,MAAO,MAAM,cACpD,mBAAmB,aAEnB,MAAM,OAAQ,MAAM,WAAW,MAAO,GACtC,aAAa,WAAW,UAAW,CAAE,EAAE,YAAY,IAAI,UAAU,MAAO,CAAE;AAI3E,YAAK,UAAU,KAAM,GAAI,GAAI;AAC5B,cAAK,CAAC,OAAQ;AACb,mBAAO;AAAA,UACR;AACA,gBAAM;AAAA,QACP;AAMA,aAAO,CAAC,QAAQ,kBAAkB,KAAK;AAAA;AAAA;AAAA;AAAA,QAMtC,CAAC,QAAQ,qBAAqB,KAAK,SAAU,MAAM,IAAK;AAAA;AAAA,QAIxD,QAAQ;AAAA;AAAA,QAIR,CAAC,WAAY,GAAI,KAAK,OAAO,IAAK,MAAM,WAAW,OAAO,MAAO,MAAM;AAAA,QAGvE,KAAK,eAAe,EAAE,QAAS;AAE/B,wBAAc,OAAO,IAAK,MAAM,aAAa,OAAO,MAAO,MAAM;AAKjE,6BAAmB,cAAc;AACjC,cAAK,kBAAmB;AACvB,kBAAM,KAAM,UAAW;AAAA,UACxB;AAAA,QACD;AAGA,cAAM,WAAY,GAAI,KAAK;AAG3B,eAAS,MACR;AAAA,UACC;AAAA,UACA;AAAA,UACA,UAAW,cAAc,WAAW;AAAA,UACpC;AAAA,UACA;AAAA;AAAA,UAGA;AAAA,QACD,IACG;AAAA,MACL;AAEA,aAAO,OAAQ;AAAA;AAAA;AAAA,QAId,UAAU;AAAA,UACT,SAAS;AAAA,YACR,KAAK,SAAU,MAAM,UAAW;AAC/B,kBAAK,UAAW;AAGf,oBAAI,MAAM,OAAQ,MAAM,SAAU;AAClC,uBAAO,QAAQ,KAAK,MAAM;AAAA,cAC3B;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA;AAAA,QAGA,WAAW;AAAA,UACV,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,kBAAkB;AAAA,UAClB,aAAa;AAAA,UACb,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,UACP,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,MAAM;AAAA;AAAA,UAGN,aAAa;AAAA,UACb,cAAc;AAAA,UACd,aAAa;AAAA,UACb,kBAAkB;AAAA,UAClB,eAAe;AAAA,QAChB;AAAA;AAAA;AAAA,QAIA,UAAU,CAAC;AAAA;AAAA,QAGX,OAAO,SAAU,MAAM,MAAM,OAAO,OAAQ;AAG3C,cAAK,CAAC,QAAQ,KAAK,aAAa,KAAK,KAAK,aAAa,KAAK,CAAC,KAAK,OAAQ;AACzE;AAAA,UACD;AAGA,cAAI,KAAK,MAAM,OACd,WAAW,UAAW,IAAK,GAC3B,eAAe,YAAY,KAAM,IAAK,GACtC,QAAQ,KAAK;AAKd,cAAK,CAAC,cAAe;AACpB,mBAAO,cAAe,QAAS;AAAA,UAChC;AAGA,kBAAQ,OAAO,SAAU,IAAK,KAAK,OAAO,SAAU,QAAS;AAG7D,cAAK,UAAU,QAAY;AAC1B,mBAAO,OAAO;AAGd,gBAAK,SAAS,aAAc,MAAM,QAAQ,KAAM,KAAM,MAAO,IAAK,CAAE,GAAI;AACvE,sBAAQ,UAAW,MAAM,MAAM,GAAI;AAGnC,qBAAO;AAAA,YACR;AAGA,gBAAK,SAAS,QAAQ,UAAU,OAAQ;AACvC;AAAA,YACD;AAKA,gBAAK,SAAS,YAAY,CAAC,cAAe;AACzC,uBAAS,OAAO,IAAK,CAAE,MAAO,OAAO,UAAW,QAAS,IAAI,KAAK;AAAA,YACnE;AAGA,gBAAK,CAAC,QAAQ,mBAAmB,UAAU,MAAM,KAAK,QAAS,YAAa,MAAM,GAAI;AACrF,oBAAO,IAAK,IAAI;AAAA,YACjB;AAGA,gBAAK,CAAC,SAAS,EAAG,SAAS,WACxB,QAAQ,MAAM,IAAK,MAAM,OAAO,KAAM,OAAQ,QAAY;AAE5D,kBAAK,cAAe;AACnB,sBAAM,YAAa,MAAM,KAAM;AAAA,cAChC,OAAO;AACN,sBAAO,IAAK,IAAI;AAAA,cACjB;AAAA,YACD;AAAA,UAED,OAAO;AAGN,gBAAK,SAAS,SAAS,UACpB,MAAM,MAAM,IAAK,MAAM,OAAO,KAAM,OAAQ,QAAY;AAE1D,qBAAO;AAAA,YACR;AAGA,mBAAO,MAAO,IAAK;AAAA,UACpB;AAAA,QACD;AAAA,QAEA,KAAK,SAAU,MAAM,MAAM,OAAO,QAAS;AAC1C,cAAI,KAAK,KAAK,OACb,WAAW,UAAW,IAAK,GAC3B,eAAe,YAAY,KAAM,IAAK;AAKvC,cAAK,CAAC,cAAe;AACpB,mBAAO,cAAe,QAAS;AAAA,UAChC;AAGA,kBAAQ,OAAO,SAAU,IAAK,KAAK,OAAO,SAAU,QAAS;AAG7D,cAAK,SAAS,SAAS,OAAQ;AAC9B,kBAAM,MAAM,IAAK,MAAM,MAAM,KAAM;AAAA,UACpC;AAGA,cAAK,QAAQ,QAAY;AACxB,kBAAM,OAAQ,MAAM,MAAM,MAAO;AAAA,UAClC;AAGA,cAAK,QAAQ,YAAY,QAAQ,oBAAqB;AACrD,kBAAM,mBAAoB,IAAK;AAAA,UAChC;AAGA,cAAK,UAAU,MAAM,OAAQ;AAC5B,kBAAM,WAAY,GAAI;AACtB,mBAAO,UAAU,QAAQ,SAAU,GAAI,IAAI,OAAO,IAAI;AAAA,UACvD;AAEA,iBAAO;AAAA,QACR;AAAA,MACD,CAAE;AAEF,aAAO,KAAM,CAAE,UAAU,OAAQ,GAAG,SAAU,IAAI,WAAY;AAC7D,eAAO,SAAU,SAAU,IAAI;AAAA,UAC9B,KAAK,SAAU,MAAM,UAAU,OAAQ;AACtC,gBAAK,UAAW;AAIf,qBAAO,aAAa,KAAM,OAAO,IAAK,MAAM,SAAU,CAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAQrD,CAAC,KAAK,eAAe,EAAE,UAAU,CAAC,KAAK,sBAAsB,EAAE,SACjE,KAAM,MAAM,SAAS,WAAW;AAC/B,uBAAO,iBAAkB,MAAM,WAAW,KAAM;AAAA,cACjD,CAAE,IACF,iBAAkB,MAAM,WAAW,KAAM;AAAA,YAC3C;AAAA,UACD;AAAA,UAEA,KAAK,SAAU,MAAM,OAAO,OAAQ;AACnC,gBAAI,SACH,SAAS,UAAW,IAAK,GAIzB,qBAAqB,CAAC,QAAQ,cAAc,KAC3C,OAAO,aAAa,YAGrB,kBAAkB,sBAAsB,OACxC,cAAc,mBACb,OAAO,IAAK,MAAM,aAAa,OAAO,MAAO,MAAM,cACpD,WAAW,QACV;AAAA,cACC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD,IACA;AAIF,gBAAK,eAAe,oBAAqB;AACxC,0BAAY,KAAK;AAAA,gBAChB,KAAM,WAAW,UAAW,CAAE,EAAE,YAAY,IAAI,UAAU,MAAO,CAAE,CAAE,IACrE,WAAY,OAAQ,SAAU,CAAE,IAChC,mBAAoB,MAAM,WAAW,UAAU,OAAO,MAAO,IAC7D;AAAA,cACD;AAAA,YACD;AAGA,gBAAK,aAAc,UAAU,QAAQ,KAAM,KAAM,OAC9C,QAAS,CAAE,KAAK,UAAW,MAAO;AAEpC,mBAAK,MAAO,SAAU,IAAI;AAC1B,sBAAQ,OAAO,IAAK,MAAM,SAAU;AAAA,YACrC;AAEA,mBAAO,kBAAmB,MAAM,OAAO,QAAS;AAAA,UACjD;AAAA,QACD;AAAA,MACD,CAAE;AAEF,aAAO,SAAS,aAAa;AAAA,QAAc,QAAQ;AAAA,QAClD,SAAU,MAAM,UAAW;AAC1B,cAAK,UAAW;AACf,oBAAS,WAAY,OAAQ,MAAM,YAAa,CAAE,KACjD,KAAK,sBAAsB,EAAE,OAC5B,KAAM,MAAM,EAAE,YAAY,EAAE,GAAG,WAAW;AACzC,qBAAO,KAAK,sBAAsB,EAAE;AAAA,YACrC,CAAE,KACA;AAAA,UACL;AAAA,QACD;AAAA,MACD;AAGA,aAAO,KAAM;AAAA,QACZ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,MACT,GAAG,SAAU,QAAQ,QAAS;AAC7B,eAAO,SAAU,SAAS,MAAO,IAAI;AAAA,UACpC,QAAQ,SAAU,OAAQ;AACzB,gBAAI,IAAI,GACP,WAAW,CAAC,GAGZ,QAAQ,OAAO,UAAU,WAAW,MAAM,MAAO,GAAI,IAAI,CAAE,KAAM;AAElE,mBAAQ,IAAI,GAAG,KAAM;AACpB,uBAAU,SAAS,UAAW,CAAE,IAAI,MAAO,IAC1C,MAAO,CAAE,KAAK,MAAO,IAAI,CAAE,KAAK,MAAO,CAAE;AAAA,YAC3C;AAEA,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,YAAK,WAAW,UAAW;AAC1B,iBAAO,SAAU,SAAS,MAAO,EAAE,MAAM;AAAA,QAC1C;AAAA,MACD,CAAE;AAEF,aAAO,GAAG,OAAQ;AAAA,QACjB,KAAK,SAAU,MAAM,OAAQ;AAC5B,iBAAO,OAAQ,MAAM,SAAU,MAAMkB,OAAMJ,QAAQ;AAClD,gBAAI,QAAQ,KACX,MAAM,CAAC,GACP,IAAI;AAEL,gBAAK,MAAM,QAASI,KAAK,GAAI;AAC5B,uBAAS,UAAW,IAAK;AACzB,oBAAMA,MAAK;AAEX,qBAAQ,IAAI,KAAK,KAAM;AACtB,oBAAKA,MAAM,CAAE,CAAE,IAAI,OAAO,IAAK,MAAMA,MAAM,CAAE,GAAG,OAAO,MAAO;AAAA,cAC/D;AAEA,qBAAO;AAAA,YACR;AAEA,mBAAOJ,WAAU,SAChB,OAAO,MAAO,MAAMI,OAAMJ,MAAM,IAChC,OAAO,IAAK,MAAMI,KAAK;AAAA,UACzB,GAAG,MAAM,OAAO,UAAU,SAAS,CAAE;AAAA,QACtC;AAAA,MACD,CAAE;AAGF,eAAS,MAAO,MAAM,SAAS,MAAM,KAAK,QAAS;AAClD,eAAO,IAAI,MAAM,UAAU,KAAM,MAAM,SAAS,MAAM,KAAK,MAAO;AAAA,MACnE;AACA,aAAO,QAAQ;AAEf,YAAM,YAAY;AAAA,QACjB,aAAa;AAAA,QACb,MAAM,SAAU,MAAM,SAAS,MAAM,KAAK,QAAQ,MAAO;AACxD,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,eAAK,SAAS,UAAU,OAAO,OAAO;AACtC,eAAK,UAAU;AACf,eAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AACjC,eAAK,MAAM;AACX,eAAK,OAAO,SAAU,OAAO,UAAW,IAAK,IAAI,KAAK;AAAA,QACvD;AAAA,QACA,KAAK,WAAW;AACf,cAAI,QAAQ,MAAM,UAAW,KAAK,IAAK;AAEvC,iBAAO,SAAS,MAAM,MACrB,MAAM,IAAK,IAAK,IAChB,MAAM,UAAU,SAAS,IAAK,IAAK;AAAA,QACrC;AAAA,QACA,KAAK,SAAU,SAAU;AACxB,cAAI,OACH,QAAQ,MAAM,UAAW,KAAK,IAAK;AAEpC,cAAK,KAAK,QAAQ,UAAW;AAC5B,iBAAK,MAAM,QAAQ,OAAO,OAAQ,KAAK,MAAO;AAAA,cAC7C;AAAA,cAAS,KAAK,QAAQ,WAAW;AAAA,cAAS;AAAA,cAAG;AAAA,cAAG,KAAK,QAAQ;AAAA,YAC9D;AAAA,UACD,OAAO;AACN,iBAAK,MAAM,QAAQ;AAAA,UACpB;AACA,eAAK,OAAQ,KAAK,MAAM,KAAK,SAAU,QAAQ,KAAK;AAEpD,cAAK,KAAK,QAAQ,MAAO;AACxB,iBAAK,QAAQ,KAAK,KAAM,KAAK,MAAM,KAAK,KAAK,IAAK;AAAA,UACnD;AAEA,cAAK,SAAS,MAAM,KAAM;AACzB,kBAAM,IAAK,IAAK;AAAA,UACjB,OAAO;AACN,kBAAM,UAAU,SAAS,IAAK,IAAK;AAAA,UACpC;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAEA,YAAM,UAAU,KAAK,YAAY,MAAM;AAEvC,YAAM,YAAY;AAAA,QACjB,UAAU;AAAA,UACT,KAAK,SAAU,OAAQ;AACtB,gBAAI;AAIJ,gBAAK,MAAM,KAAK,aAAa,KAC5B,MAAM,KAAM,MAAM,IAAK,KAAK,QAAQ,MAAM,KAAK,MAAO,MAAM,IAAK,KAAK,MAAO;AAC7E,qBAAO,MAAM,KAAM,MAAM,IAAK;AAAA,YAC/B;AAMA,qBAAS,OAAO,IAAK,MAAM,MAAM,MAAM,MAAM,EAAG;AAGhD,mBAAO,CAAC,UAAU,WAAW,SAAS,IAAI;AAAA,UAC3C;AAAA,UACA,KAAK,SAAU,OAAQ;AAKtB,gBAAK,OAAO,GAAG,KAAM,MAAM,IAAK,GAAI;AACnC,qBAAO,GAAG,KAAM,MAAM,IAAK,EAAG,KAAM;AAAA,YACrC,WAAY,MAAM,KAAK,aAAa,MACnC,OAAO,SAAU,MAAM,IAAK,KAC3B,MAAM,KAAK,MAAO,cAAe,MAAM,IAAK,CAAE,KAAK,OAAS;AAC7D,qBAAO,MAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAK;AAAA,YAC9D,OAAO;AACN,oBAAM,KAAM,MAAM,IAAK,IAAI,MAAM;AAAA,YAClC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAIA,YAAM,UAAU,YAAY,MAAM,UAAU,aAAa;AAAA,QACxD,KAAK,SAAU,OAAQ;AACtB,cAAK,MAAM,KAAK,YAAY,MAAM,KAAK,YAAa;AACnD,kBAAM,KAAM,MAAM,IAAK,IAAI,MAAM;AAAA,UAClC;AAAA,QACD;AAAA,MACD;AAEA,aAAO,SAAS;AAAA,QACf,QAAQ,SAAU,GAAI;AACrB,iBAAO;AAAA,QACR;AAAA,QACA,OAAO,SAAU,GAAI;AACpB,iBAAO,MAAM,KAAK,IAAK,IAAI,KAAK,EAAG,IAAI;AAAA,QACxC;AAAA,QACA,UAAU;AAAA,MACX;AAEA,aAAO,KAAK,MAAM,UAAU;AAG5B,aAAO,GAAG,OAAO,CAAC;AAKlB,UACC,OAAO,YACP,WAAW,0BACX,OAAO;AAER,eAAS,WAAW;AACnB,YAAK,YAAa;AACjB,cAAK,SAAS,WAAW,SAASlB,QAAO,uBAAwB;AAChE,YAAAA,QAAO,sBAAuB,QAAS;AAAA,UACxC,OAAO;AACN,YAAAA,QAAO,WAAY,UAAU,OAAO,GAAG,QAAS;AAAA,UACjD;AAEA,iBAAO,GAAG,KAAK;AAAA,QAChB;AAAA,MACD;AAGA,eAAS,cAAc;AACtB,QAAAA,QAAO,WAAY,WAAW;AAC7B,kBAAQ;AAAA,QACT,CAAE;AACF,eAAS,QAAQ,KAAK,IAAI;AAAA,MAC3B;AAGA,eAAS,MAAO,MAAM,cAAe;AACpC,YAAI,OACH,IAAI,GACJ,QAAQ,EAAE,QAAQ,KAAK;AAIxB,uBAAe,eAAe,IAAI;AAClC,eAAQ,IAAI,GAAG,KAAK,IAAI,cAAe;AACtC,kBAAQ,UAAW,CAAE;AACrB,gBAAO,WAAW,KAAM,IAAI,MAAO,YAAY,KAAM,IAAI;AAAA,QAC1D;AAEA,YAAK,cAAe;AACnB,gBAAM,UAAU,MAAM,QAAQ;AAAA,QAC/B;AAEA,eAAO;AAAA,MACR;AAEA,eAAS,YAAa,OAAO,MAAM,WAAY;AAC9C,YAAI,OACH,cAAe,UAAU,SAAU,IAAK,KAAK,CAAC,GAAI,OAAQ,UAAU,SAAU,GAAI,CAAE,GACpF,QAAQ,GACR,SAAS,WAAW;AACrB,eAAQ,QAAQ,QAAQ,SAAU;AACjC,cAAO,QAAQ,WAAY,KAAM,EAAE,KAAM,WAAW,MAAM,KAAM,GAAM;AAGrE,mBAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD;AAEA,eAAS,iBAAkB,MAAM,OAAO,MAAO;AAC9C,YAAI,MAAM,OAAO,QAAQ,OAAO,SAAS,WAAW,gBAAgB,SACnE,QAAQ,WAAW,SAAS,YAAY,OACxC,OAAO,MACP,OAAO,CAAC,GACR,QAAQ,KAAK,OACb,SAAS,KAAK,YAAY,mBAAoB,IAAK,GACnD,WAAW,SAAS,IAAK,MAAM,QAAS;AAGzC,YAAK,CAAC,KAAK,OAAQ;AAClB,kBAAQ,OAAO,YAAa,MAAM,IAAK;AACvC,cAAK,MAAM,YAAY,MAAO;AAC7B,kBAAM,WAAW;AACjB,sBAAU,MAAM,MAAM;AACtB,kBAAM,MAAM,OAAO,WAAW;AAC7B,kBAAK,CAAC,MAAM,UAAW;AACtB,wBAAQ;AAAA,cACT;AAAA,YACD;AAAA,UACD;AACA,gBAAM;AAEN,eAAK,OAAQ,WAAW;AAGvB,iBAAK,OAAQ,WAAW;AACvB,oBAAM;AACN,kBAAK,CAAC,OAAO,MAAO,MAAM,IAAK,EAAE,QAAS;AACzC,sBAAM,MAAM,KAAK;AAAA,cAClB;AAAA,YACD,CAAE;AAAA,UACH,CAAE;AAAA,QACH;AAGA,aAAM,QAAQ,OAAQ;AACrB,kBAAQ,MAAO,IAAK;AACpB,cAAK,SAAS,KAAM,KAAM,GAAI;AAC7B,mBAAO,MAAO,IAAK;AACnB,qBAAS,UAAU,UAAU;AAC7B,gBAAK,WAAY,SAAS,SAAS,SAAW;AAI7C,kBAAK,UAAU,UAAU,YAAY,SAAU,IAAK,MAAM,QAAY;AACrE,yBAAS;AAAA,cAGV,OAAO;AACN;AAAA,cACD;AAAA,YACD;AACA,iBAAM,IAAK,IAAI,YAAY,SAAU,IAAK,KAAK,OAAO,MAAO,MAAM,IAAK;AAAA,UACzE;AAAA,QACD;AAGA,oBAAY,CAAC,OAAO,cAAe,KAAM;AACzC,YAAK,CAAC,aAAa,OAAO,cAAe,IAAK,GAAI;AACjD;AAAA,QACD;AAGA,YAAK,SAAS,KAAK,aAAa,GAAI;AAMnC,eAAK,WAAW,CAAE,MAAM,UAAU,MAAM,WAAW,MAAM,SAAU;AAGnE,2BAAiB,YAAY,SAAS;AACtC,cAAK,kBAAkB,MAAO;AAC7B,6BAAiB,SAAS,IAAK,MAAM,SAAU;AAAA,UAChD;AACA,oBAAU,OAAO,IAAK,MAAM,SAAU;AACtC,cAAK,YAAY,QAAS;AACzB,gBAAK,gBAAiB;AACrB,wBAAU;AAAA,YACX,OAAO;AAGN,uBAAU,CAAE,IAAK,GAAG,IAAK;AACzB,+BAAiB,KAAK,MAAM,WAAW;AACvC,wBAAU,OAAO,IAAK,MAAM,SAAU;AACtC,uBAAU,CAAE,IAAK,CAAE;AAAA,YACpB;AAAA,UACD;AAGA,cAAK,YAAY,YAAY,YAAY,kBAAkB,kBAAkB,MAAO;AACnF,gBAAK,OAAO,IAAK,MAAM,OAAQ,MAAM,QAAS;AAG7C,kBAAK,CAAC,WAAY;AACjB,qBAAK,KAAM,WAAW;AACrB,wBAAM,UAAU;AAAA,gBACjB,CAAE;AACF,oBAAK,kBAAkB,MAAO;AAC7B,4BAAU,MAAM;AAChB,mCAAiB,YAAY,SAAS,KAAK;AAAA,gBAC5C;AAAA,cACD;AACA,oBAAM,UAAU;AAAA,YACjB;AAAA,UACD;AAAA,QACD;AAEA,YAAK,KAAK,UAAW;AACpB,gBAAM,WAAW;AACjB,eAAK,OAAQ,WAAW;AACvB,kBAAM,WAAW,KAAK,SAAU,CAAE;AAClC,kBAAM,YAAY,KAAK,SAAU,CAAE;AACnC,kBAAM,YAAY,KAAK,SAAU,CAAE;AAAA,UACpC,CAAE;AAAA,QACH;AAGA,oBAAY;AACZ,aAAM,QAAQ,MAAO;AAGpB,cAAK,CAAC,WAAY;AACjB,gBAAK,UAAW;AACf,kBAAK,YAAY,UAAW;AAC3B,yBAAS,SAAS;AAAA,cACnB;AAAA,YACD,OAAO;AACN,yBAAW,SAAS,OAAQ,MAAM,UAAU,EAAE,SAAS,eAAe,CAAE;AAAA,YACzE;AAGA,gBAAK,QAAS;AACb,uBAAS,SAAS,CAAC;AAAA,YACpB;AAGA,gBAAK,QAAS;AACb,uBAAU,CAAE,IAAK,GAAG,IAAK;AAAA,YAC1B;AAIA,iBAAK,KAAM,WAAW;AAKrB,kBAAK,CAAC,QAAS;AACd,yBAAU,CAAE,IAAK,CAAE;AAAA,cACpB;AACA,uBAAS,OAAQ,MAAM,QAAS;AAChC,mBAAM,QAAQ,MAAO;AACpB,uBAAO,MAAO,MAAM,MAAM,KAAM,IAAK,CAAE;AAAA,cACxC;AAAA,YACD,CAAE;AAAA,UACH;AAGA,sBAAY,YAAa,SAAS,SAAU,IAAK,IAAI,GAAG,MAAM,IAAK;AACnE,cAAK,EAAG,QAAQ,WAAa;AAC5B,qBAAU,IAAK,IAAI,UAAU;AAC7B,gBAAK,QAAS;AACb,wBAAU,MAAM,UAAU;AAC1B,wBAAU,QAAQ;AAAA,YACnB;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,eAAS,WAAY,OAAO,eAAgB;AAC3C,YAAI,OAAO,MAAM,QAAQ,OAAO;AAGhC,aAAM,SAAS,OAAQ;AACtB,iBAAO,UAAW,KAAM;AACxB,mBAAS,cAAe,IAAK;AAC7B,kBAAQ,MAAO,KAAM;AACrB,cAAK,MAAM,QAAS,KAAM,GAAI;AAC7B,qBAAS,MAAO,CAAE;AAClB,oBAAQ,MAAO,KAAM,IAAI,MAAO,CAAE;AAAA,UACnC;AAEA,cAAK,UAAU,MAAO;AACrB,kBAAO,IAAK,IAAI;AAChB,mBAAO,MAAO,KAAM;AAAA,UACrB;AAEA,kBAAQ,OAAO,SAAU,IAAK;AAC9B,cAAK,SAAS,YAAY,OAAQ;AACjC,oBAAQ,MAAM,OAAQ,KAAM;AAC5B,mBAAO,MAAO,IAAK;AAInB,iBAAM,SAAS,OAAQ;AACtB,kBAAK,EAAG,SAAS,QAAU;AAC1B,sBAAO,KAAM,IAAI,MAAO,KAAM;AAC9B,8BAAe,KAAM,IAAI;AAAA,cAC1B;AAAA,YACD;AAAA,UACD,OAAO;AACN,0BAAe,IAAK,IAAI;AAAA,UACzB;AAAA,QACD;AAAA,MACD;AAEA,eAAS,UAAW,MAAM,YAAY,SAAU;AAC/C,YAAI,QACH,SACA,QAAQ,GACR,SAAS,UAAU,WAAW,QAC9B,WAAW,OAAO,SAAS,EAAE,OAAQ,WAAW;AAG/C,iBAAO,KAAK;AAAA,QACb,CAAE,GACF,OAAO,WAAW;AACjB,cAAK,SAAU;AACd,mBAAO;AAAA,UACR;AACA,cAAI,cAAc,SAAS,YAAY,GACtC,YAAY,KAAK,IAAK,GAAG,UAAU,YAAY,UAAU,WAAW,WAAY,GAIhF,OAAO,YAAY,UAAU,YAAY,GACzC,UAAU,IAAI,MACdmB,SAAQ,GACRC,UAAS,UAAU,OAAO;AAE3B,iBAAQD,SAAQC,SAAQD,UAAU;AACjC,sBAAU,OAAQA,MAAM,EAAE,IAAK,OAAQ;AAAA,UACxC;AAEA,mBAAS,WAAY,MAAM,CAAE,WAAW,SAAS,SAAU,CAAE;AAG7D,cAAK,UAAU,KAAKC,SAAS;AAC5B,mBAAO;AAAA,UACR;AAGA,cAAK,CAACA,SAAS;AACd,qBAAS,WAAY,MAAM,CAAE,WAAW,GAAG,CAAE,CAAE;AAAA,UAChD;AAGA,mBAAS,YAAa,MAAM,CAAE,SAAU,CAAE;AAC1C,iBAAO;AAAA,QACR,GACA,YAAY,SAAS,QAAS;AAAA,UAC7B;AAAA,UACA,OAAO,OAAO,OAAQ,CAAC,GAAG,UAAW;AAAA,UACrC,MAAM,OAAO,OAAQ,MAAM;AAAA,YAC1B,eAAe,CAAC;AAAA,YAChB,QAAQ,OAAO,OAAO;AAAA,UACvB,GAAG,OAAQ;AAAA,UACX,oBAAoB;AAAA,UACpB,iBAAiB;AAAA,UACjB,WAAW,SAAS,YAAY;AAAA,UAChC,UAAU,QAAQ;AAAA,UAClB,QAAQ,CAAC;AAAA,UACT,aAAa,SAAU,MAAM,KAAM;AAClC,gBAAI,QAAQ,OAAO;AAAA,cAAO;AAAA,cAAM,UAAU;AAAA,cAAM;AAAA,cAAM;AAAA,cACrD,UAAU,KAAK,cAAe,IAAK,KAAK,UAAU,KAAK;AAAA,YAAO;AAC/D,sBAAU,OAAO,KAAM,KAAM;AAC7B,mBAAO;AAAA,UACR;AAAA,UACA,MAAM,SAAU,SAAU;AACzB,gBAAID,SAAQ,GAIXC,UAAS,UAAU,UAAU,OAAO,SAAS;AAC9C,gBAAK,SAAU;AACd,qBAAO;AAAA,YACR;AACA,sBAAU;AACV,mBAAQD,SAAQC,SAAQD,UAAU;AACjC,wBAAU,OAAQA,MAAM,EAAE,IAAK,CAAE;AAAA,YAClC;AAGA,gBAAK,SAAU;AACd,uBAAS,WAAY,MAAM,CAAE,WAAW,GAAG,CAAE,CAAE;AAC/C,uBAAS,YAAa,MAAM,CAAE,WAAW,OAAQ,CAAE;AAAA,YACpD,OAAO;AACN,uBAAS,WAAY,MAAM,CAAE,WAAW,OAAQ,CAAE;AAAA,YACnD;AACA,mBAAO;AAAA,UACR;AAAA,QACD,CAAE,GACF,QAAQ,UAAU;AAEnB,mBAAY,OAAO,UAAU,KAAK,aAAc;AAEhD,eAAQ,QAAQ,QAAQ,SAAU;AACjC,mBAAS,UAAU,WAAY,KAAM,EAAE,KAAM,WAAW,MAAM,OAAO,UAAU,IAAK;AACpF,cAAK,QAAS;AACb,gBAAK,WAAY,OAAO,IAAK,GAAI;AAChC,qBAAO,YAAa,UAAU,MAAM,UAAU,KAAK,KAAM,EAAE,OAC1D,OAAO,KAAK,KAAM,MAAO;AAAA,YAC3B;AACA,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,eAAO,IAAK,OAAO,aAAa,SAAU;AAE1C,YAAK,WAAY,UAAU,KAAK,KAAM,GAAI;AACzC,oBAAU,KAAK,MAAM,KAAM,MAAM,SAAU;AAAA,QAC5C;AAGA,kBACE,SAAU,UAAU,KAAK,QAAS,EAClC,KAAM,UAAU,KAAK,MAAM,UAAU,KAAK,QAAS,EACnD,KAAM,UAAU,KAAK,IAAK,EAC1B,OAAQ,UAAU,KAAK,MAAO;AAEhC,eAAO,GAAG;AAAA,UACT,OAAO,OAAQ,MAAM;AAAA,YACpB;AAAA,YACA,MAAM;AAAA,YACN,OAAO,UAAU,KAAK;AAAA,UACvB,CAAE;AAAA,QACH;AAEA,eAAO;AAAA,MACR;AAEA,aAAO,YAAY,OAAO,OAAQ,WAAW;AAAA,QAE5C,UAAU;AAAA,UACT,KAAK,CAAE,SAAU,MAAM,OAAQ;AAC9B,gBAAI,QAAQ,KAAK,YAAa,MAAM,KAAM;AAC1C,sBAAW,MAAM,MAAM,MAAM,QAAQ,KAAM,KAAM,GAAG,KAAM;AAC1D,mBAAO;AAAA,UACR,CAAE;AAAA,QACH;AAAA,QAEA,SAAS,SAAU,OAAO,UAAW;AACpC,cAAK,WAAY,KAAM,GAAI;AAC1B,uBAAW;AACX,oBAAQ,CAAE,GAAI;AAAA,UACf,OAAO;AACN,oBAAQ,MAAM,MAAO,aAAc;AAAA,UACpC;AAEA,cAAI,MACH,QAAQ,GACR,SAAS,MAAM;AAEhB,iBAAQ,QAAQ,QAAQ,SAAU;AACjC,mBAAO,MAAO,KAAM;AACpB,sBAAU,SAAU,IAAK,IAAI,UAAU,SAAU,IAAK,KAAK,CAAC;AAC5D,sBAAU,SAAU,IAAK,EAAE,QAAS,QAAS;AAAA,UAC9C;AAAA,QACD;AAAA,QAEA,YAAY,CAAE,gBAAiB;AAAA,QAE/B,WAAW,SAAU,UAAU,SAAU;AACxC,cAAK,SAAU;AACd,sBAAU,WAAW,QAAS,QAAS;AAAA,UACxC,OAAO;AACN,sBAAU,WAAW,KAAM,QAAS;AAAA,UACrC;AAAA,QACD;AAAA,MACD,CAAE;AAEF,aAAO,QAAQ,SAAU,OAAO,QAAQ,IAAK;AAC5C,YAAI,MAAM,SAAS,OAAO,UAAU,WAAW,OAAO,OAAQ,CAAC,GAAG,KAAM,IAAI;AAAA,UAC3E,UAAU,MAAM,CAAC,MAAM,UACtB,WAAY,KAAM,KAAK;AAAA,UACxB,UAAU;AAAA,UACV,QAAQ,MAAM,UAAU,UAAU,CAAC,WAAY,MAAO,KAAK;AAAA,QAC5D;AAGA,YAAK,OAAO,GAAG,KAAM;AACpB,cAAI,WAAW;AAAA,QAEhB,OAAO;AACN,cAAK,OAAO,IAAI,aAAa,UAAW;AACvC,gBAAK,IAAI,YAAY,OAAO,GAAG,QAAS;AACvC,kBAAI,WAAW,OAAO,GAAG,OAAQ,IAAI,QAAS;AAAA,YAE/C,OAAO;AACN,kBAAI,WAAW,OAAO,GAAG,OAAO;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AAGA,YAAK,IAAI,SAAS,QAAQ,IAAI,UAAU,MAAO;AAC9C,cAAI,QAAQ;AAAA,QACb;AAGA,YAAI,MAAM,IAAI;AAEd,YAAI,WAAW,WAAW;AACzB,cAAK,WAAY,IAAI,GAAI,GAAI;AAC5B,gBAAI,IAAI,KAAM,IAAK;AAAA,UACpB;AAEA,cAAK,IAAI,OAAQ;AAChB,mBAAO,QAAS,MAAM,IAAI,KAAM;AAAA,UACjC;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAEA,aAAO,GAAG,OAAQ;AAAA,QACjB,QAAQ,SAAU,OAAO,IAAI,QAAQ,UAAW;AAG/C,iBAAO,KAAK,OAAQ,kBAAmB,EAAE,IAAK,WAAW,CAAE,EAAE,KAAK,EAGhE,IAAI,EAAE,QAAS,EAAE,SAAS,GAAG,GAAG,OAAO,QAAQ,QAAS;AAAA,QAC3D;AAAA,QACA,SAAS,SAAU,MAAM,OAAO,QAAQ,UAAW;AAClD,cAAI,QAAQ,OAAO,cAAe,IAAK,GACtC,SAAS,OAAO,MAAO,OAAO,QAAQ,QAAS,GAC/C,cAAc,WAAW;AAGxB,gBAAI,OAAO,UAAW,MAAM,OAAO,OAAQ,CAAC,GAAG,IAAK,GAAG,MAAO;AAG9D,gBAAK,SAAS,SAAS,IAAK,MAAM,QAAS,GAAI;AAC9C,mBAAK,KAAM,IAAK;AAAA,YACjB;AAAA,UACD;AAED,sBAAY,SAAS;AAErB,iBAAO,SAAS,OAAO,UAAU,QAChC,KAAK,KAAM,WAAY,IACvB,KAAK,MAAO,OAAO,OAAO,WAAY;AAAA,QACxC;AAAA,QACA,MAAM,SAAU,MAAM,YAAY,SAAU;AAC3C,cAAI,YAAY,SAAU,OAAQ;AACjC,gBAAI,OAAO,MAAM;AACjB,mBAAO,MAAM;AACb,iBAAM,OAAQ;AAAA,UACf;AAEA,cAAK,OAAO,SAAS,UAAW;AAC/B,sBAAU;AACV,yBAAa;AACb,mBAAO;AAAA,UACR;AACA,cAAK,YAAa;AACjB,iBAAK,MAAO,QAAQ,MAAM,CAAC,CAAE;AAAA,UAC9B;AAEA,iBAAO,KAAK,KAAM,WAAW;AAC5B,gBAAI,UAAU,MACb,QAAQ,QAAQ,QAAQ,OAAO,cAC/B,SAAS,OAAO,QAChB,OAAO,SAAS,IAAK,IAAK;AAE3B,gBAAK,OAAQ;AACZ,kBAAK,KAAM,KAAM,KAAK,KAAM,KAAM,EAAE,MAAO;AAC1C,0BAAW,KAAM,KAAM,CAAE;AAAA,cAC1B;AAAA,YACD,OAAO;AACN,mBAAM,SAAS,MAAO;AACrB,oBAAK,KAAM,KAAM,KAAK,KAAM,KAAM,EAAE,QAAQ,KAAK,KAAM,KAAM,GAAI;AAChE,4BAAW,KAAM,KAAM,CAAE;AAAA,gBAC1B;AAAA,cACD;AAAA,YACD;AAEA,iBAAM,QAAQ,OAAO,QAAQ,WAAW;AACvC,kBAAK,OAAQ,KAAM,EAAE,SAAS,SAC3B,QAAQ,QAAQ,OAAQ,KAAM,EAAE,UAAU,OAAS;AAErD,uBAAQ,KAAM,EAAE,KAAK,KAAM,OAAQ;AACnC,0BAAU;AACV,uBAAO,OAAQ,OAAO,CAAE;AAAA,cACzB;AAAA,YACD;AAKA,gBAAK,WAAW,CAAC,SAAU;AAC1B,qBAAO,QAAS,MAAM,IAAK;AAAA,YAC5B;AAAA,UACD,CAAE;AAAA,QACH;AAAA,QACA,QAAQ,SAAU,MAAO;AACxB,cAAK,SAAS,OAAQ;AACrB,mBAAO,QAAQ;AAAA,UAChB;AACA,iBAAO,KAAK,KAAM,WAAW;AAC5B,gBAAI,OACH,OAAO,SAAS,IAAK,IAAK,GAC1B,QAAQ,KAAM,OAAO,OAAQ,GAC7B,QAAQ,KAAM,OAAO,YAAa,GAClC,SAAS,OAAO,QAChB,SAAS,QAAQ,MAAM,SAAS;AAGjC,iBAAK,SAAS;AAGd,mBAAO,MAAO,MAAM,MAAM,CAAC,CAAE;AAE7B,gBAAK,SAAS,MAAM,MAAO;AAC1B,oBAAM,KAAK,KAAM,MAAM,IAAK;AAAA,YAC7B;AAGA,iBAAM,QAAQ,OAAO,QAAQ,WAAW;AACvC,kBAAK,OAAQ,KAAM,EAAE,SAAS,QAAQ,OAAQ,KAAM,EAAE,UAAU,MAAO;AACtE,uBAAQ,KAAM,EAAE,KAAK,KAAM,IAAK;AAChC,uBAAO,OAAQ,OAAO,CAAE;AAAA,cACzB;AAAA,YACD;AAGA,iBAAM,QAAQ,GAAG,QAAQ,QAAQ,SAAU;AAC1C,kBAAK,MAAO,KAAM,KAAK,MAAO,KAAM,EAAE,QAAS;AAC9C,sBAAO,KAAM,EAAE,OAAO,KAAM,IAAK;AAAA,cAClC;AAAA,YACD;AAGA,mBAAO,KAAK;AAAA,UACb,CAAE;AAAA,QACH;AAAA,MACD,CAAE;AAEF,aAAO,KAAM,CAAE,UAAU,QAAQ,MAAO,GAAG,SAAU,IAAI,MAAO;AAC/D,YAAI,QAAQ,OAAO,GAAI,IAAK;AAC5B,eAAO,GAAI,IAAK,IAAI,SAAU,OAAO,QAAQ,UAAW;AACvD,iBAAO,SAAS,QAAQ,OAAO,UAAU,YACxC,MAAM,MAAO,MAAM,SAAU,IAC7B,KAAK,QAAS,MAAO,MAAM,IAAK,GAAG,OAAO,QAAQ,QAAS;AAAA,QAC7D;AAAA,MACD,CAAE;AAGF,aAAO,KAAM;AAAA,QACZ,WAAW,MAAO,MAAO;AAAA,QACzB,SAAS,MAAO,MAAO;AAAA,QACvB,aAAa,MAAO,QAAS;AAAA,QAC7B,QAAQ,EAAE,SAAS,OAAO;AAAA,QAC1B,SAAS,EAAE,SAAS,OAAO;AAAA,QAC3B,YAAY,EAAE,SAAS,SAAS;AAAA,MACjC,GAAG,SAAU,MAAM,OAAQ;AAC1B,eAAO,GAAI,IAAK,IAAI,SAAU,OAAO,QAAQ,UAAW;AACvD,iBAAO,KAAK,QAAS,OAAO,OAAO,QAAQ,QAAS;AAAA,QACrD;AAAA,MACD,CAAE;AAEF,aAAO,SAAS,CAAC;AACjB,aAAO,GAAG,OAAO,WAAW;AAC3B,YAAI,OACH,IAAI,GACJ,SAAS,OAAO;AAEjB,gBAAQ,KAAK,IAAI;AAEjB,eAAQ,IAAI,OAAO,QAAQ,KAAM;AAChC,kBAAQ,OAAQ,CAAE;AAGlB,cAAK,CAAC,MAAM,KAAK,OAAQ,CAAE,MAAM,OAAQ;AACxC,mBAAO,OAAQ,KAAK,CAAE;AAAA,UACvB;AAAA,QACD;AAEA,YAAK,CAAC,OAAO,QAAS;AACrB,iBAAO,GAAG,KAAK;AAAA,QAChB;AACA,gBAAQ;AAAA,MACT;AAEA,aAAO,GAAG,QAAQ,SAAU,OAAQ;AACnC,eAAO,OAAO,KAAM,KAAM;AAC1B,eAAO,GAAG,MAAM;AAAA,MACjB;AAEA,aAAO,GAAG,WAAW;AACrB,aAAO,GAAG,QAAQ,WAAW;AAC5B,YAAK,YAAa;AACjB;AAAA,QACD;AAEA,qBAAa;AACb,iBAAS;AAAA,MACV;AAEA,aAAO,GAAG,OAAO,WAAW;AAC3B,qBAAa;AAAA,MACd;AAEA,aAAO,GAAG,SAAS;AAAA,QAClB,MAAM;AAAA,QACN,MAAM;AAAA;AAAA,QAGN,UAAU;AAAA,MACX;AAIA,aAAO,GAAG,QAAQ,SAAU,MAAM,MAAO;AACxC,eAAO,OAAO,KAAK,OAAO,GAAG,OAAQ,IAAK,KAAK,OAAO;AACtD,eAAO,QAAQ;AAEf,eAAO,KAAK,MAAO,MAAM,SAAU,MAAM,OAAQ;AAChD,cAAI,UAAUnB,QAAO,WAAY,MAAM,IAAK;AAC5C,gBAAM,OAAO,WAAW;AACvB,YAAAA,QAAO,aAAc,OAAQ;AAAA,UAC9B;AAAA,QACD,CAAE;AAAA,MACH;AAGA,OAAE,WAAW;AACZ,YAAI,QAAQ,SAAS,cAAe,OAAQ,GAC3C,SAAS,SAAS,cAAe,QAAS,GAC1C,MAAM,OAAO,YAAa,SAAS,cAAe,QAAS,CAAE;AAE9D,cAAM,OAAO;AAIb,gBAAQ,UAAU,MAAM,UAAU;AAIlC,gBAAQ,cAAc,IAAI;AAI1B,gBAAQ,SAAS,cAAe,OAAQ;AACxC,cAAM,QAAQ;AACd,cAAM,OAAO;AACb,gBAAQ,aAAa,MAAM,UAAU;AAAA,MACtC,GAAI;AAGJ,UAAI,UACH,aAAa,OAAO,KAAK;AAE1B,aAAO,GAAG,OAAQ;AAAA,QACjB,MAAM,SAAU,MAAM,OAAQ;AAC7B,iBAAO,OAAQ,MAAM,OAAO,MAAM,MAAM,OAAO,UAAU,SAAS,CAAE;AAAA,QACrE;AAAA,QAEA,YAAY,SAAU,MAAO;AAC5B,iBAAO,KAAK,KAAM,WAAW;AAC5B,mBAAO,WAAY,MAAM,IAAK;AAAA,UAC/B,CAAE;AAAA,QACH;AAAA,MACD,CAAE;AAEF,aAAO,OAAQ;AAAA,QACd,MAAM,SAAU,MAAM,MAAM,OAAQ;AACnC,cAAI,KAAK,OACR,QAAQ,KAAK;AAGd,cAAK,UAAU,KAAK,UAAU,KAAK,UAAU,GAAI;AAChD;AAAA,UACD;AAGA,cAAK,OAAO,KAAK,iBAAiB,aAAc;AAC/C,mBAAO,OAAO,KAAM,MAAM,MAAM,KAAM;AAAA,UACvC;AAIA,cAAK,UAAU,KAAK,CAAC,OAAO,SAAU,IAAK,GAAI;AAC9C,oBAAQ,OAAO,UAAW,KAAK,YAAY,CAAE,MAC1C,OAAO,KAAK,MAAM,KAAK,KAAM,IAAK,IAAI,WAAW;AAAA,UACrD;AAEA,cAAK,UAAU,QAAY;AAC1B,gBAAK,UAAU,MAAO;AACrB,qBAAO,WAAY,MAAM,IAAK;AAC9B;AAAA,YACD;AAEA,gBAAK,SAAS,SAAS,UACpB,MAAM,MAAM,IAAK,MAAM,OAAO,IAAK,OAAQ,QAAY;AACzD,qBAAO;AAAA,YACR;AAEA,iBAAK,aAAc,MAAM,QAAQ,EAAG;AACpC,mBAAO;AAAA,UACR;AAEA,cAAK,SAAS,SAAS,UAAW,MAAM,MAAM,IAAK,MAAM,IAAK,OAAQ,MAAO;AAC5E,mBAAO;AAAA,UACR;AAEA,gBAAM,OAAO,KAAK,KAAM,MAAM,IAAK;AAGnC,iBAAO,OAAO,OAAO,SAAY;AAAA,QAClC;AAAA,QAEA,WAAW;AAAA,UACV,MAAM;AAAA,YACL,KAAK,SAAU,MAAM,OAAQ;AAC5B,kBAAK,CAAC,QAAQ,cAAc,UAAU,WACrC,SAAU,MAAM,OAAQ,GAAI;AAC5B,oBAAI,MAAM,KAAK;AACf,qBAAK,aAAc,QAAQ,KAAM;AACjC,oBAAK,KAAM;AACV,uBAAK,QAAQ;AAAA,gBACd;AACA,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QAEA,YAAY,SAAU,MAAM,OAAQ;AACnC,cAAI,MACH,IAAI,GAIJ,YAAY,SAAS,MAAM,MAAO,aAAc;AAEjD,cAAK,aAAa,KAAK,aAAa,GAAI;AACvC,mBAAU,OAAO,UAAW,GAAI,GAAM;AACrC,mBAAK,gBAAiB,IAAK;AAAA,YAC5B;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAE;AAGF,iBAAW;AAAA,QACV,KAAK,SAAU,MAAM,OAAO,MAAO;AAClC,cAAK,UAAU,OAAQ;AAGtB,mBAAO,WAAY,MAAM,IAAK;AAAA,UAC/B,OAAO;AACN,iBAAK,aAAc,MAAM,IAAK;AAAA,UAC/B;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAEA,aAAO,KAAM,OAAO,KAAK,MAAM,KAAK,OAAO,MAAO,MAAO,GAAG,SAAU,IAAI,MAAO;AAChF,YAAI,SAAS,WAAY,IAAK,KAAK,OAAO,KAAK;AAE/C,mBAAY,IAAK,IAAI,SAAU,MAAMkB,OAAM,OAAQ;AAClD,cAAI,KAAK,QACR,gBAAgBA,MAAK,YAAY;AAElC,cAAK,CAAC,OAAQ;AAGb,qBAAS,WAAY,aAAc;AACnC,uBAAY,aAAc,IAAI;AAC9B,kBAAM,OAAQ,MAAMA,OAAM,KAAM,KAAK,OACpC,gBACA;AACD,uBAAY,aAAc,IAAI;AAAA,UAC/B;AACA,iBAAO;AAAA,QACR;AAAA,MACD,CAAE;AAKF,UAAI,aAAa,uCAChB,aAAa;AAEd,aAAO,GAAG,OAAQ;AAAA,QACjB,MAAM,SAAU,MAAM,OAAQ;AAC7B,iBAAO,OAAQ,MAAM,OAAO,MAAM,MAAM,OAAO,UAAU,SAAS,CAAE;AAAA,QACrE;AAAA,QAEA,YAAY,SAAU,MAAO;AAC5B,iBAAO,KAAK,KAAM,WAAW;AAC5B,mBAAO,KAAM,OAAO,QAAS,IAAK,KAAK,IAAK;AAAA,UAC7C,CAAE;AAAA,QACH;AAAA,MACD,CAAE;AAEF,aAAO,OAAQ;AAAA,QACd,MAAM,SAAU,MAAM,MAAM,OAAQ;AACnC,cAAI,KAAK,OACR,QAAQ,KAAK;AAGd,cAAK,UAAU,KAAK,UAAU,KAAK,UAAU,GAAI;AAChD;AAAA,UACD;AAEA,cAAK,UAAU,KAAK,CAAC,OAAO,SAAU,IAAK,GAAI;AAG9C,mBAAO,OAAO,QAAS,IAAK,KAAK;AACjC,oBAAQ,OAAO,UAAW,IAAK;AAAA,UAChC;AAEA,cAAK,UAAU,QAAY;AAC1B,gBAAK,SAAS,SAAS,UACpB,MAAM,MAAM,IAAK,MAAM,OAAO,IAAK,OAAQ,QAAY;AACzD,qBAAO;AAAA,YACR;AAEA,mBAAS,KAAM,IAAK,IAAI;AAAA,UACzB;AAEA,cAAK,SAAS,SAAS,UAAW,MAAM,MAAM,IAAK,MAAM,IAAK,OAAQ,MAAO;AAC5E,mBAAO;AAAA,UACR;AAEA,iBAAO,KAAM,IAAK;AAAA,QACnB;AAAA,QAEA,WAAW;AAAA,UACV,UAAU;AAAA,YACT,KAAK,SAAU,MAAO;AAMrB,kBAAI,WAAW,OAAO,KAAK,KAAM,MAAM,UAAW;AAElD,kBAAK,UAAW;AACf,uBAAO,SAAU,UAAU,EAAG;AAAA,cAC/B;AAEA,kBACC,WAAW,KAAM,KAAK,QAAS,KAC/B,WAAW,KAAM,KAAK,QAAS,KAC/B,KAAK,MACJ;AACD,uBAAO;AAAA,cACR;AAEA,qBAAO;AAAA,YACR;AAAA,UACD;AAAA,QACD;AAAA,QAEA,SAAS;AAAA,UACR,OAAO;AAAA,UACP,SAAS;AAAA,QACV;AAAA,MACD,CAAE;AAUF,UAAK,CAAC,QAAQ,aAAc;AAC3B,eAAO,UAAU,WAAW;AAAA,UAC3B,KAAK,SAAU,MAAO;AAIrB,gBAAI,SAAS,KAAK;AAClB,gBAAK,UAAU,OAAO,YAAa;AAClC,qBAAO,WAAW;AAAA,YACnB;AACA,mBAAO;AAAA,UACR;AAAA,UACA,KAAK,SAAU,MAAO;AAIrB,gBAAI,SAAS,KAAK;AAClB,gBAAK,QAAS;AACb,qBAAO;AAEP,kBAAK,OAAO,YAAa;AACxB,uBAAO,WAAW;AAAA,cACnB;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO,KAAM;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD,GAAG,WAAW;AACb,eAAO,QAAS,KAAK,YAAY,CAAE,IAAI;AAAA,MACxC,CAAE;AAOD,eAAS,iBAAkB,OAAQ;AAClC,YAAI,SAAS,MAAM,MAAO,aAAc,KAAK,CAAC;AAC9C,eAAO,OAAO,KAAM,GAAI;AAAA,MACzB;AAGD,eAAS,SAAU,MAAO;AACzB,eAAO,KAAK,gBAAgB,KAAK,aAAc,OAAQ,KAAK;AAAA,MAC7D;AAEA,eAAS,eAAgB,OAAQ;AAChC,YAAK,MAAM,QAAS,KAAM,GAAI;AAC7B,iBAAO;AAAA,QACR;AACA,YAAK,OAAO,UAAU,UAAW;AAChC,iBAAO,MAAM,MAAO,aAAc,KAAK,CAAC;AAAA,QACzC;AACA,eAAO,CAAC;AAAA,MACT;AAEA,aAAO,GAAG,OAAQ;AAAA,QACjB,UAAU,SAAU,OAAQ;AAC3B,cAAI,YAAY,KAAK,UAAU,WAAW,GAAG;AAE7C,cAAK,WAAY,KAAM,GAAI;AAC1B,mBAAO,KAAK,KAAM,SAAU,GAAI;AAC/B,qBAAQ,IAAK,EAAE,SAAU,MAAM,KAAM,MAAM,GAAG,SAAU,IAAK,CAAE,CAAE;AAAA,YAClE,CAAE;AAAA,UACH;AAEA,uBAAa,eAAgB,KAAM;AAEnC,cAAK,WAAW,QAAS;AACxB,mBAAO,KAAK,KAAM,WAAW;AAC5B,yBAAW,SAAU,IAAK;AAC1B,oBAAM,KAAK,aAAa,KAAO,MAAM,iBAAkB,QAAS,IAAI;AAEpE,kBAAK,KAAM;AACV,qBAAM,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAM;AACzC,8BAAY,WAAY,CAAE;AAC1B,sBAAK,IAAI,QAAS,MAAM,YAAY,GAAI,IAAI,GAAI;AAC/C,2BAAO,YAAY;AAAA,kBACpB;AAAA,gBACD;AAGA,6BAAa,iBAAkB,GAAI;AACnC,oBAAK,aAAa,YAAa;AAC9B,uBAAK,aAAc,SAAS,UAAW;AAAA,gBACxC;AAAA,cACD;AAAA,YACD,CAAE;AAAA,UACH;AAEA,iBAAO;AAAA,QACR;AAAA,QAEA,aAAa,SAAU,OAAQ;AAC9B,cAAI,YAAY,KAAK,UAAU,WAAW,GAAG;AAE7C,cAAK,WAAY,KAAM,GAAI;AAC1B,mBAAO,KAAK,KAAM,SAAU,GAAI;AAC/B,qBAAQ,IAAK,EAAE,YAAa,MAAM,KAAM,MAAM,GAAG,SAAU,IAAK,CAAE,CAAE;AAAA,YACrE,CAAE;AAAA,UACH;AAEA,cAAK,CAAC,UAAU,QAAS;AACxB,mBAAO,KAAK,KAAM,SAAS,EAAG;AAAA,UAC/B;AAEA,uBAAa,eAAgB,KAAM;AAEnC,cAAK,WAAW,QAAS;AACxB,mBAAO,KAAK,KAAM,WAAW;AAC5B,yBAAW,SAAU,IAAK;AAG1B,oBAAM,KAAK,aAAa,KAAO,MAAM,iBAAkB,QAAS,IAAI;AAEpE,kBAAK,KAAM;AACV,qBAAM,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAM;AACzC,8BAAY,WAAY,CAAE;AAG1B,yBAAQ,IAAI,QAAS,MAAM,YAAY,GAAI,IAAI,IAAK;AACnD,0BAAM,IAAI,QAAS,MAAM,YAAY,KAAK,GAAI;AAAA,kBAC/C;AAAA,gBACD;AAGA,6BAAa,iBAAkB,GAAI;AACnC,oBAAK,aAAa,YAAa;AAC9B,uBAAK,aAAc,SAAS,UAAW;AAAA,gBACxC;AAAA,cACD;AAAA,YACD,CAAE;AAAA,UACH;AAEA,iBAAO;AAAA,QACR;AAAA,QAEA,aAAa,SAAU,OAAO,UAAW;AACxC,cAAI,YAAY,WAAW,GAAG,MAC7B,OAAO,OAAO,OACd,eAAe,SAAS,YAAY,MAAM,QAAS,KAAM;AAE1D,cAAK,WAAY,KAAM,GAAI;AAC1B,mBAAO,KAAK,KAAM,SAAUV,IAAI;AAC/B,qBAAQ,IAAK,EAAE;AAAA,gBACd,MAAM,KAAM,MAAMA,IAAG,SAAU,IAAK,GAAG,QAAS;AAAA,gBAChD;AAAA,cACD;AAAA,YACD,CAAE;AAAA,UACH;AAEA,cAAK,OAAO,aAAa,aAAa,cAAe;AACpD,mBAAO,WAAW,KAAK,SAAU,KAAM,IAAI,KAAK,YAAa,KAAM;AAAA,UACpE;AAEA,uBAAa,eAAgB,KAAM;AAEnC,iBAAO,KAAK,KAAM,WAAW;AAC5B,gBAAK,cAAe;AAGnB,qBAAO,OAAQ,IAAK;AAEpB,mBAAM,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAM;AACzC,4BAAY,WAAY,CAAE;AAG1B,oBAAK,KAAK,SAAU,SAAU,GAAI;AACjC,uBAAK,YAAa,SAAU;AAAA,gBAC7B,OAAO;AACN,uBAAK,SAAU,SAAU;AAAA,gBAC1B;AAAA,cACD;AAAA,YAGD,WAAY,UAAU,UAAa,SAAS,WAAY;AACvD,0BAAY,SAAU,IAAK;AAC3B,kBAAK,WAAY;AAGhB,yBAAS,IAAK,MAAM,iBAAiB,SAAU;AAAA,cAChD;AAMA,kBAAK,KAAK,cAAe;AACxB,qBAAK;AAAA,kBAAc;AAAA,kBAClB,aAAa,UAAU,QACtB,KACA,SAAS,IAAK,MAAM,eAAgB,KAAK;AAAA,gBAC3C;AAAA,cACD;AAAA,YACD;AAAA,UACD,CAAE;AAAA,QACH;AAAA,QAEA,UAAU,SAAU,UAAW;AAC9B,cAAI,WAAW,MACd,IAAI;AAEL,sBAAY,MAAM,WAAW;AAC7B,iBAAU,OAAO,KAAM,GAAI,GAAM;AAChC,gBAAK,KAAK,aAAa,MACpB,MAAM,iBAAkB,SAAU,IAAK,CAAE,IAAI,KAAM,QAAS,SAAU,IAAI,IAAK;AACjF,qBAAO;AAAA,YACR;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,MACD,CAAE;AAKF,UAAI,UAAU;AAEd,aAAO,GAAG,OAAQ;AAAA,QACjB,KAAK,SAAU,OAAQ;AACtB,cAAI,OAAO,KAAK,iBACf,OAAO,KAAM,CAAE;AAEhB,cAAK,CAAC,UAAU,QAAS;AACxB,gBAAK,MAAO;AACX,sBAAQ,OAAO,SAAU,KAAK,IAAK,KAClC,OAAO,SAAU,KAAK,SAAS,YAAY,CAAE;AAE9C,kBAAK,SACJ,SAAS,UACP,MAAM,MAAM,IAAK,MAAM,OAAQ,OAAQ,QACxC;AACD,uBAAO;AAAA,cACR;AAEA,oBAAM,KAAK;AAGX,kBAAK,OAAO,QAAQ,UAAW;AAC9B,uBAAO,IAAI,QAAS,SAAS,EAAG;AAAA,cACjC;AAGA,qBAAO,OAAO,OAAO,KAAK;AAAA,YAC3B;AAEA;AAAA,UACD;AAEA,4BAAkB,WAAY,KAAM;AAEpC,iBAAO,KAAK,KAAM,SAAU,GAAI;AAC/B,gBAAI;AAEJ,gBAAK,KAAK,aAAa,GAAI;AAC1B;AAAA,YACD;AAEA,gBAAK,iBAAkB;AACtB,oBAAM,MAAM,KAAM,MAAM,GAAG,OAAQ,IAAK,EAAE,IAAI,CAAE;AAAA,YACjD,OAAO;AACN,oBAAM;AAAA,YACP;AAGA,gBAAK,OAAO,MAAO;AAClB,oBAAM;AAAA,YAEP,WAAY,OAAO,QAAQ,UAAW;AACrC,qBAAO;AAAA,YAER,WAAY,MAAM,QAAS,GAAI,GAAI;AAClC,oBAAM,OAAO,IAAK,KAAK,SAAUM,QAAQ;AACxC,uBAAOA,UAAS,OAAO,KAAKA,SAAQ;AAAA,cACrC,CAAE;AAAA,YACH;AAEA,oBAAQ,OAAO,SAAU,KAAK,IAAK,KAAK,OAAO,SAAU,KAAK,SAAS,YAAY,CAAE;AAGrF,gBAAK,CAAC,SAAS,EAAG,SAAS,UAAW,MAAM,IAAK,MAAM,KAAK,OAAQ,MAAM,QAAY;AACrF,mBAAK,QAAQ;AAAA,YACd;AAAA,UACD,CAAE;AAAA,QACH;AAAA,MACD,CAAE;AAEF,aAAO,OAAQ;AAAA,QACd,UAAU;AAAA,UACT,QAAQ;AAAA,YACP,KAAK,SAAU,MAAO;AAErB,kBAAI,MAAM,OAAO,KAAK,KAAM,MAAM,OAAQ;AAC1C,qBAAO,OAAO,OACb;AAAA;AAAA;AAAA;AAAA;AAAA,gBAMA,iBAAkB,OAAO,KAAM,IAAK,CAAE;AAAA;AAAA,YACxC;AAAA,UACD;AAAA,UACA,QAAQ;AAAA,YACP,KAAK,SAAU,MAAO;AACrB,kBAAI,OAAO,QAAQ,GAClB,UAAU,KAAK,SACf,QAAQ,KAAK,eACb,MAAM,KAAK,SAAS,cACpB,SAAS,MAAM,OAAO,CAAC,GACvB,MAAM,MAAM,QAAQ,IAAI,QAAQ;AAEjC,kBAAK,QAAQ,GAAI;AAChB,oBAAI;AAAA,cAEL,OAAO;AACN,oBAAI,MAAM,QAAQ;AAAA,cACnB;AAGA,qBAAQ,IAAI,KAAK,KAAM;AACtB,yBAAS,QAAS,CAAE;AAIpB,qBAAO,OAAO,YAAY,MAAM;AAAA,gBAG9B,CAAC,OAAO,aACN,CAAC,OAAO,WAAW,YACpB,CAAC,SAAU,OAAO,YAAY,UAAW,IAAM;AAGjD,0BAAQ,OAAQ,MAAO,EAAE,IAAI;AAG7B,sBAAK,KAAM;AACV,2BAAO;AAAA,kBACR;AAGA,yBAAO,KAAM,KAAM;AAAA,gBACpB;AAAA,cACD;AAEA,qBAAO;AAAA,YACR;AAAA,YAEA,KAAK,SAAU,MAAM,OAAQ;AAC5B,kBAAI,WAAW,QACd,UAAU,KAAK,SACf,SAAS,OAAO,UAAW,KAAM,GACjC,IAAI,QAAQ;AAEb,qBAAQ,KAAM;AACb,yBAAS,QAAS,CAAE;AAIpB,oBAAK,OAAO,WACX,OAAO,QAAS,OAAO,SAAS,OAAO,IAAK,MAAO,GAAG,MAAO,IAAI,IAChE;AACD,8BAAY;AAAA,gBACb;AAAA,cAGD;AAGA,kBAAK,CAAC,WAAY;AACjB,qBAAK,gBAAgB;AAAA,cACtB;AACA,qBAAO;AAAA,YACR;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAE;AAGF,aAAO,KAAM,CAAE,SAAS,UAAW,GAAG,WAAW;AAChD,eAAO,SAAU,IAAK,IAAI;AAAA,UACzB,KAAK,SAAU,MAAM,OAAQ;AAC5B,gBAAK,MAAM,QAAS,KAAM,GAAI;AAC7B,qBAAS,KAAK,UAAU,OAAO,QAAS,OAAQ,IAAK,EAAE,IAAI,GAAG,KAAM,IAAI;AAAA,YACzE;AAAA,UACD;AAAA,QACD;AACA,YAAK,CAAC,QAAQ,SAAU;AACvB,iBAAO,SAAU,IAAK,EAAE,MAAM,SAAU,MAAO;AAC9C,mBAAO,KAAK,aAAc,OAAQ,MAAM,OAAO,OAAO,KAAK;AAAA,UAC5D;AAAA,QACD;AAAA,MACD,CAAE;AAMF,UAAI,WAAWd,QAAO;AAEtB,UAAI,QAAQ,EAAE,MAAM,KAAK,IAAI,EAAE;AAE/B,UAAI,SAAW;AAKf,aAAO,WAAW,SAAU,MAAO;AAClC,YAAI,KAAK;AACT,YAAK,CAAC,QAAQ,OAAO,SAAS,UAAW;AACxC,iBAAO;AAAA,QACR;AAIA,YAAI;AACH,gBAAQ,IAAIA,QAAO,UAAU,EAAI,gBAAiB,MAAM,UAAW;AAAA,QACpE,SAAU,GAAI;AAAA,QAAC;AAEf,0BAAkB,OAAO,IAAI,qBAAsB,aAAc,EAAG,CAAE;AACtE,YAAK,CAAC,OAAO,iBAAkB;AAC9B,iBAAO,MAAO,mBACb,kBACC,OAAO,IAAK,gBAAgB,YAAY,SAAU,IAAK;AACtD,mBAAO,GAAG;AAAA,UACX,CAAE,EAAE,KAAM,IAAK,IACf,KACA;AAAA,QACH;AACA,eAAO;AAAA,MACR;AAGA,UAAI,cAAc,mCACjB,0BAA0B,SAAU,GAAI;AACvC,UAAE,gBAAgB;AAAA,MACnB;AAED,aAAO,OAAQ,OAAO,OAAO;AAAA,QAE5B,SAAS,SAAU,OAAO,MAAM,MAAM,cAAe;AAEpD,cAAI,GAAG,KAAK,KAAK,YAAY,QAAQ,QAAQ,SAAS,aACrD,YAAY,CAAE,QAAQ,QAAS,GAC/B,OAAO,OAAO,KAAM,OAAO,MAAO,IAAI,MAAM,OAAO,OACnD,aAAa,OAAO,KAAM,OAAO,WAAY,IAAI,MAAM,UAAU,MAAO,GAAI,IAAI,CAAC;AAElF,gBAAM,cAAc,MAAM,OAAO,QAAQ;AAGzC,cAAK,KAAK,aAAa,KAAK,KAAK,aAAa,GAAI;AACjD;AAAA,UACD;AAGA,cAAK,YAAY,KAAM,OAAO,OAAO,MAAM,SAAU,GAAI;AACxD;AAAA,UACD;AAEA,cAAK,KAAK,QAAS,GAAI,IAAI,IAAK;AAG/B,yBAAa,KAAK,MAAO,GAAI;AAC7B,mBAAO,WAAW,MAAM;AACxB,uBAAW,KAAK;AAAA,UACjB;AACA,mBAAS,KAAK,QAAS,GAAI,IAAI,KAAK,OAAO;AAG3C,kBAAQ,MAAO,OAAO,OAAQ,IAC7B,QACA,IAAI,OAAO,MAAO,MAAM,OAAO,UAAU,YAAY,KAAM;AAG5D,gBAAM,YAAY,eAAe,IAAI;AACrC,gBAAM,YAAY,WAAW,KAAM,GAAI;AACvC,gBAAM,aAAa,MAAM,YACxB,IAAI,OAAQ,YAAY,WAAW,KAAM,eAAgB,IAAI,SAAU,IACvE;AAGD,gBAAM,SAAS;AACf,cAAK,CAAC,MAAM,QAAS;AACpB,kBAAM,SAAS;AAAA,UAChB;AAGA,iBAAO,QAAQ,OACd,CAAE,KAAM,IACR,OAAO,UAAW,MAAM,CAAE,KAAM,CAAE;AAGnC,oBAAU,OAAO,MAAM,QAAS,IAAK,KAAK,CAAC;AAC3C,cAAK,CAAC,gBAAgB,QAAQ,WAAW,QAAQ,QAAQ,MAAO,MAAM,IAAK,MAAM,OAAQ;AACxF;AAAA,UACD;AAIA,cAAK,CAAC,gBAAgB,CAAC,QAAQ,YAAY,CAAC,SAAU,IAAK,GAAI;AAE9D,yBAAa,QAAQ,gBAAgB;AACrC,gBAAK,CAAC,YAAY,KAAM,aAAa,IAAK,GAAI;AAC7C,oBAAM,IAAI;AAAA,YACX;AACA,mBAAQ,KAAK,MAAM,IAAI,YAAa;AACnC,wBAAU,KAAM,GAAI;AACpB,oBAAM;AAAA,YACP;AAGA,gBAAK,SAAU,KAAK,iBAAiB,WAAa;AACjD,wBAAU,KAAM,IAAI,eAAe,IAAI,gBAAgBA,OAAO;AAAA,YAC/D;AAAA,UACD;AAGA,cAAI;AACJ,kBAAU,MAAM,UAAW,GAAI,MAAO,CAAC,MAAM,qBAAqB,GAAI;AACrE,0BAAc;AACd,kBAAM,OAAO,IAAI,IAChB,aACA,QAAQ,YAAY;AAGrB,sBAAW,SAAS,IAAK,KAAK,QAAS,KAAK,uBAAO,OAAQ,IAAK,GAAK,MAAM,IAAK,KAC/E,SAAS,IAAK,KAAK,QAAS;AAC7B,gBAAK,QAAS;AACb,qBAAO,MAAO,KAAK,IAAK;AAAA,YACzB;AAGA,qBAAS,UAAU,IAAK,MAAO;AAC/B,gBAAK,UAAU,OAAO,SAAS,WAAY,GAAI,GAAI;AAClD,oBAAM,SAAS,OAAO,MAAO,KAAK,IAAK;AACvC,kBAAK,MAAM,WAAW,OAAQ;AAC7B,sBAAM,eAAe;AAAA,cACtB;AAAA,YACD;AAAA,UACD;AACA,gBAAM,OAAO;AAGb,cAAK,CAAC,gBAAgB,CAAC,MAAM,mBAAmB,GAAI;AAEnD,iBAAO,CAAC,QAAQ,YACf,QAAQ,SAAS,MAAO,UAAU,IAAI,GAAG,IAAK,MAAM,UACpD,WAAY,IAAK,GAAI;AAIrB,kBAAK,UAAU,WAAY,KAAM,IAAK,CAAE,KAAK,CAAC,SAAU,IAAK,GAAI;AAGhE,sBAAM,KAAM,MAAO;AAEnB,oBAAK,KAAM;AACV,uBAAM,MAAO,IAAI;AAAA,gBAClB;AAGA,uBAAO,MAAM,YAAY;AAEzB,oBAAK,MAAM,qBAAqB,GAAI;AACnC,8BAAY,iBAAkB,MAAM,uBAAwB;AAAA,gBAC7D;AAEA,qBAAM,IAAK,EAAE;AAEb,oBAAK,MAAM,qBAAqB,GAAI;AACnC,8BAAY,oBAAqB,MAAM,uBAAwB;AAAA,gBAChE;AAEA,uBAAO,MAAM,YAAY;AAEzB,oBAAK,KAAM;AACV,uBAAM,MAAO,IAAI;AAAA,gBAClB;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,iBAAO,MAAM;AAAA,QACd;AAAA;AAAA;AAAA,QAIA,UAAU,SAAU,MAAM,MAAM,OAAQ;AACvC,cAAI,IAAI,OAAO;AAAA,YACd,IAAI,OAAO,MAAM;AAAA,YACjB;AAAA,YACA;AAAA,cACC;AAAA,cACA,aAAa;AAAA,YACd;AAAA,UACD;AAEA,iBAAO,MAAM,QAAS,GAAG,MAAM,IAAK;AAAA,QACrC;AAAA,MAED,CAAE;AAEF,aAAO,GAAG,OAAQ;AAAA,QAEjB,SAAS,SAAU,MAAM,MAAO;AAC/B,iBAAO,KAAK,KAAM,WAAW;AAC5B,mBAAO,MAAM,QAAS,MAAM,MAAM,IAAK;AAAA,UACxC,CAAE;AAAA,QACH;AAAA,QACA,gBAAgB,SAAU,MAAM,MAAO;AACtC,cAAI,OAAO,KAAM,CAAE;AACnB,cAAK,MAAO;AACX,mBAAO,OAAO,MAAM,QAAS,MAAM,MAAM,MAAM,IAAK;AAAA,UACrD;AAAA,QACD;AAAA,MACD,CAAE;AAGF,UACC,WAAW,SACX,QAAQ,UACR,kBAAkB,yCAClB,eAAe;AAEhB,eAAS,YAAa,QAAQ,KAAK,aAAa,KAAM;AACrD,YAAI;AAEJ,YAAK,MAAM,QAAS,GAAI,GAAI;AAG3B,iBAAO,KAAM,KAAK,SAAU,GAAG,GAAI;AAClC,gBAAK,eAAe,SAAS,KAAM,MAAO,GAAI;AAG7C,kBAAK,QAAQ,CAAE;AAAA,YAEhB,OAAO;AAGN;AAAA,gBACC,SAAS,OAAQ,OAAO,MAAM,YAAY,KAAK,OAAO,IAAI,MAAO;AAAA,gBACjE;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AAAA,YACD;AAAA,UACD,CAAE;AAAA,QAEH,WAAY,CAAC,eAAe,OAAQ,GAAI,MAAM,UAAW;AAGxD,eAAM,QAAQ,KAAM;AACnB,wBAAa,SAAS,MAAM,OAAO,KAAK,IAAK,IAAK,GAAG,aAAa,GAAI;AAAA,UACvE;AAAA,QAED,OAAO;AAGN,cAAK,QAAQ,GAAI;AAAA,QAClB;AAAA,MACD;AAIA,aAAO,QAAQ,SAAU,GAAG,aAAc;AACzC,YAAI,QACH,IAAI,CAAC,GACL,MAAM,SAAU,KAAK,iBAAkB;AAGtC,cAAI,QAAQ,WAAY,eAAgB,IACvC,gBAAgB,IAChB;AAED,YAAG,EAAE,MAAO,IAAI,mBAAoB,GAAI,IAAI,MAC3C,mBAAoB,SAAS,OAAO,KAAK,KAAM;AAAA,QACjD;AAED,YAAK,KAAK,MAAO;AAChB,iBAAO;AAAA,QACR;AAGA,YAAK,MAAM,QAAS,CAAE,KAAO,EAAE,UAAU,CAAC,OAAO,cAAe,CAAE,GAAM;AAGvE,iBAAO,KAAM,GAAG,WAAW;AAC1B,gBAAK,KAAK,MAAM,KAAK,KAAM;AAAA,UAC5B,CAAE;AAAA,QAEH,OAAO;AAIN,eAAM,UAAU,GAAI;AACnB,wBAAa,QAAQ,EAAG,MAAO,GAAG,aAAa,GAAI;AAAA,UACpD;AAAA,QACD;AAGA,eAAO,EAAE,KAAM,GAAI;AAAA,MACpB;AAEA,aAAO,GAAG,OAAQ;AAAA,QACjB,WAAW,WAAW;AACrB,iBAAO,OAAO,MAAO,KAAK,eAAe,CAAE;AAAA,QAC5C;AAAA,QACA,gBAAgB,WAAW;AAC1B,iBAAO,KAAK,IAAK,WAAW;AAG3B,gBAAI,WAAW,OAAO,KAAM,MAAM,UAAW;AAC7C,mBAAO,WAAW,OAAO,UAAW,QAAS,IAAI;AAAA,UAClD,CAAE,EAAE,OAAQ,WAAW;AACtB,gBAAI,OAAO,KAAK;AAGhB,mBAAO,KAAK,QAAQ,CAAC,OAAQ,IAAK,EAAE,GAAI,WAAY,KACnD,aAAa,KAAM,KAAK,QAAS,KAAK,CAAC,gBAAgB,KAAM,IAAK,MAChE,KAAK,WAAW,CAAC,eAAe,KAAM,IAAK;AAAA,UAC/C,CAAE,EAAE,IAAK,SAAU,IAAI,MAAO;AAC7B,gBAAI,MAAM,OAAQ,IAAK,EAAE,IAAI;AAE7B,gBAAK,OAAO,MAAO;AAClB,qBAAO;AAAA,YACR;AAEA,gBAAK,MAAM,QAAS,GAAI,GAAI;AAC3B,qBAAO,OAAO,IAAK,KAAK,SAAUqB,MAAM;AACvC,uBAAO,EAAE,MAAM,KAAK,MAAM,OAAOA,KAAI,QAAS,OAAO,MAAO,EAAE;AAAA,cAC/D,CAAE;AAAA,YACH;AAEA,mBAAO,EAAE,MAAM,KAAK,MAAM,OAAO,IAAI,QAAS,OAAO,MAAO,EAAE;AAAA,UAC/D,CAAE,EAAE,IAAI;AAAA,QACT;AAAA,MACD,CAAE;AAGF,UACC,MAAM,QACN,QAAQ,QACR,aAAa,iBACb,WAAW,8BAGX,iBAAiB,6DACjB,aAAa,kBACb,YAAY,SAWZ,aAAa,CAAC,GAOd,aAAa,CAAC,GAGd,WAAW,KAAK,OAAQ,GAAI,GAG5B,eAAe,SAAS,cAAe,GAAI;AAE5C,mBAAa,OAAO,SAAS;AAG7B,eAAS,4BAA6B,WAAY;AAGjD,eAAO,SAAU,oBAAoB,MAAO;AAE3C,cAAK,OAAO,uBAAuB,UAAW;AAC7C,mBAAO;AACP,iCAAqB;AAAA,UACtB;AAEA,cAAI,UACH,IAAI,GACJ,YAAY,mBAAmB,YAAY,EAAE,MAAO,aAAc,KAAK,CAAC;AAEzE,cAAK,WAAY,IAAK,GAAI;AAGzB,mBAAU,WAAW,UAAW,GAAI,GAAM;AAGzC,kBAAK,SAAU,CAAE,MAAM,KAAM;AAC5B,2BAAW,SAAS,MAAO,CAAE,KAAK;AAClC,iBAAE,UAAW,QAAS,IAAI,UAAW,QAAS,KAAK,CAAC,GAAI,QAAS,IAAK;AAAA,cAGvE,OAAO;AACN,iBAAE,UAAW,QAAS,IAAI,UAAW,QAAS,KAAK,CAAC,GAAI,KAAM,IAAK;AAAA,cACpE;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAGA,eAAS,8BAA+B,WAAW,SAAS,iBAAiB,OAAQ;AAEpF,YAAI,YAAY,CAAC,GAChB,mBAAqB,cAAc;AAEpC,iBAAS,QAAS,UAAW;AAC5B,cAAI;AACJ,oBAAW,QAAS,IAAI;AACxB,iBAAO,KAAM,UAAW,QAAS,KAAK,CAAC,GAAG,SAAU,GAAG,oBAAqB;AAC3E,gBAAI,sBAAsB,mBAAoB,SAAS,iBAAiB,KAAM;AAC9E,gBAAK,OAAO,wBAAwB,YACnC,CAAC,oBAAoB,CAAC,UAAW,mBAAoB,GAAI;AAEzD,sBAAQ,UAAU,QAAS,mBAAoB;AAC/C,sBAAS,mBAAoB;AAC7B,qBAAO;AAAA,YACR,WAAY,kBAAmB;AAC9B,qBAAO,EAAG,WAAW;AAAA,YACtB;AAAA,UACD,CAAE;AACF,iBAAO;AAAA,QACR;AAEA,eAAO,QAAS,QAAQ,UAAW,CAAE,CAAE,KAAK,CAAC,UAAW,GAAI,KAAK,QAAS,GAAI;AAAA,MAC/E;AAKA,eAAS,WAAY,QAAQ,KAAM;AAClC,YAAI,KAAK,MACR,cAAc,OAAO,aAAa,eAAe,CAAC;AAEnD,aAAM,OAAO,KAAM;AAClB,cAAK,IAAK,GAAI,MAAM,QAAY;AAC/B,aAAE,YAAa,GAAI,IAAI,SAAW,SAAU,OAAO,CAAC,IAAS,GAAI,IAAI,IAAK,GAAI;AAAA,UAC/E;AAAA,QACD;AACA,YAAK,MAAO;AACX,iBAAO,OAAQ,MAAM,QAAQ,IAAK;AAAA,QACnC;AAEA,eAAO;AAAA,MACR;AAMA,eAAS,oBAAqB,GAAG,OAAO,WAAY;AAEnD,YAAI,IAAI,MAAM,eAAe,eAC5B,WAAW,EAAE,UACb,YAAY,EAAE;AAGf,eAAQ,UAAW,CAAE,MAAM,KAAM;AAChC,oBAAU,MAAM;AAChB,cAAK,OAAO,QAAY;AACvB,iBAAK,EAAE,YAAY,MAAM,kBAAmB,cAAe;AAAA,UAC5D;AAAA,QACD;AAGA,YAAK,IAAK;AACT,eAAM,QAAQ,UAAW;AACxB,gBAAK,SAAU,IAAK,KAAK,SAAU,IAAK,EAAE,KAAM,EAAG,GAAI;AACtD,wBAAU,QAAS,IAAK;AACxB;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAGA,YAAK,UAAW,CAAE,KAAK,WAAY;AAClC,0BAAgB,UAAW,CAAE;AAAA,QAC9B,OAAO;AAGN,eAAM,QAAQ,WAAY;AACzB,gBAAK,CAAC,UAAW,CAAE,KAAK,EAAE,WAAY,OAAO,MAAM,UAAW,CAAE,CAAE,GAAI;AACrE,8BAAgB;AAChB;AAAA,YACD;AACA,gBAAK,CAAC,eAAgB;AACrB,8BAAgB;AAAA,YACjB;AAAA,UACD;AAGA,0BAAgB,iBAAiB;AAAA,QAClC;AAKA,YAAK,eAAgB;AACpB,cAAK,kBAAkB,UAAW,CAAE,GAAI;AACvC,sBAAU,QAAS,aAAc;AAAA,UAClC;AACA,iBAAO,UAAW,aAAc;AAAA,QACjC;AAAA,MACD;AAKA,eAAS,YAAa,GAAG,UAAU,OAAO,WAAY;AACrD,YAAI,OAAO,SAAS,MAAM,KAAK,MAC9B,aAAa,CAAC,GAGd,YAAY,EAAE,UAAU,MAAM;AAG/B,YAAK,UAAW,CAAE,GAAI;AACrB,eAAM,QAAQ,EAAE,YAAa;AAC5B,uBAAY,KAAK,YAAY,CAAE,IAAI,EAAE,WAAY,IAAK;AAAA,UACvD;AAAA,QACD;AAEA,kBAAU,UAAU,MAAM;AAG1B,eAAQ,SAAU;AAEjB,cAAK,EAAE,eAAgB,OAAQ,GAAI;AAClC,kBAAO,EAAE,eAAgB,OAAQ,CAAE,IAAI;AAAA,UACxC;AAGA,cAAK,CAAC,QAAQ,aAAa,EAAE,YAAa;AACzC,uBAAW,EAAE,WAAY,UAAU,EAAE,QAAS;AAAA,UAC/C;AAEA,iBAAO;AACP,oBAAU,UAAU,MAAM;AAE1B,cAAK,SAAU;AAGd,gBAAK,YAAY,KAAM;AAEtB,wBAAU;AAAA,YAGX,WAAY,SAAS,OAAO,SAAS,SAAU;AAG9C,qBAAO,WAAY,OAAO,MAAM,OAAQ,KAAK,WAAY,OAAO,OAAQ;AAGxE,kBAAK,CAAC,MAAO;AACZ,qBAAM,SAAS,YAAa;AAG3B,wBAAM,MAAM,MAAO,GAAI;AACvB,sBAAK,IAAK,CAAE,MAAM,SAAU;AAG3B,2BAAO,WAAY,OAAO,MAAM,IAAK,CAAE,CAAE,KACxC,WAAY,OAAO,IAAK,CAAE,CAAE;AAC7B,wBAAK,MAAO;AAGX,0BAAK,SAAS,MAAO;AACpB,+BAAO,WAAY,KAAM;AAAA,sBAG1B,WAAY,WAAY,KAAM,MAAM,MAAO;AAC1C,kCAAU,IAAK,CAAE;AACjB,kCAAU,QAAS,IAAK,CAAE,CAAE;AAAA,sBAC7B;AACA;AAAA,oBACD;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AAGA,kBAAK,SAAS,MAAO;AAGpB,oBAAK,QAAQ,EAAE,QAAS;AACvB,6BAAW,KAAM,QAAS;AAAA,gBAC3B,OAAO;AACN,sBAAI;AACH,+BAAW,KAAM,QAAS;AAAA,kBAC3B,SAAU,GAAI;AACb,2BAAO;AAAA,sBACN,OAAO;AAAA,sBACP,OAAO,OAAO,IAAI,wBAAwB,OAAO,SAAS;AAAA,oBAC3D;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,eAAO,EAAE,OAAO,WAAW,MAAM,SAAS;AAAA,MAC3C;AAEA,aAAO,OAAQ;AAAA;AAAA,QAGd,QAAQ;AAAA;AAAA,QAGR,cAAc,CAAC;AAAA,QACf,MAAM,CAAC;AAAA,QAEP,cAAc;AAAA,UACb,KAAK,SAAS;AAAA,UACd,MAAM;AAAA,UACN,SAAS,eAAe,KAAM,SAAS,QAAS;AAAA,UAChD,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,OAAO;AAAA,UACP,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAcb,SAAS;AAAA,YACR,KAAK;AAAA,YACL,MAAM;AAAA,YACN,MAAM;AAAA,YACN,KAAK;AAAA,YACL,MAAM;AAAA,UACP;AAAA,UAEA,UAAU;AAAA,YACT,KAAK;AAAA,YACL,MAAM;AAAA,YACN,MAAM;AAAA,UACP;AAAA,UAEA,gBAAgB;AAAA,YACf,KAAK;AAAA,YACL,MAAM;AAAA,YACN,MAAM;AAAA,UACP;AAAA;AAAA;AAAA,UAIA,YAAY;AAAA;AAAA,YAGX,UAAU;AAAA;AAAA,YAGV,aAAa;AAAA;AAAA,YAGb,aAAa,KAAK;AAAA;AAAA,YAGlB,YAAY,OAAO;AAAA,UACpB;AAAA;AAAA;AAAA;AAAA;AAAA,UAMA,aAAa;AAAA,YACZ,KAAK;AAAA,YACL,SAAS;AAAA,UACV;AAAA,QACD;AAAA;AAAA;AAAA;AAAA,QAKA,WAAW,SAAU,QAAQ,UAAW;AACvC,iBAAO;AAAA;AAAA,YAGN,WAAY,WAAY,QAAQ,OAAO,YAAa,GAAG,QAAS;AAAA;AAAA;AAAA,YAGhE,WAAY,OAAO,cAAc,MAAO;AAAA;AAAA,QAC1C;AAAA,QAEA,eAAe,4BAA6B,UAAW;AAAA,QACvD,eAAe,4BAA6B,UAAW;AAAA;AAAA,QAGvD,MAAM,SAAU,KAAK,SAAU;AAG9B,cAAK,OAAO,QAAQ,UAAW;AAC9B,sBAAU;AACV,kBAAM;AAAA,UACP;AAGA,oBAAU,WAAW,CAAC;AAEtB,cAAI,WAGH,UAGA,uBACA,iBAGA,cAGA,WAGAC,YAGA,aAGA,GAGA,UAGA,IAAI,OAAO,UAAW,CAAC,GAAG,OAAQ,GAGlC,kBAAkB,EAAE,WAAW,GAG/B,qBAAqB,EAAE,YACpB,gBAAgB,YAAY,gBAAgB,UAC9C,OAAQ,eAAgB,IACxB,OAAO,OAGR,WAAW,OAAO,SAAS,GAC3B,mBAAmB,OAAO,UAAW,aAAc,GAGnD,aAAa,EAAE,cAAc,CAAC,GAG9B,iBAAiB,CAAC,GAClB,sBAAsB,CAAC,GAGvB,WAAW,YAGX,QAAQ;AAAA,YACP,YAAY;AAAA;AAAA,YAGZ,mBAAmB,SAAU,KAAM;AAClC,kBAAI;AACJ,kBAAKA,YAAY;AAChB,oBAAK,CAAC,iBAAkB;AACvB,oCAAkB,CAAC;AACnB,yBAAU,QAAQ,SAAS,KAAM,qBAAsB,GAAM;AAC5D,oCAAiB,MAAO,CAAE,EAAE,YAAY,IAAI,GAAI,KAC7C,gBAAiB,MAAO,CAAE,EAAE,YAAY,IAAI,GAAI,KAAK,CAAC,GACtD,OAAQ,MAAO,CAAE,CAAE;AAAA,kBACvB;AAAA,gBACD;AACA,wBAAQ,gBAAiB,IAAI,YAAY,IAAI,GAAI;AAAA,cAClD;AACA,qBAAO,SAAS,OAAO,OAAO,MAAM,KAAM,IAAK;AAAA,YAChD;AAAA;AAAA,YAGA,uBAAuB,WAAW;AACjC,qBAAOA,aAAY,wBAAwB;AAAA,YAC5C;AAAA;AAAA,YAGA,kBAAkB,SAAU,MAAM,OAAQ;AACzC,kBAAKA,cAAa,MAAO;AACxB,uBAAO,oBAAqB,KAAK,YAAY,CAAE,IAC9C,oBAAqB,KAAK,YAAY,CAAE,KAAK;AAC9C,+BAAgB,IAAK,IAAI;AAAA,cAC1B;AACA,qBAAO;AAAA,YACR;AAAA;AAAA,YAGA,kBAAkB,SAAU,MAAO;AAClC,kBAAKA,cAAa,MAAO;AACxB,kBAAE,WAAW;AAAA,cACd;AACA,qBAAO;AAAA,YACR;AAAA;AAAA,YAGA,YAAY,SAAU,KAAM;AAC3B,kBAAI;AACJ,kBAAK,KAAM;AACV,oBAAKA,YAAY;AAGhB,wBAAM,OAAQ,IAAK,MAAM,MAAO,CAAE;AAAA,gBACnC,OAAO;AAGN,uBAAM,QAAQ,KAAM;AACnB,+BAAY,IAAK,IAAI,CAAE,WAAY,IAAK,GAAG,IAAK,IAAK,CAAE;AAAA,kBACxD;AAAA,gBACD;AAAA,cACD;AACA,qBAAO;AAAA,YACR;AAAA;AAAA,YAGA,OAAO,SAAU,YAAa;AAC7B,kBAAI,YAAY,cAAc;AAC9B,kBAAK,WAAY;AAChB,0BAAU,MAAO,SAAU;AAAA,cAC5B;AACA,mBAAM,GAAG,SAAU;AACnB,qBAAO;AAAA,YACR;AAAA,UACD;AAGD,mBAAS,QAAS,KAAM;AAKxB,YAAE,QAAU,OAAO,EAAE,OAAO,SAAS,QAAS,IAC5C,QAAS,WAAW,SAAS,WAAW,IAAK;AAG/C,YAAE,OAAO,QAAQ,UAAU,QAAQ,QAAQ,EAAE,UAAU,EAAE;AAGzD,YAAE,aAAc,EAAE,YAAY,KAAM,YAAY,EAAE,MAAO,aAAc,KAAK,CAAE,EAAG;AAGjF,cAAK,EAAE,eAAe,MAAO;AAC5B,wBAAY,SAAS,cAAe,GAAI;AAKxC,gBAAI;AACH,wBAAU,OAAO,EAAE;AAInB,wBAAU,OAAO,UAAU;AAC3B,gBAAE,cAAc,aAAa,WAAW,OAAO,aAAa,SAC3D,UAAU,WAAW,OAAO,UAAU;AAAA,YACxC,SAAU,GAAI;AAIb,gBAAE,cAAc;AAAA,YACjB;AAAA,UACD;AAGA,cAAK,EAAE,QAAQ,EAAE,eAAe,OAAO,EAAE,SAAS,UAAW;AAC5D,cAAE,OAAO,OAAO,MAAO,EAAE,MAAM,EAAE,WAAY;AAAA,UAC9C;AAGA,wCAA+B,YAAY,GAAG,SAAS,KAAM;AAG7D,cAAKA,YAAY;AAChB,mBAAO;AAAA,UACR;AAIA,wBAAc,OAAO,SAAS,EAAE;AAGhC,cAAK,eAAe,OAAO,aAAa,GAAI;AAC3C,mBAAO,MAAM,QAAS,WAAY;AAAA,UACnC;AAGA,YAAE,OAAO,EAAE,KAAK,YAAY;AAG5B,YAAE,aAAa,CAAC,WAAW,KAAM,EAAE,IAAK;AAKxC,qBAAW,EAAE,IAAI,QAAS,OAAO,EAAG;AAGpC,cAAK,CAAC,EAAE,YAAa;AAGpB,uBAAW,EAAE,IAAI,MAAO,SAAS,MAAO;AAGxC,gBAAK,EAAE,SAAU,EAAE,eAAe,OAAO,EAAE,SAAS,WAAa;AAChE,2BAAc,OAAO,KAAM,QAAS,IAAI,MAAM,OAAQ,EAAE;AAGxD,qBAAO,EAAE;AAAA,YACV;AAGA,gBAAK,EAAE,UAAU,OAAQ;AACxB,yBAAW,SAAS,QAAS,YAAY,IAAK;AAC9C,0BAAa,OAAO,KAAM,QAAS,IAAI,MAAM,OAAQ,OAAS,MAAM,SACnE;AAAA,YACF;AAGA,cAAE,MAAM,WAAW;AAAA,UAGpB,WAAY,EAAE,QAAQ,EAAE,gBACrB,EAAE,eAAe,IAAK,QAAS,mCAAoC,MAAM,GAAI;AAC/E,cAAE,OAAO,EAAE,KAAK,QAAS,KAAK,GAAI;AAAA,UACnC;AAGA,cAAK,EAAE,YAAa;AACnB,gBAAK,OAAO,aAAc,QAAS,GAAI;AACtC,oBAAM,iBAAkB,qBAAqB,OAAO,aAAc,QAAS,CAAE;AAAA,YAC9E;AACA,gBAAK,OAAO,KAAM,QAAS,GAAI;AAC9B,oBAAM,iBAAkB,iBAAiB,OAAO,KAAM,QAAS,CAAE;AAAA,YAClE;AAAA,UACD;AAGA,cAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,gBAAgB,SAAS,QAAQ,aAAc;AAC/E,kBAAM,iBAAkB,gBAAgB,EAAE,WAAY;AAAA,UACvD;AAGA,gBAAM;AAAA,YACL;AAAA,YACA,EAAE,UAAW,CAAE,KAAK,EAAE,QAAS,EAAE,UAAW,CAAE,CAAE,IAC/C,EAAE,QAAS,EAAE,UAAW,CAAE,CAAE,KACzB,EAAE,UAAW,CAAE,MAAM,MAAM,OAAO,WAAW,aAAa,MAC7D,EAAE,QAAS,GAAI;AAAA,UACjB;AAGA,eAAM,KAAK,EAAE,SAAU;AACtB,kBAAM,iBAAkB,GAAG,EAAE,QAAS,CAAE,CAAE;AAAA,UAC3C;AAGA,cAAK,EAAE,eACJ,EAAE,WAAW,KAAM,iBAAiB,OAAO,CAAE,MAAM,SAASA,aAAc;AAG5E,mBAAO,MAAM,MAAM;AAAA,UACpB;AAGA,qBAAW;AAGX,2BAAiB,IAAK,EAAE,QAAS;AACjC,gBAAM,KAAM,EAAE,OAAQ;AACtB,gBAAM,KAAM,EAAE,KAAM;AAGpB,sBAAY,8BAA+B,YAAY,GAAG,SAAS,KAAM;AAGzE,cAAK,CAAC,WAAY;AACjB,iBAAM,IAAI,cAAe;AAAA,UAC1B,OAAO;AACN,kBAAM,aAAa;AAGnB,gBAAK,aAAc;AAClB,iCAAmB,QAAS,YAAY,CAAE,OAAO,CAAE,CAAE;AAAA,YACtD;AAGA,gBAAKA,YAAY;AAChB,qBAAO;AAAA,YACR;AAGA,gBAAK,EAAE,SAAS,EAAE,UAAU,GAAI;AAC/B,6BAAetB,QAAO,WAAY,WAAW;AAC5C,sBAAM,MAAO,SAAU;AAAA,cACxB,GAAG,EAAE,OAAQ;AAAA,YACd;AAEA,gBAAI;AACH,cAAAsB,aAAY;AACZ,wBAAU,KAAM,gBAAgB,IAAK;AAAA,YACtC,SAAU,GAAI;AAGb,kBAAKA,YAAY;AAChB,sBAAM;AAAA,cACP;AAGA,mBAAM,IAAI,CAAE;AAAA,YACb;AAAA,UACD;AAGA,mBAAS,KAAM,QAAQ,kBAAkB,WAAW,SAAU;AAC7D,gBAAI,WAAW,SAAS,OAAO,UAAU,UACxC,aAAa;AAGd,gBAAKA,YAAY;AAChB;AAAA,YACD;AAEA,YAAAA,aAAY;AAGZ,gBAAK,cAAe;AACnB,cAAAtB,QAAO,aAAc,YAAa;AAAA,YACnC;AAIA,wBAAY;AAGZ,oCAAwB,WAAW;AAGnC,kBAAM,aAAa,SAAS,IAAI,IAAI;AAGpC,wBAAY,UAAU,OAAO,SAAS,OAAO,WAAW;AAGxD,gBAAK,WAAY;AAChB,yBAAW,oBAAqB,GAAG,OAAO,SAAU;AAAA,YACrD;AAGA,gBAAK,CAAC,aACL,OAAO,QAAS,UAAU,EAAE,SAAU,IAAI,MAC1C,OAAO,QAAS,QAAQ,EAAE,SAAU,IAAI,GAAI;AAC5C,gBAAE,WAAY,aAAc,IAAI,WAAW;AAAA,cAAC;AAAA,YAC7C;AAGA,uBAAW,YAAa,GAAG,UAAU,OAAO,SAAU;AAGtD,gBAAK,WAAY;AAGhB,kBAAK,EAAE,YAAa;AACnB,2BAAW,MAAM,kBAAmB,eAAgB;AACpD,oBAAK,UAAW;AACf,yBAAO,aAAc,QAAS,IAAI;AAAA,gBACnC;AACA,2BAAW,MAAM,kBAAmB,MAAO;AAC3C,oBAAK,UAAW;AACf,yBAAO,KAAM,QAAS,IAAI;AAAA,gBAC3B;AAAA,cACD;AAGA,kBAAK,WAAW,OAAO,EAAE,SAAS,QAAS;AAC1C,6BAAa;AAAA,cAGd,WAAY,WAAW,KAAM;AAC5B,6BAAa;AAAA,cAGd,OAAO;AACN,6BAAa,SAAS;AACtB,0BAAU,SAAS;AACnB,wBAAQ,SAAS;AACjB,4BAAY,CAAC;AAAA,cACd;AAAA,YACD,OAAO;AAGN,sBAAQ;AACR,kBAAK,UAAU,CAAC,YAAa;AAC5B,6BAAa;AACb,oBAAK,SAAS,GAAI;AACjB,2BAAS;AAAA,gBACV;AAAA,cACD;AAAA,YACD;AAGA,kBAAM,SAAS;AACf,kBAAM,cAAe,oBAAoB,cAAe;AAGxD,gBAAK,WAAY;AAChB,uBAAS,YAAa,iBAAiB,CAAE,SAAS,YAAY,KAAM,CAAE;AAAA,YACvE,OAAO;AACN,uBAAS,WAAY,iBAAiB,CAAE,OAAO,YAAY,KAAM,CAAE;AAAA,YACpE;AAGA,kBAAM,WAAY,UAAW;AAC7B,yBAAa;AAEb,gBAAK,aAAc;AAClB,iCAAmB;AAAA,gBAAS,YAAY,gBAAgB;AAAA,gBACvD,CAAE,OAAO,GAAG,YAAY,UAAU,KAAM;AAAA,cAAE;AAAA,YAC5C;AAGA,6BAAiB,SAAU,iBAAiB,CAAE,OAAO,UAAW,CAAE;AAElE,gBAAK,aAAc;AAClB,iCAAmB,QAAS,gBAAgB,CAAE,OAAO,CAAE,CAAE;AAGzD,kBAAK,CAAG,EAAE,OAAO,QAAW;AAC3B,uBAAO,MAAM,QAAS,UAAW;AAAA,cAClC;AAAA,YACD;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,QAEA,SAAS,SAAU,KAAK,MAAM,UAAW;AACxC,iBAAO,OAAO,IAAK,KAAK,MAAM,UAAU,MAAO;AAAA,QAChD;AAAA,QAEA,WAAW,SAAU,KAAK,UAAW;AACpC,iBAAO,OAAO,IAAK,KAAK,QAAW,UAAU,QAAS;AAAA,QACvD;AAAA,MACD,CAAE;AAEF,aAAO,KAAM,CAAE,OAAO,MAAO,GAAG,SAAU,IAAI,QAAS;AACtD,eAAQ,MAAO,IAAI,SAAU,KAAK,MAAM,UAAU,MAAO;AAGxD,cAAK,WAAY,IAAK,GAAI;AACzB,mBAAO,QAAQ;AACf,uBAAW;AACX,mBAAO;AAAA,UACR;AAGA,iBAAO,OAAO,KAAM,OAAO,OAAQ;AAAA,YAClC;AAAA,YACA,MAAM;AAAA,YACN,UAAU;AAAA,YACV;AAAA,YACA,SAAS;AAAA,UACV,GAAG,OAAO,cAAe,GAAI,KAAK,GAAI,CAAE;AAAA,QACzC;AAAA,MACD,CAAE;AAEF,aAAO,cAAe,SAAU,GAAI;AACnC,YAAI;AACJ,aAAM,KAAK,EAAE,SAAU;AACtB,cAAK,EAAE,YAAY,MAAM,gBAAiB;AACzC,cAAE,cAAc,EAAE,QAAS,CAAE,KAAK;AAAA,UACnC;AAAA,QACD;AAAA,MACD,CAAE;AAGF,aAAO,WAAW,SAAU,KAAK,SAAS,KAAM;AAC/C,eAAO,OAAO,KAAM;AAAA,UACnB;AAAA;AAAA,UAGA,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA;AAAA;AAAA;AAAA,UAKR,YAAY;AAAA,YACX,eAAe,WAAW;AAAA,YAAC;AAAA,UAC5B;AAAA,UACA,YAAY,SAAU,UAAW;AAChC,mBAAO,WAAY,UAAU,SAAS,GAAI;AAAA,UAC3C;AAAA,QACD,CAAE;AAAA,MACH;AAGA,aAAO,GAAG,OAAQ;AAAA,QACjB,SAAS,SAAU,MAAO;AACzB,cAAI;AAEJ,cAAK,KAAM,CAAE,GAAI;AAChB,gBAAK,WAAY,IAAK,GAAI;AACzB,qBAAO,KAAK,KAAM,KAAM,CAAE,CAAE;AAAA,YAC7B;AAGA,mBAAO,OAAQ,MAAM,KAAM,CAAE,EAAE,aAAc,EAAE,GAAI,CAAE,EAAE,MAAO,IAAK;AAEnE,gBAAK,KAAM,CAAE,EAAE,YAAa;AAC3B,mBAAK,aAAc,KAAM,CAAE,CAAE;AAAA,YAC9B;AAEA,iBAAK,IAAK,WAAW;AACpB,kBAAI,OAAO;AAEX,qBAAQ,KAAK,mBAAoB;AAChC,uBAAO,KAAK;AAAA,cACb;AAEA,qBAAO;AAAA,YACR,CAAE,EAAE,OAAQ,IAAK;AAAA,UAClB;AAEA,iBAAO;AAAA,QACR;AAAA,QAEA,WAAW,SAAU,MAAO;AAC3B,cAAK,WAAY,IAAK,GAAI;AACzB,mBAAO,KAAK,KAAM,SAAU,GAAI;AAC/B,qBAAQ,IAAK,EAAE,UAAW,KAAK,KAAM,MAAM,CAAE,CAAE;AAAA,YAChD,CAAE;AAAA,UACH;AAEA,iBAAO,KAAK,KAAM,WAAW;AAC5B,gBAAI,OAAO,OAAQ,IAAK,GACvB,WAAW,KAAK,SAAS;AAE1B,gBAAK,SAAS,QAAS;AACtB,uBAAS,QAAS,IAAK;AAAA,YAExB,OAAO;AACN,mBAAK,OAAQ,IAAK;AAAA,YACnB;AAAA,UACD,CAAE;AAAA,QACH;AAAA,QAEA,MAAM,SAAU,MAAO;AACtB,cAAI,iBAAiB,WAAY,IAAK;AAEtC,iBAAO,KAAK,KAAM,SAAU,GAAI;AAC/B,mBAAQ,IAAK,EAAE,QAAS,iBAAiB,KAAK,KAAM,MAAM,CAAE,IAAI,IAAK;AAAA,UACtE,CAAE;AAAA,QACH;AAAA,QAEA,QAAQ,SAAU,UAAW;AAC5B,eAAK,OAAQ,QAAS,EAAE,IAAK,MAAO,EAAE,KAAM,WAAW;AACtD,mBAAQ,IAAK,EAAE,YAAa,KAAK,UAAW;AAAA,UAC7C,CAAE;AACF,iBAAO;AAAA,QACR;AAAA,MACD,CAAE;AAGF,aAAO,KAAK,QAAQ,SAAS,SAAU,MAAO;AAC7C,eAAO,CAAC,OAAO,KAAK,QAAQ,QAAS,IAAK;AAAA,MAC3C;AACA,aAAO,KAAK,QAAQ,UAAU,SAAU,MAAO;AAC9C,eAAO,CAAC,EAAG,KAAK,eAAe,KAAK,gBAAgB,KAAK,eAAe,EAAE;AAAA,MAC3E;AAKA,aAAO,aAAa,MAAM,WAAW;AACpC,YAAI;AACH,iBAAO,IAAIA,QAAO,eAAe;AAAA,QAClC,SAAU,GAAI;AAAA,QAAC;AAAA,MAChB;AAEA,UAAI,mBAAmB;AAAA;AAAA,QAGrB,GAAG;AAAA;AAAA;AAAA,QAIH,MAAM;AAAA,MACP,GACA,eAAe,OAAO,aAAa,IAAI;AAExC,cAAQ,OAAO,CAAC,CAAC,gBAAkB,qBAAqB;AACxD,cAAQ,OAAO,eAAe,CAAC,CAAC;AAEhC,aAAO,cAAe,SAAU,SAAU;AACzC,YAAI,UAAU;AAGd,YAAK,QAAQ,QAAQ,gBAAgB,CAAC,QAAQ,aAAc;AAC3D,iBAAO;AAAA,YACN,MAAM,SAAU,SAAS,UAAW;AACnC,kBAAI,GACH,MAAM,QAAQ,IAAI;AAEnB,kBAAI;AAAA,gBACH,QAAQ;AAAA,gBACR,QAAQ;AAAA,gBACR,QAAQ;AAAA,gBACR,QAAQ;AAAA,gBACR,QAAQ;AAAA,cACT;AAGA,kBAAK,QAAQ,WAAY;AACxB,qBAAM,KAAK,QAAQ,WAAY;AAC9B,sBAAK,CAAE,IAAI,QAAQ,UAAW,CAAE;AAAA,gBACjC;AAAA,cACD;AAGA,kBAAK,QAAQ,YAAY,IAAI,kBAAmB;AAC/C,oBAAI,iBAAkB,QAAQ,QAAS;AAAA,cACxC;AAOA,kBAAK,CAAC,QAAQ,eAAe,CAAC,QAAS,kBAAmB,GAAI;AAC7D,wBAAS,kBAAmB,IAAI;AAAA,cACjC;AAGA,mBAAM,KAAK,SAAU;AACpB,oBAAI,iBAAkB,GAAG,QAAS,CAAE,CAAE;AAAA,cACvC;AAGA,yBAAW,SAAU,MAAO;AAC3B,uBAAO,WAAW;AACjB,sBAAK,UAAW;AACf,+BAAW,gBAAgB,IAAI,SAC9B,IAAI,UAAU,IAAI,UAAU,IAAI,YAC/B,IAAI,qBAAqB;AAE3B,wBAAK,SAAS,SAAU;AACvB,0BAAI,MAAM;AAAA,oBACX,WAAY,SAAS,SAAU;AAK9B,0BAAK,OAAO,IAAI,WAAW,UAAW;AACrC,iCAAU,GAAG,OAAQ;AAAA,sBACtB,OAAO;AACN;AAAA;AAAA,0BAGC,IAAI;AAAA,0BACJ,IAAI;AAAA,wBACL;AAAA,sBACD;AAAA,oBACD,OAAO;AACN;AAAA,wBACC,iBAAkB,IAAI,MAAO,KAAK,IAAI;AAAA,wBACtC,IAAI;AAAA;AAAA;AAAA;AAAA,yBAKF,IAAI,gBAAgB,YAAa,UACnC,OAAO,IAAI,iBAAiB,WAC3B,EAAE,QAAQ,IAAI,SAAS,IACvB,EAAE,MAAM,IAAI,aAAa;AAAA,wBAC1B,IAAI,sBAAsB;AAAA,sBAC3B;AAAA,oBACD;AAAA,kBACD;AAAA,gBACD;AAAA,cACD;AAGA,kBAAI,SAAS,SAAS;AACtB,8BAAgB,IAAI,UAAU,IAAI,YAAY,SAAU,OAAQ;AAKhE,kBAAK,IAAI,YAAY,QAAY;AAChC,oBAAI,UAAU;AAAA,cACf,OAAO;AACN,oBAAI,qBAAqB,WAAW;AAGnC,sBAAK,IAAI,eAAe,GAAI;AAM3B,oBAAAA,QAAO,WAAY,WAAW;AAC7B,0BAAK,UAAW;AACf,sCAAc;AAAA,sBACf;AAAA,oBACD,CAAE;AAAA,kBACH;AAAA,gBACD;AAAA,cACD;AAGA,yBAAW,SAAU,OAAQ;AAE7B,kBAAI;AAGH,oBAAI,KAAM,QAAQ,cAAc,QAAQ,QAAQ,IAAK;AAAA,cACtD,SAAU,GAAI;AAGb,oBAAK,UAAW;AACf,wBAAM;AAAA,gBACP;AAAA,cACD;AAAA,YACD;AAAA,YAEA,OAAO,WAAW;AACjB,kBAAK,UAAW;AACf,yBAAS;AAAA,cACV;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAE;AAMF,aAAO,cAAe,SAAU,GAAI;AACnC,YAAK,EAAE,aAAc;AACpB,YAAE,SAAS,SAAS;AAAA,QACrB;AAAA,MACD,CAAE;AAGF,aAAO,UAAW;AAAA,QACjB,SAAS;AAAA,UACR,QAAQ;AAAA,QAET;AAAA,QACA,UAAU;AAAA,UACT,QAAQ;AAAA,QACT;AAAA,QACA,YAAY;AAAA,UACX,eAAe,SAAU,MAAO;AAC/B,mBAAO,WAAY,IAAK;AACxB,mBAAO;AAAA,UACR;AAAA,QACD;AAAA,MACD,CAAE;AAGF,aAAO,cAAe,UAAU,SAAU,GAAI;AAC7C,YAAK,EAAE,UAAU,QAAY;AAC5B,YAAE,QAAQ;AAAA,QACX;AACA,YAAK,EAAE,aAAc;AACpB,YAAE,OAAO;AAAA,QACV;AAAA,MACD,CAAE;AAGF,aAAO,cAAe,UAAU,SAAU,GAAI;AAG7C,YAAK,EAAE,eAAe,EAAE,aAAc;AACrC,cAAI,QAAQ;AACZ,iBAAO;AAAA,YACN,MAAM,SAAU,GAAG,UAAW;AAC7B,uBAAS,OAAQ,UAAW,EAC1B,KAAM,EAAE,eAAe,CAAC,CAAE,EAC1B,KAAM,EAAE,SAAS,EAAE,eAAe,KAAK,EAAE,IAAI,CAAE,EAC/C,GAAI,cAAc,WAAW,SAAU,KAAM;AAC7C,uBAAO,OAAO;AACd,2BAAW;AACX,oBAAK,KAAM;AACV,2BAAU,IAAI,SAAS,UAAU,MAAM,KAAK,IAAI,IAAK;AAAA,gBACtD;AAAA,cACD,CAAE;AAGH,uBAAS,KAAK,YAAa,OAAQ,CAAE,CAAE;AAAA,YACxC;AAAA,YACA,OAAO,WAAW;AACjB,kBAAK,UAAW;AACf,yBAAS;AAAA,cACV;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAE;AAKF,UAAI,eAAe,CAAC,GACnB,SAAS;AAGV,aAAO,UAAW;AAAA,QACjB,OAAO;AAAA,QACP,eAAe,WAAW;AACzB,cAAI,WAAW,aAAa,IAAI,KAAO,OAAO,UAAU,MAAQ,MAAM;AACtE,eAAM,QAAS,IAAI;AACnB,iBAAO;AAAA,QACR;AAAA,MACD,CAAE;AAGF,aAAO,cAAe,cAAc,SAAU,GAAG,kBAAkB,OAAQ;AAE1E,YAAI,cAAc,aAAa,mBAC9B,WAAW,EAAE,UAAU,UAAW,OAAO,KAAM,EAAE,GAAI,IACpD,QACA,OAAO,EAAE,SAAS,aACf,EAAE,eAAe,IACjB,QAAS,mCAAoC,MAAM,KACrD,OAAO,KAAM,EAAE,IAAK,KAAK;AAI5B,YAAK,YAAY,EAAE,UAAW,CAAE,MAAM,SAAU;AAG/C,yBAAe,EAAE,gBAAgB,WAAY,EAAE,aAAc,IAC5D,EAAE,cAAc,IAChB,EAAE;AAGH,cAAK,UAAW;AACf,cAAG,QAAS,IAAI,EAAG,QAAS,EAAE,QAAS,QAAQ,OAAO,YAAa;AAAA,UACpE,WAAY,EAAE,UAAU,OAAQ;AAC/B,cAAE,QAAS,OAAO,KAAM,EAAE,GAAI,IAAI,MAAM,OAAQ,EAAE,QAAQ,MAAM;AAAA,UACjE;AAGA,YAAE,WAAY,aAAc,IAAI,WAAW;AAC1C,gBAAK,CAAC,mBAAoB;AACzB,qBAAO,MAAO,eAAe,iBAAkB;AAAA,YAChD;AACA,mBAAO,kBAAmB,CAAE;AAAA,UAC7B;AAGA,YAAE,UAAW,CAAE,IAAI;AAGnB,wBAAcA,QAAQ,YAAa;AACnC,UAAAA,QAAQ,YAAa,IAAI,WAAW;AACnC,gCAAoB;AAAA,UACrB;AAGA,gBAAM,OAAQ,WAAW;AAGxB,gBAAK,gBAAgB,QAAY;AAChC,qBAAQA,OAAO,EAAE,WAAY,YAAa;AAAA,YAG3C,OAAO;AACN,cAAAA,QAAQ,YAAa,IAAI;AAAA,YAC1B;AAGA,gBAAK,EAAG,YAAa,GAAI;AAGxB,gBAAE,gBAAgB,iBAAiB;AAGnC,2BAAa,KAAM,YAAa;AAAA,YACjC;AAGA,gBAAK,qBAAqB,WAAY,WAAY,GAAI;AACrD,0BAAa,kBAAmB,CAAE,CAAE;AAAA,YACrC;AAEA,gCAAoB,cAAc;AAAA,UACnC,CAAE;AAGF,iBAAO;AAAA,QACR;AAAA,MACD,CAAE;AAUF,cAAQ,qBAAuB,WAAW;AACzC,YAAI,OAAO,SAAS,eAAe,mBAAoB,EAAG,EAAE;AAC5D,aAAK,YAAY;AACjB,eAAO,KAAK,WAAW,WAAW;AAAA,MACnC,EAAI;AAOJ,aAAO,YAAY,SAAU,MAAM,SAAS,aAAc;AACzD,YAAK,OAAO,SAAS,UAAW;AAC/B,iBAAO,CAAC;AAAA,QACT;AACA,YAAK,OAAO,YAAY,WAAY;AACnC,wBAAc;AACd,oBAAU;AAAA,QACX;AAEA,YAAI,MAAM,QAAQ;AAElB,YAAK,CAAC,SAAU;AAIf,cAAK,QAAQ,oBAAqB;AACjC,sBAAU,SAAS,eAAe,mBAAoB,EAAG;AAKzD,mBAAO,QAAQ,cAAe,MAAO;AACrC,iBAAK,OAAO,SAAS,SAAS;AAC9B,oBAAQ,KAAK,YAAa,IAAK;AAAA,UAChC,OAAO;AACN,sBAAU;AAAA,UACX;AAAA,QACD;AAEA,iBAAS,WAAW,KAAM,IAAK;AAC/B,kBAAU,CAAC,eAAe,CAAC;AAG3B,YAAK,QAAS;AACb,iBAAO,CAAE,QAAQ,cAAe,OAAQ,CAAE,CAAE,CAAE;AAAA,QAC/C;AAEA,iBAAS,cAAe,CAAE,IAAK,GAAG,SAAS,OAAQ;AAEnD,YAAK,WAAW,QAAQ,QAAS;AAChC,iBAAQ,OAAQ,EAAE,OAAO;AAAA,QAC1B;AAEA,eAAO,OAAO,MAAO,CAAC,GAAG,OAAO,UAAW;AAAA,MAC5C;AAMA,aAAO,GAAG,OAAO,SAAU,KAAK,QAAQ,UAAW;AAClD,YAAI,UAAU,MAAM,UACnB,OAAO,MACP,MAAM,IAAI,QAAS,GAAI;AAExB,YAAK,MAAM,IAAK;AACf,qBAAW,iBAAkB,IAAI,MAAO,GAAI,CAAE;AAC9C,gBAAM,IAAI,MAAO,GAAG,GAAI;AAAA,QACzB;AAGA,YAAK,WAAY,MAAO,GAAI;AAG3B,qBAAW;AACX,mBAAS;AAAA,QAGV,WAAY,UAAU,OAAO,WAAW,UAAW;AAClD,iBAAO;AAAA,QACR;AAGA,YAAK,KAAK,SAAS,GAAI;AACtB,iBAAO,KAAM;AAAA,YACZ;AAAA;AAAA;AAAA;AAAA,YAKA,MAAM,QAAQ;AAAA,YACd,UAAU;AAAA,YACV,MAAM;AAAA,UACP,CAAE,EAAE,KAAM,SAAU,cAAe;AAGlC,uBAAW;AAEX,iBAAK,KAAM;AAAA;AAAA;AAAA,cAIV,OAAQ,OAAQ,EAAE,OAAQ,OAAO,UAAW,YAAa,CAAE,EAAE,KAAM,QAAS;AAAA;AAAA;AAAA,cAG5E;AAAA,aAAa;AAAA,UAKf,CAAE,EAAE,OAAQ,YAAY,SAAU,OAAO,QAAS;AACjD,iBAAK,KAAM,WAAW;AACrB,uBAAS,MAAO,MAAM,YAAY,CAAE,MAAM,cAAc,QAAQ,KAAM,CAAE;AAAA,YACzE,CAAE;AAAA,UACH,CAAE;AAAA,QACH;AAEA,eAAO;AAAA,MACR;AAKA,aAAO,KAAK,QAAQ,WAAW,SAAU,MAAO;AAC/C,eAAO,OAAO,KAAM,OAAO,QAAQ,SAAU,IAAK;AACjD,iBAAO,SAAS,GAAG;AAAA,QACpB,CAAE,EAAE;AAAA,MACL;AAKA,aAAO,SAAS;AAAA,QACf,WAAW,SAAU,MAAM,SAAS,GAAI;AACvC,cAAI,aAAa,SAAS,WAAW,QAAQ,WAAW,YAAY,mBACnE,WAAW,OAAO,IAAK,MAAM,UAAW,GACxC,UAAU,OAAQ,IAAK,GACvB,QAAQ,CAAC;AAGV,cAAK,aAAa,UAAW;AAC5B,iBAAK,MAAM,WAAW;AAAA,UACvB;AAEA,sBAAY,QAAQ,OAAO;AAC3B,sBAAY,OAAO,IAAK,MAAM,KAAM;AACpC,uBAAa,OAAO,IAAK,MAAM,MAAO;AACtC,+BAAsB,aAAa,cAAc,aAAa,aAC3D,YAAY,YAAa,QAAS,MAAO,IAAI;AAIhD,cAAK,mBAAoB;AACxB,0BAAc,QAAQ,SAAS;AAC/B,qBAAS,YAAY;AACrB,sBAAU,YAAY;AAAA,UAEvB,OAAO;AACN,qBAAS,WAAY,SAAU,KAAK;AACpC,sBAAU,WAAY,UAAW,KAAK;AAAA,UACvC;AAEA,cAAK,WAAY,OAAQ,GAAI;AAG5B,sBAAU,QAAQ,KAAM,MAAM,GAAG,OAAO,OAAQ,CAAC,GAAG,SAAU,CAAE;AAAA,UACjE;AAEA,cAAK,QAAQ,OAAO,MAAO;AAC1B,kBAAM,MAAQ,QAAQ,MAAM,UAAU,MAAQ;AAAA,UAC/C;AACA,cAAK,QAAQ,QAAQ,MAAO;AAC3B,kBAAM,OAAS,QAAQ,OAAO,UAAU,OAAS;AAAA,UAClD;AAEA,cAAK,WAAW,SAAU;AACzB,oBAAQ,MAAM,KAAM,MAAM,KAAM;AAAA,UAEjC,OAAO;AACN,oBAAQ,IAAK,KAAM;AAAA,UACpB;AAAA,QACD;AAAA,MACD;AAEA,aAAO,GAAG,OAAQ;AAAA;AAAA,QAGjB,QAAQ,SAAU,SAAU;AAG3B,cAAK,UAAU,QAAS;AACvB,mBAAO,YAAY,SAClB,OACA,KAAK,KAAM,SAAU,GAAI;AACxB,qBAAO,OAAO,UAAW,MAAM,SAAS,CAAE;AAAA,YAC3C,CAAE;AAAA,UACJ;AAEA,cAAI,MAAM,KACT,OAAO,KAAM,CAAE;AAEhB,cAAK,CAAC,MAAO;AACZ;AAAA,UACD;AAMA,cAAK,CAAC,KAAK,eAAe,EAAE,QAAS;AACpC,mBAAO,EAAE,KAAK,GAAG,MAAM,EAAE;AAAA,UAC1B;AAGA,iBAAO,KAAK,sBAAsB;AAClC,gBAAM,KAAK,cAAc;AACzB,iBAAO;AAAA,YACN,KAAK,KAAK,MAAM,IAAI;AAAA,YACpB,MAAM,KAAK,OAAO,IAAI;AAAA,UACvB;AAAA,QACD;AAAA;AAAA;AAAA,QAIA,UAAU,WAAW;AACpB,cAAK,CAAC,KAAM,CAAE,GAAI;AACjB;AAAA,UACD;AAEA,cAAI,cAAc,QAAQ,KACzB,OAAO,KAAM,CAAE,GACf,eAAe,EAAE,KAAK,GAAG,MAAM,EAAE;AAGlC,cAAK,OAAO,IAAK,MAAM,UAAW,MAAM,SAAU;AAGjD,qBAAS,KAAK,sBAAsB;AAAA,UAErC,OAAO;AACN,qBAAS,KAAK,OAAO;AAIrB,kBAAM,KAAK;AACX,2BAAe,KAAK,gBAAgB,IAAI;AACxC,mBAAQ,iBACL,iBAAiB,IAAI,QAAQ,iBAAiB,IAAI,oBACpD,OAAO,IAAK,cAAc,UAAW,MAAM,UAAW;AAEtD,6BAAe,aAAa;AAAA,YAC7B;AACA,gBAAK,gBAAgB,iBAAiB,QAAQ,aAAa,aAAa,GAAI;AAG3E,6BAAe,OAAQ,YAAa,EAAE,OAAO;AAC7C,2BAAa,OAAO,OAAO,IAAK,cAAc,kBAAkB,IAAK;AACrE,2BAAa,QAAQ,OAAO,IAAK,cAAc,mBAAmB,IAAK;AAAA,YACxE;AAAA,UACD;AAGA,iBAAO;AAAA,YACN,KAAK,OAAO,MAAM,aAAa,MAAM,OAAO,IAAK,MAAM,aAAa,IAAK;AAAA,YACzE,MAAM,OAAO,OAAO,aAAa,OAAO,OAAO,IAAK,MAAM,cAAc,IAAK;AAAA,UAC9E;AAAA,QACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAYA,cAAc,WAAW;AACxB,iBAAO,KAAK,IAAK,WAAW;AAC3B,gBAAI,eAAe,KAAK;AAExB,mBAAQ,gBAAgB,OAAO,IAAK,cAAc,UAAW,MAAM,UAAW;AAC7E,6BAAe,aAAa;AAAA,YAC7B;AAEA,mBAAO,gBAAgB;AAAA,UACxB,CAAE;AAAA,QACH;AAAA,MACD,CAAE;AAGF,aAAO,KAAM,EAAE,YAAY,eAAe,WAAW,cAAc,GAAG,SAAU,QAAQ,MAAO;AAC9F,YAAI,MAAM,kBAAkB;AAE5B,eAAO,GAAI,MAAO,IAAI,SAAU,KAAM;AACrC,iBAAO,OAAQ,MAAM,SAAU,MAAMuB,SAAQF,MAAM;AAGlD,gBAAI;AACJ,gBAAK,SAAU,IAAK,GAAI;AACvB,oBAAM;AAAA,YACP,WAAY,KAAK,aAAa,GAAI;AACjC,oBAAM,KAAK;AAAA,YACZ;AAEA,gBAAKA,SAAQ,QAAY;AACxB,qBAAO,MAAM,IAAK,IAAK,IAAI,KAAME,OAAO;AAAA,YACzC;AAEA,gBAAK,KAAM;AACV,kBAAI;AAAA,gBACH,CAAC,MAAMF,OAAM,IAAI;AAAA,gBACjB,MAAMA,OAAM,IAAI;AAAA,cACjB;AAAA,YAED,OAAO;AACN,mBAAME,OAAO,IAAIF;AAAA,YAClB;AAAA,UACD,GAAG,QAAQ,KAAK,UAAU,MAAO;AAAA,QAClC;AAAA,MACD,CAAE;AAQF,aAAO,KAAM,CAAE,OAAO,MAAO,GAAG,SAAU,IAAI,MAAO;AACpD,eAAO,SAAU,IAAK,IAAI;AAAA,UAAc,QAAQ;AAAA,UAC/C,SAAU,MAAM,UAAW;AAC1B,gBAAK,UAAW;AACf,yBAAW,OAAQ,MAAM,IAAK;AAG9B,qBAAO,UAAU,KAAM,QAAS,IAC/B,OAAQ,IAAK,EAAE,SAAS,EAAG,IAAK,IAAI,OACpC;AAAA,YACF;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAE;AAIF,aAAO,KAAM,EAAE,QAAQ,UAAU,OAAO,QAAQ,GAAG,SAAU,MAAM,MAAO;AACzE,eAAO,KAAM;AAAA,UACZ,SAAS,UAAU;AAAA,UACnB,SAAS;AAAA,UACT,IAAI,UAAU;AAAA,QACf,GAAG,SAAU,cAAc,UAAW;AAGrC,iBAAO,GAAI,QAAS,IAAI,SAAU,QAAQ,OAAQ;AACjD,gBAAI,YAAY,UAAU,WAAY,gBAAgB,OAAO,WAAW,YACvE,QAAQ,iBAAkB,WAAW,QAAQ,UAAU,OAAO,WAAW;AAE1E,mBAAO,OAAQ,MAAM,SAAU,MAAMG,OAAMV,QAAQ;AAClD,kBAAI;AAEJ,kBAAK,SAAU,IAAK,GAAI;AAGvB,uBAAO,SAAS,QAAS,OAAQ,MAAM,IACtC,KAAM,UAAU,IAAK,IACrB,KAAK,SAAS,gBAAiB,WAAW,IAAK;AAAA,cACjD;AAGA,kBAAK,KAAK,aAAa,GAAI;AAC1B,sBAAM,KAAK;AAIX,uBAAO,KAAK;AAAA,kBACX,KAAK,KAAM,WAAW,IAAK;AAAA,kBAAG,IAAK,WAAW,IAAK;AAAA,kBACnD,KAAK,KAAM,WAAW,IAAK;AAAA,kBAAG,IAAK,WAAW,IAAK;AAAA,kBACnD,IAAK,WAAW,IAAK;AAAA,gBACtB;AAAA,cACD;AAEA,qBAAOA,WAAU;AAAA;AAAA,gBAGhB,OAAO,IAAK,MAAMU,OAAM,KAAM;AAAA;AAAA;AAAA,gBAG9B,OAAO,MAAO,MAAMA,OAAMV,QAAO,KAAM;AAAA;AAAA,YACzC,GAAG,MAAM,YAAY,SAAS,QAAW,SAAU;AAAA,UACpD;AAAA,QACD,CAAE;AAAA,MACH,CAAE;AAGF,aAAO,KAAM;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD,GAAG,SAAU,IAAI,MAAO;AACvB,eAAO,GAAI,IAAK,IAAI,SAAU,IAAK;AAClC,iBAAO,KAAK,GAAI,MAAM,EAAG;AAAA,QAC1B;AAAA,MACD,CAAE;AAKF,aAAO,GAAG,OAAQ;AAAA,QAEjB,MAAM,SAAU,OAAO,MAAM,IAAK;AACjC,iBAAO,KAAK,GAAI,OAAO,MAAM,MAAM,EAAG;AAAA,QACvC;AAAA,QACA,QAAQ,SAAU,OAAO,IAAK;AAC7B,iBAAO,KAAK,IAAK,OAAO,MAAM,EAAG;AAAA,QAClC;AAAA,QAEA,UAAU,SAAU,UAAU,OAAO,MAAM,IAAK;AAC/C,iBAAO,KAAK,GAAI,OAAO,UAAU,MAAM,EAAG;AAAA,QAC3C;AAAA,QACA,YAAY,SAAU,UAAU,OAAO,IAAK;AAG3C,iBAAO,UAAU,WAAW,IAC3B,KAAK,IAAK,UAAU,IAAK,IACzB,KAAK,IAAK,OAAO,YAAY,MAAM,EAAG;AAAA,QACxC;AAAA,QAEA,OAAO,SAAU,QAAQ,OAAQ;AAChC,iBAAO,KACL,GAAI,cAAc,MAAO,EACzB,GAAI,cAAc,SAAS,MAAO;AAAA,QACrC;AAAA,MACD,CAAE;AAEF,aAAO;AAAA,QACJ,wLAE0D,MAAO,GAAI;AAAA,QACvE,SAAU,IAAI,MAAO;AAGpB,iBAAO,GAAI,IAAK,IAAI,SAAU,MAAM,IAAK;AACxC,mBAAO,UAAU,SAAS,IACzB,KAAK,GAAI,MAAM,MAAM,MAAM,EAAG,IAC9B,KAAK,QAAS,IAAK;AAAA,UACrB;AAAA,QACD;AAAA,MACD;AASA,UAAI,QAAQ;AAMZ,aAAO,QAAQ,SAAU,IAAI,SAAU;AACtC,YAAI,KAAK,MAAM;AAEf,YAAK,OAAO,YAAY,UAAW;AAClC,gBAAM,GAAI,OAAQ;AAClB,oBAAU;AACV,eAAK;AAAA,QACN;AAIA,YAAK,CAAC,WAAY,EAAG,GAAI;AACxB,iBAAO;AAAA,QACR;AAGA,eAAO,MAAM,KAAM,WAAW,CAAE;AAChC,gBAAQ,WAAW;AAClB,iBAAO,GAAG,MAAO,WAAW,MAAM,KAAK,OAAQ,MAAM,KAAM,SAAU,CAAE,CAAE;AAAA,QAC1E;AAGA,cAAM,OAAO,GAAG,OAAO,GAAG,QAAQ,OAAO;AAEzC,eAAO;AAAA,MACR;AAEA,aAAO,YAAY,SAAU,MAAO;AACnC,YAAK,MAAO;AACX,iBAAO;AAAA,QACR,OAAO;AACN,iBAAO,MAAO,IAAK;AAAA,QACpB;AAAA,MACD;AACA,aAAO,UAAU,MAAM;AACvB,aAAO,YAAY,KAAK;AACxB,aAAO,WAAW;AAClB,aAAO,aAAa;AACpB,aAAO,WAAW;AAClB,aAAO,YAAY;AACnB,aAAO,OAAO;AAEd,aAAO,MAAM,KAAK;AAElB,aAAO,YAAY,SAAU,KAAM;AAKlC,YAAI,OAAO,OAAO,KAAM,GAAI;AAC5B,gBAAS,SAAS,YAAY,SAAS;AAAA;AAAA;AAAA,QAKtC,CAAC,MAAO,MAAM,WAAY,GAAI,CAAE;AAAA,MAClC;AAEA,aAAO,OAAO,SAAU,MAAO;AAC9B,eAAO,QAAQ,OACd,MACE,OAAO,IAAK,QAAS,OAAO,IAAK;AAAA,MACrC;AAiBA,UAAK,OAAO,WAAW,cAAc,OAAO,KAAM;AACjD,eAAQ,UAAU,CAAC,GAAG,WAAW;AAChC,iBAAO;AAAA,QACR,CAAE;AAAA,MACH;AAKA,UAGC,UAAUd,QAAO,QAGjB,KAAKA,QAAO;AAEb,aAAO,aAAa,SAAU,MAAO;AACpC,YAAKA,QAAO,MAAM,QAAS;AAC1B,UAAAA,QAAO,IAAI;AAAA,QACZ;AAEA,YAAK,QAAQA,QAAO,WAAW,QAAS;AACvC,UAAAA,QAAO,SAAS;AAAA,QACjB;AAEA,eAAO;AAAA,MACR;AAKA,UAAK,OAAO,aAAa,aAAc;AACtC,QAAAA,QAAO,SAASA,QAAO,IAAI;AAAA,MAC5B;AAKA,aAAO;AAAA,IACP,CAAE;AAAA;AAAA;", "names": ["window", "isFunction", "isWindow", "arr", "push", "document", "documentElement", "rquickExpr", "i", "matches", "node", "dir", "find", "elem", "value", "deferred", "data", "nodeName", "name", "index", "length", "val", "completed", "method", "type"]}