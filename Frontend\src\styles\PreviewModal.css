/* Preview Modal Styles */
.preview-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: 20px;
  animation: overlayFadeIn 0.3s ease-out;
  backdrop-filter: blur(4px);
  /* Prevent scroll propagation to background */
  overscroll-behavior: contain;
  /* Ensure overlay captures all scroll events */
  touch-action: none;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.preview-modal {
  background: var(--white);
  border-radius: var(--border-radius-large);
  width: 95%;
  max-width: 1200px;
  height: 90vh;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--box-shadow-dark);
  animation: modalSlideIn 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.preview-modal--fullscreen {
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  border-radius: 0;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.preview-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--light-gray);
  background: var(--white);
  flex-shrink: 0;
}

.preview-modal__title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.preview-modal__title {
  margin: 0;
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--secondary-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-modal__filename {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-modal__controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.preview-modal__control-btn,
.preview-modal__close-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--dark-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  min-height: 36px;
}

.preview-modal__control-btn:hover,
.preview-modal__close-btn:hover {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.preview-modal__close-btn:hover {
  background-color: var(--error-color);
  color: var(--white);
}

/* Modal Content */
.preview-modal__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  /* Enable proper scroll containment */
  overscroll-behavior: contain;
  /* Allow touch actions within modal content */
  touch-action: auto;
}

/* Loading State */
.preview-modal__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--dark-gray);
}

.preview-modal__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.preview-modal__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--error-color);
  gap: 16px;
}

/* Video Container */
.preview-modal__video-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--black);
  overflow: hidden;
}

.preview-modal__video {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Document Container */
.preview-modal__document-container {
  flex: 1;
  overflow: auto;
  background: var(--bg-gray);
  /* Enable smooth scrolling */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  /* Contain scroll within this container */
  overscroll-behavior: contain;
}

.preview-modal__document-viewer {
  height: 100% !important;
  border: none !important;
  border-radius: 0 !important;
  /* Ensure document viewer allows scrolling */
  overflow: auto !important;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .preview-modal-overlay {
    padding: 10px;
  }

  .preview-modal {
    width: 100%;
    height: 95vh;
    max-height: 95vh;
  }

  .preview-modal--fullscreen {
    width: 100vw;
    height: 100vh;
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }

  .preview-modal__header {
    padding: 12px 16px;
  }

  .preview-modal__title {
    font-size: var(--heading6);
  }

  .preview-modal__filename {
    font-size: var(--extrasmallfont);
  }

  .preview-modal__control-btn,
  .preview-modal__close-btn {
    min-width: 32px;
    min-height: 32px;
    padding: 6px;
  }
}

@media (max-width: 480px) {
  .preview-modal-overlay {
    padding: 5px;
  }

  .preview-modal {
    width: 100%;
    height: 98vh;
    max-height: 98vh;
  }

  .preview-modal__header {
    padding: 8px 12px;
  }

  .preview-modal__title-section {
    gap: 2px;
  }

  .preview-modal__controls {
    gap: 4px;
  }

  .preview-modal__control-btn,
  .preview-modal__close-btn {
    min-width: 28px;
    min-height: 28px;
    padding: 4px;
  }
}

/* Touch and Scroll Enhancements */
.preview-modal__content {
  /* Enable smooth scrolling on mobile */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  /* Improve touch responsiveness */
  touch-action: auto;
}

.preview-modal__video {
  /* Improve video touch controls on mobile */
  touch-action: manipulation;
}

/* Prevent background scroll on all modal overlays */
body.modal-open {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
}

/* Enhanced scroll containment for nested scrollable areas */
.preview-modal__document-container *,
.preview-modal__video-container * {
  /* Ensure all child elements respect scroll containment */
  overscroll-behavior: contain;
}

/* Accessibility Enhancements */
.preview-modal__control-btn:focus,
.preview-modal__close-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .preview-modal-overlay {
    background-color: rgba(0, 0, 0, 0.95);
  }
  
  .preview-modal__header {
    border-bottom: 2px solid var(--secondary-color);
  }
}
