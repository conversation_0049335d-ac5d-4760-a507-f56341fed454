import { useEffect } from 'react';

/**
 * Custom hook to manage scroll locking when modals are open
 * Prevents background scrolling while allowing modal content to scroll
 * 
 * @param {boolean} isOpen - Whether the modal is currently open
 */
const useModalScrollLock = (isOpen) => {
  useEffect(() => {
    if (isOpen) {
      // Store current scroll position
      const scrollY = window.scrollY;
      
      // Apply scroll lock styles
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
      document.body.style.left = '0';
      
      // Add modal-open class for additional CSS targeting
      document.body.classList.add('modal-open');
      
      // Store scroll position for restoration
      document.body.setAttribute('data-scroll-y', scrollY.toString());
      
      // Cleanup function
      return () => {
        // Get stored scroll position
        const storedScrollY = document.body.getAttribute('data-scroll-y');
        
        // Remove scroll lock styles
        document.body.style.overflow = '';
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        document.body.style.left = '';
        
        // Remove modal-open class
        document.body.classList.remove('modal-open');
        
        // Remove stored scroll position
        document.body.removeAttribute('data-scroll-y');
        
        // Restore scroll position
        if (storedScrollY) {
          window.scrollTo(0, parseInt(storedScrollY, 10));
        }
      };
    }
  }, [isOpen]);
};

export default useModalScrollLock;
