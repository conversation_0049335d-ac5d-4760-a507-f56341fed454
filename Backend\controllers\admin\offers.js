const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const Offer = require('../../models/Offer');
const User = require('../../models/User');
const Content = require('../../models/Content');

// @desc    Get all offers with filtering, sorting, and pagination
// @route   GET /api/admin/offers
// @access  Private/Admin
exports.getAllOffers = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom = '',
      dateTo = '',
      minAmount = '',
      maxAmount = '',
      buyerId = '',
      sellerId = '',
      contentId = ''
    } = req.query;

    // Build query
    let query = {};

    // Search functionality
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      const users = await User.find({
        $or: [
          { firstName: searchRegex },
          { lastName: searchRegex },
          { email: searchRegex }
        ]
      }).select('_id');

      const userIds = users.map(user => user._id);

      query.$or = [
        { buyer: { $in: userIds } },
        { seller: { $in: userIds } },
        { message: searchRegex }
      ];
    }

    // Filter by status
    if (status) {
      query.status = status;
    }

    // Filter by buyer
    if (buyerId) {
      query.buyer = buyerId;
    }

    // Filter by seller
    if (sellerId) {
      query.seller = sellerId;
    }

    // Filter by content
    if (contentId) {
      query.content = contentId;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) {
        query.createdAt.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        query.createdAt.$lte = new Date(dateTo);
      }
    }

    // Amount range filter
    if (minAmount || maxAmount) {
      query.amount = {};
      if (minAmount) {
        query.amount.$gte = parseFloat(minAmount);
      }
      if (maxAmount) {
        query.amount.$lte = parseFloat(maxAmount);
      }
    }

    // Sorting
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const offers = await Offer.find(query)
      .populate('buyer', 'firstName lastName email profileImage')
      .populate('seller', 'firstName lastName email profileImage')
      .populate('content', 'title sport contentType price')
      .sort(sortOptions)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    // Get total count for pagination
    const total = await Offer.countDocuments(query);

    res.status(200).json({
      success: true,
      data: offers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get offer by ID
// @route   GET /api/admin/offers/:id
// @access  Private/Admin
exports.getOfferById = async (req, res, next) => {
  try {
    const offer = await Offer.findById(req.params.id)
      .populate('buyer', 'firstName lastName email profileImage mobile phone')
      .populate('seller', 'firstName lastName email profileImage mobile phone')
      .populate('content', 'title description sport contentType price files');

    if (!offer) {
      return next(new ErrorResponse('Offer not found', 404));
    }

    res.status(200).json({
      success: true,
      data: offer
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update offer status
// @route   PUT /api/admin/offers/:id/status
// @access  Private/Admin
exports.updateOfferStatus = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { status, notes } = req.body;

    const offer = await Offer.findById(req.params.id);
    if (!offer) {
      return next(new ErrorResponse('Offer not found', 404));
    }

    const oldStatus = offer.status;
    offer.status = status;

    // Add status change to offer history if notes provided
    if (notes) {
      if (!offer.statusHistory) {
        offer.statusHistory = [];
      }
      offer.statusHistory.push({
        status: status,
        notes: notes,
        changedBy: req.user.id,
        changedAt: new Date()
      });
    }

    await offer.save();

    res.status(200).json({
      success: true,
      data: offer,
      message: `Offer status updated from ${oldStatus} to ${status}`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get offer statistics
// @route   GET /api/admin/offers/stats
// @access  Private/Admin
exports.getOfferStats = async (req, res, next) => {
  try {
    const totalOffers = await Offer.countDocuments();
    const pendingOffers = await Offer.countDocuments({ status: 'Pending' });
    const acceptedOffers = await Offer.countDocuments({ status: 'Accepted' });
    const rejectedOffers = await Offer.countDocuments({ status: 'Rejected' });
    const cancelledOffers = await Offer.countDocuments({ status: 'Cancelled' });
    const expiredOffers = await Offer.countDocuments({ status: 'Expired' });

    // Offer value stats
    const valueStats = await Offer.aggregate([
      {
        $group: {
          _id: null,
          totalOfferValue: { $sum: '$amount' },
          averageOfferAmount: { $avg: '$amount' },
          highestOffer: { $max: '$amount' },
          lowestOffer: { $min: '$amount' }
        }
      }
    ]);

    // Acceptance rate
    const acceptanceRate = totalOffers > 0 ? (acceptedOffers / totalOffers) * 100 : 0;

    // Monthly stats for current year
    const currentYear = new Date().getFullYear();
    const monthlyStats = await Offer.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(`${currentYear}-01-01`),
            $lte: new Date(`${currentYear}-12-31`)
          }
        }
      },
      {
        $group: {
          _id: { $month: '$createdAt' },
          count: { $sum: 1 },
          totalValue: { $sum: '$amount' },
          accepted: {
            $sum: { $cond: [{ $eq: ['$status', 'Accepted'] }, 1, 0] }
          }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Top content by offers
    const topContentByOffers = await Offer.aggregate([
      {
        $group: {
          _id: '$content',
          offerCount: { $sum: 1 },
          averageOfferAmount: { $avg: '$amount' },
          totalOfferValue: { $sum: '$amount' }
        }
      },
      { $sort: { offerCount: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'contents',
          localField: '_id',
          foreignField: '_id',
          as: 'contentDetails'
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        overview: {
          total: totalOffers,
          pending: pendingOffers,
          accepted: acceptedOffers,
          rejected: rejectedOffers,
          cancelled: cancelledOffers,
          expired: expiredOffers,
          acceptanceRate: acceptanceRate.toFixed(2)
        },
        valueStats: valueStats[0] || {
          totalOfferValue: 0,
          averageOfferAmount: 0,
          highestOffer: 0,
          lowestOffer: 0
        },
        monthlyStats,
        topContentByOffers
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete offer
// @route   DELETE /api/admin/offers/:id
// @access  Private/Admin
exports.deleteOffer = async (req, res, next) => {
  try {
    const offer = await Offer.findById(req.params.id);
    if (!offer) {
      return next(new ErrorResponse('Offer not found', 404));
    }

    // Check if offer can be deleted (only pending/cancelled/expired offers)
    if (!['Pending', 'Cancelled', 'Expired'].includes(offer.status)) {
      return next(new ErrorResponse('Cannot delete offers that are accepted or rejected', 400));
    }

    await offer.deleteOne();

    res.status(200).json({
      success: true,
      message: 'Offer deleted successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk update offers
// @route   POST /api/admin/offers/bulk-update
// @access  Private/Admin
exports.bulkUpdateOffers = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { offerIds, status } = req.body;

    const result = await Offer.updateMany(
      { _id: { $in: offerIds } },
      {
        $set: {
          status,
          updatedAt: new Date()
        }
      }
    );

    res.status(200).json({
      success: true,
      message: `${result.modifiedCount} offers updated successfully`,
      data: result
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk delete offers
// @route   POST /api/admin/offers/bulk-delete
// @access  Private/Admin
exports.bulkDeleteOffers = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { offerIds } = req.body;

    const result = await Offer.deleteMany({ _id: { $in: offerIds } });

    res.status(200).json({
      success: true,
      message: `${result.deletedCount} offers deleted successfully`,
      data: result
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export offers data
// @route   GET /api/admin/offers/export
// @access  Private/Admin
exports.exportOffers = async (req, res, next) => {
  try {
    const offers = await Offer.find()
      .populate('buyer', 'firstName lastName email')
      .populate('seller', 'firstName lastName email')
      .populate('content', 'title sport contentType price');

    // Transform data for export
    const exportData = offers.map(offer => ({
      ID: offer._id,
      'Buyer Name': `${offer.buyer.firstName} ${offer.buyer.lastName}`,
      'Buyer Email': offer.buyer.email,
      'Seller Name': `${offer.seller.firstName} ${offer.seller.lastName}`,
      'Seller Email': offer.seller.email,
      'Content Title': offer.content.title,
      'Content Type': offer.content.contentType,
      'Sport': offer.content.sport,
      'Amount': offer.amount,
      'Status': offer.status,
      'Created At': offer.createdAt,
      'Updated At': offer.updatedAt
    }));

    res.status(200).json({
      success: true,
      data: exportData
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get offer analytics
// @route   GET /api/admin/offers/analytics
// @access  Private/Admin
exports.getOfferAnalytics = async (req, res, next) => {
  try {
    const { period = '30d', groupBy = 'day' } = req.query;

    // Calculate date range based on period
    const endDate = new Date();
    let startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Build group stage based on groupBy parameter
    let groupStage = {};
    switch (groupBy) {
      case 'day':
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'week':
        groupStage = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        };
        break;
      case 'month':
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      default:
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    const analytics = await Offer.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: groupStage,
          totalOffers: { $sum: 1 },
          totalValue: { $sum: '$amount' },
          averageValue: { $avg: '$amount' },
          accepted: {
            $sum: { $cond: [{ $eq: ['$status', 'Accepted'] }, 1, 0] }
          },
          rejected: {
            $sum: { $cond: [{ $eq: ['$status', 'Rejected'] }, 1, 0] }
          },
          cancelled: {
            $sum: { $cond: [{ $eq: ['$status', 'Cancelled'] }, 1, 0] }
          }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete single offer
// @route   DELETE /api/admin/offers/:id
// @access  Private/Admin
exports.deleteOffer = async (req, res, next) => {
  try {
    const offer = await Offer.findById(req.params.id);

    if (!offer) {
      return next(new ErrorResponse('Offer not found', 404));
    }

    await offer.remove();

    res.status(200).json({
      success: true,
      message: 'Offer deleted successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Flag an offer
// @route   PUT /api/admin/offers/:id/flag
// @access  Private/Admin
exports.flagOffer = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reason } = req.body;
    const offer = await Offer.findById(req.params.id);

    if (!offer) {
      return next(new ErrorResponse('Offer not found', 404));
    }

    offer.isFlagged = true;
    offer.flagReason = reason;
    offer.flaggedBy = req.user.id;
    offer.flaggedAt = new Date();

    await offer.save();

    res.status(200).json({
      success: true,
      message: 'Offer flagged successfully',
      data: offer
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Unflag an offer
// @route   PUT /api/admin/offers/:id/unflag
// @access  Private/Admin
exports.unflagOffer = async (req, res, next) => {
  try {
    const offer = await Offer.findById(req.params.id);

    if (!offer) {
      return next(new ErrorResponse('Offer not found', 404));
    }

    offer.isFlagged = false;
    offer.flagReason = undefined;
    offer.flaggedBy = undefined;
    offer.flaggedAt = undefined;

    await offer.save();

    res.status(200).json({
      success: true,
      message: 'Offer unflagged successfully',
      data: offer
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Moderate an offer
// @route   PUT /api/admin/offers/:id/moderate
// @access  Private/Admin
exports.moderateOffer = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { action, reason } = req.body;
    const offer = await Offer.findById(req.params.id);

    if (!offer) {
      return next(new ErrorResponse('Offer not found', 404));
    }

    switch (action) {
      case 'approve':
        offer.moderationStatus = 'approved';
        offer.moderatedBy = req.user.id;
        offer.moderatedAt = new Date();
        break;
      case 'reject':
        if (!reason) {
          return next(new ErrorResponse('Reason is required for rejection', 400));
        }
        offer.moderationStatus = 'rejected';
        offer.moderationReason = reason;
        offer.moderatedBy = req.user.id;
        offer.moderatedAt = new Date();
        break;
      case 'flag':
        if (!reason) {
          return next(new ErrorResponse('Reason is required for flagging', 400));
        }
        offer.isFlagged = true;
        offer.flagReason = reason;
        offer.flaggedBy = req.user.id;
        offer.flaggedAt = new Date();
        break;
      default:
        return next(new ErrorResponse('Invalid moderation action', 400));
    }

    await offer.save();

    res.status(200).json({
      success: true,
      message: `Offer ${action}ed successfully`,
      data: offer
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get offer timeline
// @route   GET /api/admin/offers/:id/timeline
// @access  Private/Admin
exports.getOfferTimeline = async (req, res, next) => {
  try {
    const offer = await Offer.findById(req.params.id)
      .populate('buyer', 'firstName lastName')
      .populate('seller', 'firstName lastName')
      .populate('moderatedBy', 'firstName lastName')
      .populate('flaggedBy', 'firstName lastName');

    if (!offer) {
      return next(new ErrorResponse('Offer not found', 404));
    }

    // Build timeline events
    const timeline = [
      {
        type: 'created',
        date: offer.createdAt,
        details: `Offer created by ${offer.buyer.firstName} ${offer.buyer.lastName}`
      }
    ];

    // Add status history events
    if (offer.statusHistory && offer.statusHistory.length > 0) {
      const statusEvents = offer.statusHistory.map(history => ({
        type: 'status_change',
        date: history.changedAt,
        details: `Status changed to ${history.status}${history.notes ? `: ${history.notes}` : ''}`
      }));
      timeline.push(...statusEvents);
    }

    // Add moderation events
    if (offer.moderatedAt) {
      timeline.push({
        type: 'moderation',
        date: offer.moderatedAt,
        details: `Offer ${offer.moderationStatus} by ${offer.moderatedBy.firstName} ${offer.moderatedBy.lastName}${offer.moderationReason ? `: ${offer.moderationReason}` : ''
          }`
      });
    }

    // Add flag events
    if (offer.flaggedAt) {
      timeline.push({
        type: 'flag',
        date: offer.flaggedAt,
        details: `Offer flagged by ${offer.flaggedBy.firstName} ${offer.flaggedBy.lastName}: ${offer.flagReason}`
      });
    }

    // Sort timeline by date
    timeline.sort((a, b) => b.date - a.date);

    res.status(200).json({
      success: true,
      data: timeline
    });
  } catch (err) {
    next(err);
  }
};
