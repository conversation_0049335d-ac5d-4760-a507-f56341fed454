import api from '../api';
import { adminServiceUtils } from './index';

// Get all offers with filtering and pagination
export const getAllOffers = async (params = {}) => {
  try {
    const response = await api.get('/admin/offers', { params });
    return adminServiceUtils.handleApiSuccess(response);
  } catch (error) {
    return adminServiceUtils.handleApiError(error);
  }
};

// Get specific offer by ID
export const getOfferById = async (id) => {
  try {
    const response = await api.get(`/admin/offers/${id}`);
    return adminServiceUtils.handleApiSuccess(response);
  } catch (error) {
    return adminServiceUtils.handleApiError(error);
  }
};

// Approve an offer
export const approveOffer = async (id, approvalNotes = '') => {
  try {
    const response = await api.put(`/admin/offers/${id}/approve`, { approvalNotes });
    return adminServiceUtils.handleApiSuccess(response);
  } catch (error) {
    return adminServiceUtils.handleApiError(error);
  }
};

// Reject an offer
export const rejectOffer = async (id, reason, rejectionNotes = '') => {
  try {
    const response = await api.put(`/admin/offers/${id}/reject`, { reason, rejectionNotes });
    return adminServiceUtils.handleApiSuccess(response);
  } catch (error) {
    return adminServiceUtils.handleApiError(error);
  }
};

// Delete an offer
export const deleteOffer = async (id) => {
  try {
    const response = await api.delete(`/admin/offers/${id}`);
    return adminServiceUtils.handleApiSuccess(response);
  } catch (error) {
    return adminServiceUtils.handleApiError(error);
  }
};

// Bulk approve offers
export const bulkApproveOffers = async (offerIds, approvalNotes = '') => {
  try {
    const response = await api.put('/admin/offers/bulk/approve', { offerIds, approvalNotes });
    return adminServiceUtils.handleApiSuccess(response);
  } catch (error) {
    return adminServiceUtils.handleApiError(error);
  }
};

// Bulk reject offers
export const bulkRejectOffers = async ({ offerIds, reason, rejectionNotes = '' }) => {
  try {
    const response = await api.put('/admin/offers/bulk/reject', { offerIds, reason, rejectionNotes });
    return adminServiceUtils.handleApiSuccess(response);
  } catch (error) {
    return adminServiceUtils.handleApiError(error);
  }
};

// Bulk delete offers
export const bulkDeleteOffers = async (offerIds) => {
  try {
    const response = await api.delete('/admin/offers/bulk', { data: { offerIds } });
    return adminServiceUtils.handleApiSuccess(response);
  } catch (error) {
    return adminServiceUtils.handleApiError(error);
  }
};

// Get offer statistics
export const getOfferStats = async () => {
  try {
    const response = await api.get('/admin/offers/stats');
    return adminServiceUtils.handleApiSuccess(response);
  } catch (error) {
    return adminServiceUtils.handleApiError(error);
  }
};

// Export offers data
export const exportOffers = async (params = {}) => {
  try {
    const response = await api.get('/admin/offers/export', { params });
    return adminServiceUtils.handleApiSuccess(response);
  } catch (error) {
    return adminServiceUtils.handleApiError(error);
  }
};

// Offer status actions
export const offerStatusActions = {
  approve: (id, notes) => approveOffer(id, notes),
  reject: (id, reason, notes) => rejectOffer(id, reason, notes),
  delete: (id) => deleteOffer(id)
};

export default {
  getAllOffers,
  getOfferById,
  approveOffer,
  rejectOffer,
  deleteOffer,
  bulkApproveOffers,
  bulkRejectOffers,
  bulkDeleteOffers,
  getOfferStats,
  exportOffers,
  offerStatusActions
}; 