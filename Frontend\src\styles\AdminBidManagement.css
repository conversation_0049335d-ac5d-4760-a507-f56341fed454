/* AdminBidManagement Component Styles */
.AdminBidManagement {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Header */
.AdminBidManagement__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--heading6);
  margin-bottom: var(--basefont);
}

.header-left {
  flex: 1;
  max-width: 400px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--basefont);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: var(--smallfont) var(--smallfont) var(--smallfont) 40px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.search-input::placeholder {
  color: var(--dark-gray);
}

.header-right {
  display: flex;
  gap: var(--smallfont);
}

.btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: #d63c2a;
}

.btn-success {
  background-color: #10b981;
  color: var(--white);
}

.btn-success:hover {
  background-color: #059669;
}

.btn-warning {
  background-color: #f59e0b;
  color: var(--white);
}

.btn-warning:hover {
  background-color: #d97706;
}

.btn-danger {
  background-color: #ef4444;
  color: var(--white);
}

.btn-danger:hover {
  background-color: #dc2626;
}

.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.btn-outline:hover {
  background-color: var(--bg-gray);
}

.btn-outline:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Filters */
.AdminBidManagement__filters {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  padding: var(--basefont);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  margin-bottom: var(--basefont);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.filter-icon {
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.filter-select {
  padding: 6px var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: var(--btn-color);
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin-left: auto;
}

.selected-count {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 600;
}

/* Table */
.AdminBidManagement__table {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.table-container {
  overflow-x: auto;
}

.bids-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
}

.bids-table th {
  background-color: var(--bg-gray);
  padding: var(--basefont);
  text-align: left;
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
  white-space: nowrap;
}

.bids-table td {
  padding: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

.bids-table tr:hover {
  background-color: var(--bg-gray);
}

/* Bid ID Column */
.bid-id {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.bid-id-text {
  font-weight: 600;
  color: var(--secondary-color);
}

.auto-bid-badge,
.highest-bid-badge {
  font-size: var(--extrasmallfont);
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
  width: fit-content;
}

.auto-bid-badge {
  background-color: #dbeafe;
  color: #1e40af;
}

.highest-bid-badge {
  background-color: #fef3c7;
  color: #92400e;
}

/* Content Info */
.content-info {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.content-thumbnail {
  width: 40px;
  height: 30px;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark-gray);
  font-size: var(--basefont);
  flex-shrink: 0;
}

.content-details {
  flex: 1;
  min-width: 0;
}

.content-title {
  display: block;
  font-weight: 500;
  color: var(--secondary-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

/* Bidder Info */
.bidder-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.bidder-name {
  font-weight: 500;
  color: var(--secondary-color);
}

.bidder-email {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Bid Amount */
.bid-amount {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.amount {
  font-weight: 600;
  color: var(--secondary-color);
}

.max-auto-bid {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Status Badges */
.status-badge {
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
  white-space: nowrap;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.won {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-badge.lost {
  background-color: #fef2f2;
  color: #991b1b;
}

.status-badge.outbid {
  background-color: #fef3c7;
  color: #92400e;
}

.status-badge.cancelled {
  background-color: #f3f4f6;
  color: #6b7280;
}

.status-badge.cancelled {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* Table Actions */
.table-actions {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.btn-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-action.view {
  background-color: var(--bg-blue);
  color: var(--btn-color);
}

.btn-action.view:hover {
  background-color: #dbeafe;
}

.btn-action.approve {
  background-color: #dcfce7;
  color: #166534;
}

.btn-action.approve:hover {
  background-color: #bbf7d0;
}

.btn-action.reject {
  background-color: #fef3c7;
  color: #92400e;
}

.btn-action.reject:hover {
  background-color: #fde68a;
}

.btn-action.edit {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.btn-action.edit:hover {
  background-color: var(--light-gray);
}

.btn-action.delete {
  background-color: #fef2f2;
  color: #ef4444;
}

.btn-action.delete:hover {
  background-color: #fee2e2;
}

/* No Results */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading3);
  text-align: center;
}

.no-results-icon {
  font-size: var(--heading2);
  color: var(--light-gray);
  margin-bottom: var(--basefont);
}

.no-results h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
}

.no-results p {
  margin: 0;
  color: var(--dark-gray);
}

/* Loading and Error States */
.loading-row,
.error-row {
  text-align: center;
  padding: 40px 20px;
}

.loading-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--basefont);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--light-gray);
  border-top: 3px solid var(--btn-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-row {
  color: #dc2626;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--basefont);
}

/* Pagination */
.AdminBidManagement__pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  margin-top: var(--basefont);
}

.pagination-info {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.pagination-controls {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.page-number {
  padding: 6px 12px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  color: var(--secondary-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-number.active {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Responsive styles */
@media (max-width: 768px) {
  .AdminBidManagement__header {
    flex-direction: column;
    align-items: stretch;
  }

  .header-left {
    max-width: none;
  }

  .AdminBidManagement__filters {
    flex-wrap: wrap;
  }

  .bulk-actions {
    margin-left: 0;
    margin-top: var(--smallfont);
    width: 100%;
  }

  .bids-table {
    font-size: var(--extrasmallfont);
  }

  .bids-table th,
  .bids-table td {
    padding: var(--smallfont);
  }

  .content-thumbnail {
    width: 32px;
    height: 24px;
    font-size: var(--smallfont);
  }

  .content-title {
    max-width: 120px;
  }

  .AdminBidManagement__pagination {
    flex-direction: column;
    gap: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .table-actions {
    flex-direction: column;
  }

  .btn-action {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }

  .bid-id-text {
    font-size: var(--extrasmallfont);
  }

  .auto-bid-badge,
  .highest-bid-badge {
    font-size: 8px;
    padding: 1px 4px;
  }
}
