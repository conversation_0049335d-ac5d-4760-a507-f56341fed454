import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectFinancialSettings,
  updateFinancialSettings,
} from "../../redux/slices/adminDashboardSlice";
import "../../styles/FinancialSettings.css";

// Icons
import { FaDollarSign, FaPercent, FaCreditCard, FaCalendarAlt, FaSave } from "react-icons/fa";

const FinancialSettings = () => {
  const dispatch = useDispatch();
  const financialSettings = useSelector(selectFinancialSettings);
  const [settings, setSettings] = useState(financialSettings);
  const [hasChanges, setHasChanges] = useState(false);

  const handleInputChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    dispatch(updateFinancialSettings(settings));
    setHasChanges(false);
    alert("Financial settings saved successfully!");
  };

  const handleReset = () => {
    setSettings(financialSettings);
    setHasChanges(false);
  };

  return (
    <div className="FinancialSettings">
      <div className="settings-section">
        <div className="section-header">
          <h3>Financial Settings</h3>
          <p>Configure payment processing, commissions, and payout settings</p>
        </div>

        <div className="settings-form">
          {/* Commission Structure */}
          <div className="settings-group">
            <h4>
              <FaPercent className="group-icon" />
              Commission Structure
            </h4>
            
            <div className="form-row">
              <div className="form-group">
                <label>Platform Commission (%)</label>
                <div className="input-with-icon">
                  <FaPercent className="input-icon" />
                  <input
                    type="number"
                    value={settings.platformCommission}
                    onChange={(e) => handleInputChange("platformCommission", parseFloat(e.target.value))}
                    className="form-input"
                    min="0"
                    max="100"
                    step="0.1"
                  />
                </div>
                <span className="form-help">Percentage taken by the platform from each sale</span>
              </div>

              <div className="form-group">
                <label>Seller Payout (%)</label>
                <div className="input-with-icon">
                  <FaPercent className="input-icon" />
                  <input
                    type="number"
                    value={settings.sellerPayout}
                    onChange={(e) => handleInputChange("sellerPayout", parseFloat(e.target.value))}
                    className="form-input"
                    min="0"
                    max="100"
                    step="0.1"
                    readOnly
                  />
                </div>
                <span className="form-help">Automatically calculated: {100 - settings.platformCommission}%</span>
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label>Minimum Payout Threshold ($)</label>
                <div className="input-with-icon">
                  <FaDollarSign className="input-icon" />
                  <input
                    type="number"
                    value={settings.minimumPayout}
                    onChange={(e) => handleInputChange("minimumPayout", parseFloat(e.target.value))}
                    className="form-input"
                    min="1"
                    step="1"
                  />
                </div>
                <span className="form-help">Minimum amount before payout is processed</span>
              </div>

              <div className="form-group">
                <label>Processing Fee (%)</label>
                <div className="input-with-icon">
                  <FaPercent className="input-icon" />
                  <input
                    type="number"
                    value={settings.processingFee}
                    onChange={(e) => handleInputChange("processingFee", parseFloat(e.target.value))}
                    className="form-input"
                    min="0"
                    max="10"
                    step="0.1"
                  />
                </div>
                <span className="form-help">Payment gateway processing fee</span>
              </div>
            </div>
          </div>

          {/* Payout Settings */}
          <div className="settings-group">
            <h4>
              <FaCalendarAlt className="group-icon" />
              Payout Schedule
            </h4>
            
            <div className="form-row">
              <div className="form-group">
                <label>Payout Frequency</label>
                <select
                  value={settings.payoutSchedule}
                  onChange={(e) => handleInputChange("payoutSchedule", e.target.value)}
                  className="form-select"
                >
                  <option value="weekly">Weekly (Every Friday)</option>
                  <option value="biweekly">Bi-weekly (Every 2 weeks)</option>
                  <option value="monthly">Monthly (1st of each month)</option>
                </select>
                <span className="form-help">How often sellers receive payouts</span>
              </div>

              <div className="form-group">
                <label>Tax Rate (%)</label>
                <div className="input-with-icon">
                  <FaPercent className="input-icon" />
                  <input
                    type="number"
                    value={settings.taxRate}
                    onChange={(e) => handleInputChange("taxRate", parseFloat(e.target.value))}
                    className="form-input"
                    min="0"
                    max="50"
                    step="0.1"
                  />
                </div>
                <span className="form-help">Default tax rate for transactions</span>
              </div>
            </div>
          </div>

          {/* Payment Gateway */}
          <div className="settings-group">
            <h4>
              <FaCreditCard className="group-icon" />
              Payment Gateway Configuration
            </h4>
            
            <div className="form-row">
              <div className="form-group">
                <label>Stripe Publishable Key</label>
                <input
                  type="text"
                  placeholder="pk_live_..."
                  className="form-input"
                />
                <span className="form-help">Your Stripe publishable key</span>
              </div>

              <div className="form-group">
                <label>Stripe Secret Key</label>
                <input
                  type="password"
                  placeholder="sk_live_..."
                  className="form-input"
                />
                <span className="form-help">Your Stripe secret key (encrypted)</span>
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label>PayPal Client ID</label>
                <input
                  type="text"
                  placeholder="Your PayPal Client ID"
                  className="form-input"
                />
                <span className="form-help">PayPal REST API Client ID</span>
              </div>

              <div className="form-group">
                <label>PayPal Client Secret</label>
                <input
                  type="password"
                  placeholder="Your PayPal Client Secret"
                  className="form-input"
                />
                <span className="form-help">PayPal REST API Client Secret</span>
              </div>
            </div>
          </div>

          {/* Content Pricing */}
          <div className="settings-group">
            <h4>
              <FaDollarSign className="group-icon" />
              Content Pricing Controls
            </h4>
            
            <div className="form-row">
              <div className="form-group">
                <label>Minimum Content Price ($)</label>
                <div className="input-with-icon">
                  <FaDollarSign className="input-icon" />
                  <input
                    type="number"
                    value={settings.minPrice}
                    onChange={(e) => handleInputChange("minPrice", parseFloat(e.target.value))}
                    className="form-input"
                    min="1"
                    step="1"
                  />
                </div>
                <span className="form-help">Minimum price sellers can set for content</span>
              </div>

              <div className="form-group">
                <label>Maximum Content Price ($)</label>
                <div className="input-with-icon">
                  <FaDollarSign className="input-icon" />
                  <input
                    type="number"
                    value={settings.maxPrice}
                    onChange={(e) => handleInputChange("maxPrice", parseFloat(e.target.value))}
                    className="form-input"
                    min="1"
                    step="1"
                  />
                </div>
                <span className="form-help">Maximum price sellers can set for content</span>
              </div>
            </div>
          </div>

          {/* Commission Preview */}
          <div className="commission-preview">
            <h4>Commission Breakdown Preview</h4>
            <div className="preview-example">
              <div className="example-sale">
                <span className="sale-label">Example Sale: $100.00</span>
                <div className="breakdown">
                  <div className="breakdown-item">
                    <span>Platform Commission ({settings.platformCommission}%)</span>
                    <span className="amount">-${(100 * settings.platformCommission / 100).toFixed(2)}</span>
                  </div>
                  <div className="breakdown-item">
                    <span>Processing Fee ({settings.processingFee}%)</span>
                    <span className="amount">-${(100 * settings.processingFee / 100).toFixed(2)}</span>
                  </div>
                  <div className="breakdown-item total">
                    <span>Seller Receives</span>
                    <span className="amount">${(100 - (100 * settings.platformCommission / 100) - (100 * settings.processingFee / 100)).toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Save Actions */}
          <div className="form-actions">
            <button 
              className="btn btn-primary"
              onClick={handleSave}
              disabled={!hasChanges}
            >
              <FaSave />
              Save Financial Settings
            </button>
            <button 
              className="btn btn-outline"
              onClick={handleReset}
              disabled={!hasChanges}
            >
              Reset Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialSettings;
