const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const User = require('../../models/User');
const Content = require('../../models/Content');
const Order = require('../../models/Order');
const Payment = require('../../models/Payment');
const Bid = require('../../models/Bid');

// @desc    Get revenue report
// @route   GET /api/admin/reports/revenue
// @access  Private/Admin
exports.getRevenueReport = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate = new Date(),
      groupBy = 'day'
    } = req.query;

    let groupFormat;
    switch (groupBy) {
      case 'day':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'week':
        groupFormat = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        };
        break;
      case 'month':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      case 'year':
        groupFormat = {
          year: { $year: '$createdAt' }
        };
        break;
      default:
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    const revenueData = await Payment.aggregate([
      {
        $match: {
          status: 'Completed',
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: groupFormat,
          totalRevenue: { $sum: '$amount' },
          platformFees: { $sum: '$platformFee' },
          sellerEarnings: { $sum: '$sellerEarnings' },
          transactionCount: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Calculate totals
    const totals = await Payment.aggregate([
      {
        $match: {
          status: 'Completed',
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          totalPlatformFees: { $sum: '$platformFee' },
          totalSellerEarnings: { $sum: '$sellerEarnings' },
          totalTransactions: { $sum: 1 }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        revenueData,
        totals: totals[0] || {
          totalRevenue: 0,
          totalPlatformFees: 0,
          totalSellerEarnings: 0,
          totalTransactions: 0
        },
        period: { startDate, endDate, groupBy }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get user report
// @route   GET /api/admin/reports/users
// @access  Private/Admin
exports.getUserReport = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      userType = 'all'
    } = req.query;

    let userQuery = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    if (userType !== 'all') {
      userQuery.role = userType;
    }

    // User registrations over time
    const userRegistrations = await User.aggregate([
      { $match: userQuery },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // User statistics by role
    const usersByRole = await User.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ]);

    // User statistics by status
    const usersByStatus = await User.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Active users (users who made purchases or sales)
    const activeUsers = await Order.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: null,
          uniqueBuyers: { $addToSet: '$buyer' },
          uniqueSellers: { $addToSet: '$seller' }
        }
      },
      {
        $project: {
          activeBuyers: { $size: '$uniqueBuyers' },
          activeSellers: { $size: '$uniqueSellers' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        userRegistrations,
        usersByRole,
        usersByStatus,
        activeUsers: activeUsers[0] || { activeBuyers: 0, activeSellers: 0 },
        period: { startDate, endDate, userType }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get content report
// @route   GET /api/admin/reports/content
// @access  Private/Admin
exports.getContentReport = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      contentType = '',
      sport = ''
    } = req.query;

    let contentQuery = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    if (contentType) contentQuery.contentType = contentType;
    if (sport) contentQuery.sport = sport;

    // Content uploads over time
    const contentUploads = await Content.aggregate([
      { $match: contentQuery },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Content by type
    const contentByType = await Content.aggregate([
      { $match: contentQuery },
      {
        $group: {
          _id: '$contentType',
          count: { $sum: 1 }
        }
      }
    ]);

    // Content by sport
    const contentBySport = await Content.aggregate([
      { $match: contentQuery },
      {
        $group: {
          _id: '$sport',
          count: { $sum: 1 }
        }
      }
    ]);

    // Content by status
    const contentByStatus = await Content.aggregate([
      { $match: contentQuery },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Top performing content
    const topContent = await Order.aggregate([
      {
        $lookup: {
          from: 'contents',
          localField: 'content',
          foreignField: '_id',
          as: 'contentInfo'
        }
      },
      { $unwind: '$contentInfo' },
      {
        $match: {
          'contentInfo.createdAt': {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$content',
          title: { $first: '$contentInfo.title' },
          sport: { $first: '$contentInfo.sport' },
          contentType: { $first: '$contentInfo.contentType' },
          sales: { $sum: 1 },
          revenue: { $sum: '$amount' }
        }
      },
      { $sort: { sales: -1 } },
      { $limit: 10 }
    ]);

    res.status(200).json({
      success: true,
      data: {
        contentUploads,
        contentByType,
        contentBySport,
        contentByStatus,
        topContent,
        period: { startDate, endDate, contentType, sport }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get sales report
// @route   GET /api/admin/reports/sales
// @access  Private/Admin
exports.getSalesReport = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      groupBy = 'day'
    } = req.query;

    let groupFormat;
    switch (groupBy) {
      case 'day':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'week':
        groupFormat = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        };
        break;
      case 'month':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      case 'year':
        groupFormat = {
          year: { $year: '$createdAt' }
        };
        break;
      default:
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    // Sales over time
    const salesData = await Order.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: groupFormat,
          totalSales: { $sum: 1 },
          totalRevenue: { $sum: '$amount' },
          averageOrderValue: { $avg: '$amount' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Sales by order type
    const salesByType = await Order.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$orderType',
          count: { $sum: 1 },
          revenue: { $sum: '$amount' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        salesData,
        salesByType,
        period: { startDate, endDate, groupBy }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get performance report
// @route   GET /api/admin/reports/performance
// @access  Private/Admin
exports.getPerformanceReport = async (req, res, next) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      metric = 'revenue'
    } = req.query;

    const dateRange = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };

    let performanceData = {};

    switch (metric) {
      case 'revenue':
        performanceData = await Payment.aggregate([
          {
            $match: {
              status: 'Completed',
              createdAt: dateRange
            }
          },
          {
            $group: {
              _id: null,
              totalRevenue: { $sum: '$amount' },
              averageTransaction: { $avg: '$amount' },
              transactionCount: { $sum: 1 }
            }
          }
        ]);
        break;

      case 'users':
        performanceData = await User.aggregate([
          {
            $match: { createdAt: dateRange }
          },
          {
            $group: {
              _id: null,
              totalUsers: { $sum: 1 },
              buyers: { $sum: { $cond: [{ $eq: ['$role', 'buyer'] }, 1, 0] } },
              sellers: { $sum: { $cond: [{ $eq: ['$role', 'seller'] }, 1, 0] } }
            }
          }
        ]);
        break;

      case 'content':
        performanceData = await Content.aggregate([
          {
            $match: { createdAt: dateRange }
          },
          {
            $group: {
              _id: null,
              totalContent: { $sum: 1 },
              published: { $sum: { $cond: [{ $eq: ['$status', 'Published'] }, 1, 0] } },
              pending: { $sum: { $cond: [{ $eq: ['$status', 'Under Review'] }, 1, 0] } }
            }
          }
        ]);
        break;

      case 'orders':
        performanceData = await Order.aggregate([
          {
            $match: { createdAt: dateRange }
          },
          {
            $group: {
              _id: null,
              totalOrders: { $sum: 1 },
              completed: { $sum: { $cond: [{ $eq: ['$status', 'Completed'] }, 1, 0] } },
              pending: { $sum: { $cond: [{ $eq: ['$status', 'Pending'] }, 1, 0] } }
            }
          }
        ]);
        break;

      default:
        return next(new ErrorResponse('Invalid metric specified', 400));
    }

    res.status(200).json({
      success: true,
      data: {
        metric,
        performance: performanceData[0] || {},
        period: { startDate, endDate }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get custom report
// @route   POST /api/admin/reports/custom
// @access  Private/Admin
exports.getCustomReport = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reportType, parameters } = req.body;

    let reportData = {};

    switch (reportType) {
      case 'user_activity':
        reportData = await getUserActivityReport(parameters);
        break;
      case 'content_performance':
        reportData = await getContentPerformanceReport(parameters);
        break;
      case 'revenue_breakdown':
        reportData = await getRevenueBreakdownReport(parameters);
        break;
      case 'seller_performance':
        reportData = await getSellerPerformanceReport(parameters);
        break;
      default:
        return next(new ErrorResponse('Invalid report type', 400));
    }

    res.status(200).json({
      success: true,
      data: {
        reportType,
        parameters,
        reportData
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export report
// @route   POST /api/admin/reports/export
// @access  Private/Admin
exports.exportReport = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reportType, format, parameters } = req.body;

    // Generate report data based on type
    let reportData;
    switch (reportType) {
      case 'revenue':
        reportData = await generateRevenueExport(parameters);
        break;
      case 'users':
        reportData = await generateUserExport(parameters);
        break;
      case 'content':
        reportData = await generateContentExport(parameters);
        break;
      case 'sales':
        reportData = await generateSalesExport(parameters);
        break;
      default:
        return next(new ErrorResponse('Invalid report type for export', 400));
    }

    if (format === 'json') {
      res.status(200).json({
        success: true,
        data: reportData
      });
    } else {
      // For CSV/Excel formats, implement file generation
      res.status(200).json({
        success: true,
        message: `${format.toUpperCase()} export functionality to be implemented`,
        data: reportData
      });
    }
  } catch (err) {
    next(err);
  }
};

// Helper function for user activity report
const getUserActivityReport = async (params) => {
  const { startDate, endDate, userRole } = params;

  let query = {
    createdAt: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  };

  if (userRole) query.role = userRole;

  return await User.aggregate([
    { $match: query },
    {
      $lookup: {
        from: 'orders',
        localField: '_id',
        foreignField: 'buyer',
        as: 'purchases'
      }
    },
    {
      $lookup: {
        from: 'orders',
        localField: '_id',
        foreignField: 'seller',
        as: 'sales'
      }
    },
    {
      $project: {
        firstName: 1,
        lastName: 1,
        email: 1,
        role: 1,
        createdAt: 1,
        purchaseCount: { $size: '$purchases' },
        salesCount: { $size: '$sales' }
      }
    },
    { $sort: { createdAt: -1 } }
  ]);
};

// Helper function for content performance report
const getContentPerformanceReport = async (params) => {
  const { startDate, endDate, sport, contentType } = params;

  let query = {
    createdAt: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  };

  if (sport) query.sport = sport;
  if (contentType) query.contentType = contentType;

  return await Content.aggregate([
    { $match: query },
    {
      $lookup: {
        from: 'orders',
        localField: '_id',
        foreignField: 'content',
        as: 'orders'
      }
    },
    {
      $project: {
        title: 1,
        sport: 1,
        contentType: 1,
        status: 1,
        createdAt: 1,
        salesCount: { $size: '$orders' },
        totalRevenue: { $sum: '$orders.amount' }
      }
    },
    { $sort: { salesCount: -1 } }
  ]);
};

// Helper function for revenue breakdown report
const getRevenueBreakdownReport = async (params) => {
  const { startDate, endDate } = params;

  return await Payment.aggregate([
    {
      $match: {
        status: 'Completed',
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $lookup: {
        from: 'orders',
        localField: 'order',
        foreignField: '_id',
        as: 'orderInfo'
      }
    },
    { $unwind: '$orderInfo' },
    {
      $lookup: {
        from: 'contents',
        localField: 'orderInfo.content',
        foreignField: '_id',
        as: 'contentInfo'
      }
    },
    { $unwind: '$contentInfo' },
    {
      $group: {
        _id: '$contentInfo.sport',
        totalRevenue: { $sum: '$amount' },
        platformFees: { $sum: '$platformFee' },
        sellerEarnings: { $sum: '$sellerEarnings' },
        transactionCount: { $sum: 1 }
      }
    },
    { $sort: { totalRevenue: -1 } }
  ]);
};

// Helper function for seller performance report
const getSellerPerformanceReport = async (params) => {
  const { startDate, endDate } = params;

  return await Payment.aggregate([
    {
      $match: {
        status: 'Completed',
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: '$seller',
        totalEarnings: { $sum: '$sellerEarnings' },
        totalSales: { $sum: 1 },
        averageSale: { $avg: '$amount' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'sellerInfo'
      }
    },
    { $unwind: '$sellerInfo' },
    {
      $project: {
        name: { $concat: ['$sellerInfo.firstName', ' ', '$sellerInfo.lastName'] },
        email: '$sellerInfo.email',
        totalEarnings: 1,
        totalSales: 1,
        averageSale: 1
      }
    },
    { $sort: { totalEarnings: -1 } }
  ]);
};

// Helper functions for export generation
const generateRevenueExport = async (params) => {
  const { startDate, endDate } = params;

  return await Payment.find({
    status: 'Completed',
    createdAt: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  })
    .populate('order')
    .populate('seller', 'firstName lastName email')
    .sort('-createdAt');
};

const generateUserExport = async (params) => {
  const { startDate, endDate, role } = params;

  let query = {
    createdAt: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  };

  if (role) query.role = role;

  return await User.find(query)
    .select('-password')
    .sort('-createdAt');
};

const generateContentExport = async (params) => {
  const { startDate, endDate, status, sport } = params;

  let query = {
    createdAt: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  };

  if (status) query.status = status;
  if (sport) query.sport = sport;

  return await Content.find(query)
    .populate('seller', 'firstName lastName email')
    .sort('-createdAt');
};

const generateSalesExport = async (params) => {
  const { startDate, endDate } = params;

  return await Order.find({
    createdAt: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  })
    .populate('buyer', 'firstName lastName email')
    .populate('seller', 'firstName lastName email')
    .populate('content', 'title sport contentType')
    .sort('-createdAt');
};

// @desc    Get report templates
// @route   GET /api/admin/reports/templates
// @access  Private/Admin
exports.getReportTemplates = async (req, res, next) => {
  try {
    // For now, return mock templates. In a real implementation,
    // these would be stored in a database
    const templates = [
      {
        id: 1,
        name: 'Monthly Revenue Report',
        reportType: 'revenue',
        parameters: {
          groupBy: 'month',
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          endDate: new Date()
        },
        createdAt: new Date(),
        createdBy: req.user.id
      },
      {
        id: 2,
        name: 'User Growth Report',
        reportType: 'users',
        parameters: {
          userType: 'all',
          startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
          endDate: new Date()
        },
        createdAt: new Date(),
        createdBy: req.user.id
      }
    ];

    res.status(200).json({
      success: true,
      data: templates
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Save report template
// @route   POST /api/admin/reports/templates
// @access  Private/Admin
exports.saveReportTemplate = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { name, reportType, parameters } = req.body;

    // In a real implementation, save to database
    const template = {
      id: Date.now(), // Mock ID
      name,
      reportType,
      parameters,
      createdAt: new Date(),
      createdBy: req.user.id
    };

    res.status(201).json({
      success: true,
      data: template
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete report template
// @route   DELETE /api/admin/reports/templates/:id
// @access  Private/Admin
exports.deleteReportTemplate = async (req, res, next) => {
  try {
    const { id } = req.params;

    // In a real implementation, delete from database
    // For now, just return success
    res.status(200).json({
      success: true,
      message: `Report template ${id} deleted successfully`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get scheduled reports
// @route   GET /api/admin/reports/scheduled
// @access  Private/Admin
exports.getScheduledReports = async (req, res, next) => {
  try {
    // For now, return mock scheduled reports
    const scheduledReports = [
      {
        id: 1,
        name: 'Weekly Revenue Summary',
        reportType: 'revenue',
        schedule: 'weekly',
        recipients: ['<EMAIL>'],
        parameters: {
          groupBy: 'week'
        },
        nextRun: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        isActive: true,
        createdAt: new Date(),
        createdBy: req.user.id
      }
    ];

    res.status(200).json({
      success: true,
      data: scheduledReports
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Schedule report
// @route   POST /api/admin/reports/schedule
// @access  Private/Admin
exports.scheduleReport = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { name, reportType, schedule, recipients, parameters } = req.body;

    // In a real implementation, save to database and set up cron job
    const scheduledReport = {
      id: Date.now(), // Mock ID
      name,
      reportType,
      schedule,
      recipients,
      parameters,
      nextRun: calculateNextRun(schedule),
      isActive: true,
      createdAt: new Date(),
      createdBy: req.user.id
    };

    res.status(201).json({
      success: true,
      data: scheduledReport
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Cancel scheduled report
// @route   DELETE /api/admin/reports/scheduled/:id
// @access  Private/Admin
exports.cancelScheduledReport = async (req, res, next) => {
  try {
    const { id } = req.params;

    // In a real implementation, remove from database and cancel cron job
    res.status(200).json({
      success: true,
      message: `Scheduled report ${id} cancelled successfully`
    });
  } catch (err) {
    next(err);
  }
};

// Helper function to calculate next run time
const calculateNextRun = (schedule) => {
  const now = new Date();

  switch (schedule) {
    case 'daily':
      return new Date(now.getTime() + 24 * 60 * 60 * 1000);
    case 'weekly':
      return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    case 'monthly':
      return new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
    default:
      return new Date(now.getTime() + 24 * 60 * 60 * 1000);
  }
};
