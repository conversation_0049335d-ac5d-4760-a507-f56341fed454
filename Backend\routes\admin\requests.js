const express = require('express');
const { check } = require('express-validator');
const {
  getAllRequests,
  getRequestById,
  updateRequestStatus,
  deleteRequest,
  bulkUpdateRequests,
  bulkDeleteRequests,
  getRequestStats,
  exportRequests,
  getRequestAnalytics,
  flagRequest,
  unflagRequest,
  moderateRequest,
  getRequestTimeline
} = require('../../controllers/admin/requests');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin request routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get all custom requests with filtering, sorting, and pagination
router.get('/', getAllRequests);

// Get request statistics
router.get('/stats', getRequestStats);

// Get request analytics
router.get('/analytics', [
  check('period', 'Period must be valid').optional().isIn(['7d', '30d', '90d', '1y', 'all']),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month']),
], getRequestAnalytics);

// Export requests data
router.get('/export', exportRequests);

// Bulk operations
router.post('/bulk-update', [
  check('requestIds', 'Request IDs array is required').isArray(),
  check('status', 'Status is required').isIn(['Pending', 'Accepted', 'Rejected', 'Completed', 'Cancelled']),
], bulkUpdateRequests);

router.post('/bulk-delete', [
  check('requestIds', 'Request IDs array is required').isArray(),
], bulkDeleteRequests);

// Individual request operations
router.get('/:id', getRequestById);

router.put('/:id/status', [
  check('status', 'Status is required').isIn(['Pending', 'Accepted', 'Rejected', 'Completed', 'Cancelled']),
  check('notes', 'Notes must be a string').optional().isString(),
], updateRequestStatus);

router.get('/:id/timeline', getRequestTimeline);

router.put('/:id/flag', [
  check('reason', 'Flag reason is required').not().isEmpty(),
], flagRequest);

router.put('/:id/unflag', unflagRequest);

router.put('/:id/moderate', [
  check('action', 'Moderation action is required').isIn(['approve', 'reject', 'flag']),
  check('reason', 'Reason is required for rejection or flagging').optional().not().isEmpty(),
], moderateRequest);

router.delete('/:id', deleteRequest);

module.exports = router;
