import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { selectAnalyticsWithFallback, selectStats, addActivity } from "../../redux/slices/adminDashboardSlice";
import AdminLayout from "../../components/admin/AdminLayout";
import "../../styles/AdminReports.css";

// Icons
import { FaChartLine, FaUsers, FaDollarSign, FaDownload, FaCalendarAlt } from "react-icons/fa";
import { MdTrendingUp, MdTrendingDown } from "react-icons/md";

const AdminReports = () => {
  const dispatch = useDispatch();
  const analytics = useSelector(selectAnalyticsWithFallback);
  const stats = useSelector(selectStats);
  const [selectedPeriod, setSelectedPeriod] = useState("6months");

  // Ensure analytics data is available with fallback
  const safeAnalytics = analytics || {
    salesChart: { labels: [], data: [] },
    userRegistrations: { labels: [], data: [] },
    categoryDistribution: { labels: [], data: [] },
    revenueByCategory: { labels: [], data: [] }
  };

  // Mock chart data - in real app, this would come from API
  const chartData = {
    salesChart: {
      labels: safeAnalytics.salesChart?.labels || [],
      datasets: [
        {
          label: 'Sales Revenue',
          data: safeAnalytics.salesChart?.data || [],
          borderColor: '#ee3425',
          backgroundColor: 'rgba(238, 52, 37, 0.1)',
          tension: 0.4,
        }
      ]
    },
    userRegistrations: {
      labels: safeAnalytics.userRegistrations?.labels || [],
      datasets: [
        {
          label: 'New Users',
          data: safeAnalytics.userRegistrations?.data || [],
          backgroundColor: '#3b82f6',
        }
      ]
    }
  };

  // Handle export functions
  const handleExportPDF = () => {
    // Simulate PDF export
    dispatch(addActivity({
      id: Date.now(),
      type: 'report_export',
      description: `Reports exported as PDF for period: ${selectedPeriod}`,
      timestamp: new Date().toISOString(),
      user: 'Admin',
    }));

    // Create a simple text content for demonstration
    const reportContent = `
XOSportsHub Analytics Report
Period: ${selectedPeriod}
Generated: ${new Date().toLocaleString()}

Key Metrics:
- Total Revenue: ${formatCurrency(stats.totalRevenue)}
- Total Users: ${stats.totalBuyers + stats.totalSellers}
- Total Content: ${stats.totalContent}
- Monthly Revenue: ${formatCurrency(stats.monthlyRevenue)}
- Monthly Orders: ${stats.monthlyOrders}

Revenue Trend: ${revenueChange.isPositive ? '+' : ''}${revenueChange.value}%
User Growth: ${usersChange.isPositive ? '+' : ''}${usersChange.value}%
Content Growth: ${contentChange.isPositive ? '+' : ''}${contentChange.value}%
    `;

    // Create and download a text file (in real app, this would be a PDF)
    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `xosportshub-report-${selectedPeriod}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    alert('PDF report has been generated and downloaded!');
  };

  const handleExportCSV = () => {
    // Simulate CSV export
    dispatch(addActivity({
      id: Date.now(),
      type: 'report_export',
      description: `Reports exported as CSV for period: ${selectedPeriod}`,
      timestamp: new Date().toISOString(),
      user: 'Admin',
    }));

    // Create CSV content
    const csvContent = `
Date,Revenue,Users,Content,Orders
${new Date().toISOString().split('T')[0]},${stats.totalRevenue},${stats.totalBuyers + stats.totalSellers},${stats.totalContent},${stats.monthlyOrders}
    `.trim();

    // Create and download CSV file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `xosportshub-data-${selectedPeriod}-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    alert('CSV data has been generated and downloaded!');
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Calculate percentage change (mock data)
  const getPercentageChange = (current, previous) => {
    const change = ((current - previous) / previous) * 100;
    return {
      value: Math.abs(change).toFixed(1),
      isPositive: change > 0
    };
  };

  // Mock previous period data
  const previousStats = {
    totalRevenue: 10200,
    totalUsers: 1200,
    totalContent: 420,
    monthlyRevenue: 2800
  };

  const revenueChange = getPercentageChange(stats.totalRevenue, previousStats.totalRevenue);
  const usersChange = getPercentageChange(stats.totalBuyers + stats.totalSellers, previousStats.totalUsers);
  const contentChange = getPercentageChange(stats.totalContent, previousStats.totalContent);
  const monthlyRevenueChange = getPercentageChange(stats.monthlyRevenue, previousStats.monthlyRevenue);

  return (
    <AdminLayout>
      <div className="AdminReports">
        {/* Header with Export Options */}
        <div className="AdminReports__header">
          <div className="header-left">
            <div className="period-selector">
              <FaCalendarAlt className="calendar-icon" />
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="period-select"
              >
                <option value="1month">Last Month</option>
                <option value="3months">Last 3 Months</option>
                <option value="6months">Last 6 Months</option>
                <option value="1year">Last Year</option>
              </select>
            </div>
          </div>

          <div className="header-right">
            <button
              className="btn btn-outline"
              onClick={handleExportPDF}
            >
              <FaDownload />
              Export PDF
            </button>
            <button
              className="btn btn-primary"
              onClick={handleExportCSV}
            >
              <FaDownload />
              Export CSV
            </button>
          </div>
        </div>

        {/* Key Metrics Cards */}
        <div className="AdminReports__metrics">
          <div className="metric-card revenue">
            <div className="metric-header">
              <div className="metric-icon">
                <FaDollarSign />
              </div>
              <div className={`metric-trend ${revenueChange.isPositive ? 'positive' : 'negative'}`}>
                {revenueChange.isPositive ? <MdTrendingUp /> : <MdTrendingDown />}
                {revenueChange.value}%
              </div>
            </div>
            <div className="metric-content">
              <div className="metric-number">{formatCurrency(stats.totalRevenue)}</div>
              <div className="metric-label">Total Revenue</div>
              <div className="metric-sublabel">vs previous period</div>
            </div>
          </div>

          <div className="metric-card users">
            <div className="metric-header">
              <div className="metric-icon">
                <FaUsers />
              </div>
              <div className={`metric-trend ${usersChange.isPositive ? 'positive' : 'negative'}`}>
                {usersChange.isPositive ? <MdTrendingUp /> : <MdTrendingDown />}
                {usersChange.value}%
              </div>
            </div>
            <div className="metric-content">
              <div className="metric-number">{(stats.totalBuyers + stats.totalSellers).toLocaleString()}</div>
              <div className="metric-label">Total Users</div>
              <div className="metric-sublabel">vs previous period</div>
            </div>
          </div>

          <div className="metric-card content">
            <div className="metric-header">
              <div className="metric-icon">
                <FaChartLine />
              </div>
              <div className={`metric-trend ${contentChange.isPositive ? 'positive' : 'negative'}`}>
                {contentChange.isPositive ? <MdTrendingUp /> : <MdTrendingDown />}
                {contentChange.value}%
              </div>
            </div>
            <div className="metric-content">
              <div className="metric-number">{stats.totalContent.toLocaleString()}</div>
              <div className="metric-label">Total Content</div>
              <div className="metric-sublabel">vs previous period</div>
            </div>
          </div>

          <div className="metric-card monthly">
            <div className="metric-header">
              <div className="metric-icon">
                <FaDollarSign />
              </div>
              <div className={`metric-trend ${monthlyRevenueChange.isPositive ? 'positive' : 'negative'}`}>
                {monthlyRevenueChange.isPositive ? <MdTrendingUp /> : <MdTrendingDown />}
                {monthlyRevenueChange.value}%
              </div>
            </div>
            <div className="metric-content">
              <div className="metric-number">{formatCurrency(stats.monthlyRevenue)}</div>
              <div className="metric-label">Monthly Revenue</div>
              <div className="metric-sublabel">vs previous month</div>
            </div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="AdminReports__charts">
          {/* Sales Over Time Chart */}
          <div className="chart-container">
            <div className="chart-header">
              <h3>Sales Over Time</h3>
              <p>Revenue trends for the selected period</p>
            </div>
            <div className="chart-placeholder">
              <div className="chart-mock">
                <div className="chart-bars">
                  {(safeAnalytics.salesChart?.data || []).map((value, index) => (
                    <div
                      key={index}
                      className="chart-bar"
                      style={{ height: `${(value / Math.max(...(safeAnalytics.salesChart?.data || [1]))) * 100}%` }}
                    >
                      <span className="bar-value">{formatCurrency(value)}</span>
                    </div>
                  ))}
                </div>
                <div className="chart-labels">
                  {(safeAnalytics.salesChart?.labels || []).map((label, index) => (
                    <span key={index} className="chart-label">{label}</span>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* User Registrations Chart */}
          <div className="chart-container">
            <div className="chart-header">
              <h3>User Registrations</h3>
              <p>Monthly user growth</p>
            </div>
            <div className="chart-placeholder">
              <div className="chart-mock">
                <div className="chart-bars">
                  {(safeAnalytics.userRegistrations?.data || []).map((value, index) => (
                    <div
                      key={index}
                      className="chart-bar users-bar"
                      style={{ height: `${(value / Math.max(...(safeAnalytics.userRegistrations?.data || [1]))) * 100}%` }}
                    >
                      <span className="bar-value">{value}</span>
                    </div>
                  ))}
                </div>
                <div className="chart-labels">
                  {(safeAnalytics.userRegistrations?.labels || []).map((label, index) => (
                    <span key={index} className="chart-label">{label}</span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Distribution Charts */}
        <div className="AdminReports__distributions">
          {/* Content Categories Distribution */}
          <div className="distribution-container">
            <div className="distribution-header">
              <h3>Content Categories Distribution</h3>
              <p>Breakdown by content type</p>
            </div>
            <div className="distribution-chart">
              {(safeAnalytics.categoryDistribution?.labels || []).map((label, index) => {
                const percentage = (safeAnalytics.categoryDistribution?.data || [])[index] || 0;
                return (
                  <div key={index} className="distribution-item">
                    <div className="distribution-bar">
                      <div
                        className="distribution-fill"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <div className="distribution-info">
                      <span className="distribution-label">{label}</span>
                      <span className="distribution-value">{percentage}%</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Revenue by Category */}
          <div className="distribution-container">
            <div className="distribution-header">
              <h3>Revenue by Category</h3>
              <p>Revenue breakdown by content type</p>
            </div>
            <div className="distribution-chart">
              {(safeAnalytics.revenueByCategory?.labels || []).map((label, index) => {
                const amount = (safeAnalytics.revenueByCategory?.data || [])[index] || 0;
                const maxAmount = Math.max(...(safeAnalytics.revenueByCategory?.data || [1]));
                const percentage = maxAmount > 0 ? (amount / maxAmount) * 100 : 0;
                return (
                  <div key={index} className="distribution-item">
                    <div className="distribution-bar">
                      <div
                        className="distribution-fill revenue-fill"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <div className="distribution-info">
                      <span className="distribution-label">{label}</span>
                      <span className="distribution-value">{formatCurrency(amount)}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminReports;
