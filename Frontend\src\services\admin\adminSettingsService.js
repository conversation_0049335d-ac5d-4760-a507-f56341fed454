import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/settings`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Get all settings
export const getAllSettings = async () => {
  try {
    const response = await api.get('/');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update settings
export const updateSettings = async (category, settings) => {
  try {
    const response = await api.put('/', { category, settings });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get financial settings
export const getFinancialSettings = async () => {
  try {
    const response = await api.get('/financial');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update financial settings
export const updateFinancialSettings = async (settings) => {
  try {
    const response = await api.put('/financial', settings);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get email settings
export const getEmailSettings = async () => {
  try {
    const response = await api.get('/email');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update email settings
export const updateEmailSettings = async (settings) => {
  try {
    const response = await api.put('/email', settings);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get security settings
export const getSecuritySettings = async () => {
  try {
    const response = await api.get('/security');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update security settings
export const updateSecuritySettings = async (settings) => {
  try {
    const response = await api.put('/security', settings);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get system settings
export const getSystemSettings = async () => {
  try {
    const response = await api.get('/system');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update system settings
export const updateSystemSettings = async (settings) => {
  try {
    const response = await api.put('/system', settings);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Reset settings to default
export const resetSettings = async (category) => {
  try {
    const response = await api.post('/reset', { category });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Export settings
export const exportSettings = async () => {
  try {
    const response = await api.get('/export');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Import settings
export const importSettings = async (settings) => {
  try {
    const response = await api.post('/import', { settings });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get settings history
export const getSettingsHistory = async (params = {}) => {
  try {
    const response = await api.get('/history', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Restore settings from history
export const restoreSettings = async (historyId) => {
  try {
    const response = await api.post(`/restore/${historyId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Settings validation helpers
export const validateSettings = {
  financial: (settings) => {
    const errors = [];

    if (settings.platformCommission < 0 || settings.platformCommission > 100) {
      errors.push('Platform commission must be between 0 and 100');
    }

    if (settings.sellerPayout < 0 || settings.sellerPayout > 100) {
      errors.push('Seller payout must be between 0 and 100');
    }

    if (settings.platformCommission + settings.sellerPayout !== 100) {
      errors.push('Platform commission and seller payout must sum to 100');
    }

    if (settings.minimumPayout < 0) {
      errors.push('Minimum payout must be positive');
    }

    return errors;
  },

  email: (settings) => {
    const errors = [];

    if (!settings.smtpHost) {
      errors.push('SMTP host is required');
    }

    if (!settings.smtpPort || settings.smtpPort < 1 || settings.smtpPort > 65535) {
      errors.push('Valid SMTP port is required');
    }

    if (!settings.fromEmail || !/\S+@\S+\.\S+/.test(settings.fromEmail)) {
      errors.push('Valid from email is required');
    }

    return errors;
  },

  security: (settings) => {
    const errors = [];

    if (settings.sessionTimeout < 300) {
      errors.push('Session timeout must be at least 5 minutes');
    }

    if (settings.maxLoginAttempts < 1) {
      errors.push('Max login attempts must be at least 1');
    }

    if (settings.lockoutDuration < 60) {
      errors.push('Lockout duration must be at least 1 minute');
    }

    return errors;
  },

  system: (settings) => {
    const errors = [];

    if (settings.maxFileSize < 1) {
      errors.push('Max file size must be at least 1 MB');
    }

    if (!settings.allowedFormats || settings.allowedFormats.length === 0) {
      errors.push('At least one file format must be allowed');
    }

    return errors;
  }
};

// Settings categories configuration
export const settingsCategories = {
  general: {
    title: 'General Settings',
    description: 'Basic platform configuration',
    icon: 'settings',
    fields: [
      { key: 'siteName', label: 'Site Name', type: 'text', required: true },
      { key: 'siteDescription', label: 'Site Description', type: 'textarea', required: true },
      { key: 'contactEmail', label: 'Contact Email', type: 'email', required: true },
      { key: 'supportEmail', label: 'Support Email', type: 'email', required: true },
      { key: 'timezone', label: 'Timezone', type: 'select', required: true },
      { key: 'language', label: 'Default Language', type: 'select', required: true },
      { key: 'currency', label: 'Default Currency', type: 'select', required: true }
    ]
  },
  financial: {
    title: 'Financial Settings',
    description: 'Revenue and payment configuration',
    icon: 'dollar-sign',
    fields: [
      { key: 'platformCommission', label: 'Platform Commission (%)', type: 'number', required: true },
      { key: 'sellerPayout', label: 'Seller Payout (%)', type: 'number', required: true },
      { key: 'minimumPayout', label: 'Minimum Payout Amount', type: 'number', required: true },
      { key: 'processingFee', label: 'Processing Fee (%)', type: 'number', required: true },
      { key: 'payoutSchedule', label: 'Payout Schedule', type: 'select', required: true },
      { key: 'taxRate', label: 'Tax Rate (%)', type: 'number', required: false },
      { key: 'refundPolicy', label: 'Refund Policy (days)', type: 'number', required: true }
    ]
  },
  email: {
    title: 'Email Settings',
    description: 'SMTP and email configuration',
    icon: 'mail',
    fields: [
      { key: 'smtpHost', label: 'SMTP Host', type: 'text', required: true },
      { key: 'smtpPort', label: 'SMTP Port', type: 'number', required: true },
      { key: 'smtpUser', label: 'SMTP Username', type: 'text', required: false },
      { key: 'smtpPassword', label: 'SMTP Password', type: 'password', required: false },
      { key: 'fromEmail', label: 'From Email', type: 'email', required: true },
      { key: 'fromName', label: 'From Name', type: 'text', required: true },
      { key: 'enableNotifications', label: 'Enable Notifications', type: 'boolean', required: false }
    ]
  },
  security: {
    title: 'Security Settings',
    description: 'Authentication and security configuration',
    icon: 'shield',
    fields: [
      { key: 'sessionTimeout', label: 'Session Timeout (seconds)', type: 'number', required: true },
      { key: 'passwordExpiry', label: 'Password Expiry (days)', type: 'number', required: true },
      { key: 'maxLoginAttempts', label: 'Max Login Attempts', type: 'number', required: true },
      { key: 'lockoutDuration', label: 'Lockout Duration (seconds)', type: 'number', required: true },
      { key: 'twoFactorAuth', label: 'Two-Factor Authentication', type: 'boolean', required: false },
      { key: 'ipWhitelist', label: 'IP Whitelist', type: 'array', required: false }
    ]
  },
  system: {
    title: 'System Settings',
    description: 'System and performance configuration',
    icon: 'server',
    fields: [
      { key: 'maxFileSize', label: 'Max File Size (MB)', type: 'number', required: true },
      { key: 'allowedFormats', label: 'Allowed File Formats', type: 'array', required: true },
      { key: 'maintenanceMode', label: 'Maintenance Mode', type: 'boolean', required: false },
      { key: 'debugMode', label: 'Debug Mode', type: 'boolean', required: false },
      { key: 'cacheEnabled', label: 'Cache Enabled', type: 'boolean', required: false },
      { key: 'backupFrequency', label: 'Backup Frequency', type: 'select', required: true }
    ]
  }
};

// Settings actions
export const settingsActions = {
  updateGeneral: (settings) => updateSettings('general', settings),
  updateFinancial: (settings) => updateFinancialSettings(settings),
  updateEmail: (settings) => updateEmailSettings(settings),
  updateSecurity: (settings) => updateSecuritySettings(settings),
  updateSystem: (settings) => updateSystemSettings(settings),
  resetAll: () => resetSettings('all'),
  resetCategory: (category) => resetSettings(category),
};

export default {
  getAllSettings,
  updateSettings,
  getFinancialSettings,
  updateFinancialSettings,
  getEmailSettings,
  updateEmailSettings,
  getSecuritySettings,
  updateSecuritySettings,
  getSystemSettings,
  updateSystemSettings,
  resetSettings,
  exportSettings,
  importSettings,
  getSettingsHistory,
  restoreSettings,
  validateSettings,
  settingsCategories,
  settingsActions
};
