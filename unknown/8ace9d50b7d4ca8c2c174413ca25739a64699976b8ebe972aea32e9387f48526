/* AdminCMSPages Component Styles */
.AdminCMSPages {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Header */
.AdminCMSPages__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--heading6);
  margin-bottom: var(--basefont);
}

.header-left {
  flex: 1;
  max-width: 400px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--basefont);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: var(--smallfont) var(--smallfont) var(--smallfont) 40px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.search-input::placeholder {
  color: var(--dark-gray);
}

.header-right {
  display: flex;
  gap: var(--smallfont);
}

.btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.btn.btn-primary:hover {
  background-color: #d32f2f;
}

.btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.btn.btn-outline:hover {
  background-color: var(--bg-gray);
}

.btn.btn-success {
  background-color: #10b981;
  color: var(--white);
}

.btn.btn-success:hover {
  background-color: #059669;
}

.btn.btn-danger {
  background-color: #ef4444;
  color: var(--white);
}

.btn.btn-danger:hover {
  background-color: #dc2626;
}

/* Filters */
.AdminCMSPages__filters {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  padding: var(--basefont);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  margin-bottom: var(--basefont);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.filter-select {
  padding: 6px var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: var(--btn-color);
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin-left: auto;
}

.selected-count {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 600;
}

/* Table */
.AdminCMSPages__table {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
  margin-bottom: var(--heading6);
}

.table-container {
  overflow-x: auto;
}

.pages-table {
  width: 100%;
  border-collapse: collapse;
}

.pages-table th,
.pages-table td {
  padding: var(--basefont);
  text-align: left;
  border-bottom: 1px solid var(--bg-gray);
}

.pages-table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.pages-table td {
  font-size: var(--smallfont);
  color: var(--text-color);
}

.pages-table tr:hover {
  background-color: var(--bg-gray);
}

.page-info {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.page-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  background-color: var(--bg-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--btn-color);
  font-size: var(--basefont);
}

.page-details {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-weight: 600;
  color: var(--secondary-color);
}

.page-slug {
  font-family: 'Courier New', monospace;
  background-color: var(--bg-gray);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.status-toggle {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: capitalize;
}

.status-badge.published {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.draft {
  background-color: #f3f4f6;
  color: #374151;
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: var(--heading6);
  transition: all 0.3s ease;
}

.toggle-on {
  color: #10b981;
}

.toggle-off {
  color: var(--light-gray);
}

.toggle-btn:hover .toggle-on {
  color: #059669;
}

.toggle-btn:hover .toggle-off {
  color: var(--dark-gray);
}

.table-actions {
  display: flex;
  gap: 4px;
}

.btn-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-action.view {
  background-color: var(--bg-blue);
  color: var(--btn-color);
}

.btn-action.view:hover {
  background-color: #dbeafe;
}

.btn-action.edit {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.btn-action.edit:hover {
  background-color: var(--light-gray);
}

.btn-action.delete {
  background-color: #fef2f2;
  color: #ef4444;
}

.btn-action.delete:hover {
  background-color: #fee2e2;
}

/* Quick Actions */
.AdminCMSPages__quick-actions {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  padding: var(--heading6);
  margin-bottom: var(--heading6);
}

.AdminCMSPages__quick-actions h3 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--basefont);
}

.quick-action-card {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.quick-action-card:hover {
  border-color: var(--btn-color);
  box-shadow: var(--box-shadow-light);
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--bg-blue);
  color: var(--btn-color);
  font-size: var(--heading6);
}

.action-content {
  flex: 1;
}

.action-content h4 {
  margin: 0 0 4px 0;
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.action-content p {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* No Results */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading3);
  text-align: center;
}

.no-results-icon {
  font-size: var(--heading2);
  color: var(--light-gray);
  margin-bottom: var(--basefont);
}

.no-results h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
}

.no-results p {
  margin: 0;
  color: var(--dark-gray);
  font-size: var(--smallfont);
}

/* Pagination */
.AdminCMSPages__pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
}

.pagination-info {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-number.active {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Responsive styles */
@media (max-width: 768px) {
  .AdminCMSPages__header {
    flex-direction: column;
    align-items: stretch;
  }

  .header-left {
    max-width: none;
  }

  .AdminCMSPages__filters {
    flex-wrap: wrap;
  }

  .bulk-actions {
    margin-left: 0;
    margin-top: var(--smallfont);
    width: 100%;
  }

  .pages-table {
    font-size: var(--extrasmallfont);
  }

  .pages-table th,
  .pages-table td {
    padding: var(--smallfont);
  }

  .page-icon {
    width: 32px;
    height: 32px;
    font-size: var(--smallfont);
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .AdminCMSPages__pagination {
    flex-direction: column;
    gap: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .table-actions {
    flex-direction: column;
  }

  .btn-action {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }

  .quick-action-card {
    flex-direction: column;
    text-align: center;
  }
}
