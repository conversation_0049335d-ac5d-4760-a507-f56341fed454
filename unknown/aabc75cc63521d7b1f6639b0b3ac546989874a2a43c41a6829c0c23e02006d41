/* AdminSettings Component Styles */
.AdminSettings {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Settings Navigation */
.AdminSettings__nav {
  display: flex;
  gap: 4px;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  padding: 4px;
  margin-bottom: var(--heading6);
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  background: none;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
}

.nav-tab:hover {
  background-color: var(--bg-gray);
  color: var(--btn-color);
}

.nav-tab.active {
  background-color: var(--btn-color);
  color: var(--white);
  font-weight: 600;
}

/* Settings Content */
.AdminSettings__content {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.settings-section {
  padding: var(--heading6);
}

.section-header {
  margin-bottom: var(--heading6);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.section-header h3 {
  margin: 0 0 4px 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
}

.section-header p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Form Styles */
.settings-form {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.form-group label {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.form-input,
.form-textarea {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  transition: all 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* Logo Upload */
.logo-upload {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.logo-preview {
  width: 120px;
  height: 80px;
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.logo-preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.logo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: var(--dark-gray);
  font-size: var(--extrasmallfont);
}

.logo-placeholder svg {
  font-size: var(--heading6);
}

/* Notification Groups */
.notification-group,
.security-group {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.notification-group h4,
.security-group h4 {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--bg-gray);
}

/* Toggle Settings */
.toggle-setting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.toggle-setting:hover {
  border-color: var(--btn-color);
  box-shadow: var(--box-shadow-light);
}

.toggle-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.toggle-label {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.toggle-description {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--light-gray);
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: var(--white);
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--btn-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Roles Grid */
.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--basefont);
}

.role-card {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--basefont);
  transition: all 0.3s ease;
}

.role-card:hover {
  border-color: var(--btn-color);
  box-shadow: var(--box-shadow-light);
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
}

.role-header h4 {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.role-count {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  background-color: var(--bg-gray);
  padding: 2px 8px;
  border-radius: 12px;
}

.role-permissions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: var(--basefont);
}

.permission-tag {
  font-size: var(--extrasmallfont);
  background-color: var(--bg-blue);
  color: var(--btn-color);
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

/* Buttons */
.btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.btn.btn-primary:hover {
  background-color: #d32f2f;
}

.btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.btn.btn-outline:hover {
  background-color: var(--bg-gray);
}

/* Settings Actions */
.settings-actions {
  padding: var(--basefont) var(--heading6);
  border-top: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
  display: flex;
  justify-content: flex-end;
}

/* Responsive styles */
@media (max-width: 768px) {
  .AdminSettings__nav {
    flex-direction: column;
  }

  .nav-tab {
    justify-content: flex-start;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .logo-upload {
    flex-direction: column;
    align-items: flex-start;
  }

  .toggle-setting {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .roles-grid {
    grid-template-columns: 1fr;
  }

  .settings-actions {
    padding: var(--basefont);
  }
}

@media (max-width: 480px) {
  .AdminSettings__nav {
    padding: 2px;
  }

  .nav-tab {
    padding: var(--smallfont);
    font-size: var(--extrasmallfont);
  }

  .nav-tab svg {
    display: none;
  }

  .settings-section {
    padding: var(--basefont);
  }

  .role-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
