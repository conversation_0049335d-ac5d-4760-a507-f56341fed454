import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { selectProfile } from "../../redux/slices/adminDashboardSlice";
import { logout } from "../../redux/slices/authSlice";
import "../../styles/AdminNavbar.css";

// Logo
import XOSportsLogo from "../../assets/images/XOsports-hub-logo.png";

// Icons
import { IoMdNotifications } from "react-icons/io";
import { FaUser, FaCog, FaBars } from "react-icons/fa";
import { IoLogOut } from "react-icons/io5";

const AdminNavbar = ({ onToggleSidebar }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const profile = useSelector(selectProfile);
  
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);

  // Mock notification count
  const notificationCount = 5;

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate("/");
  };

  // Handle profile navigation
  const handleProfileClick = () => {
    navigate("/admin/profile");
    setShowProfileDropdown(false);
  };

  // Handle settings navigation
  const handleSettingsClick = () => {
    navigate("/admin/settings");
    setShowProfileDropdown(false);
  };

  return (
    <div className="AdminNavbar">
      <div className="AdminNavbar__container">
        {/* Left Section - Sidebar Toggle & Logo */}
        <div className="AdminNavbar__left">
          <button
            className="AdminNavbar__toggle"
            onClick={onToggleSidebar}
            aria-label="Toggle Sidebar"
          >
            <FaBars />
          </button>

          <div className="AdminNavbar__logo">
            <img src={XOSportsLogo} alt="XO Sports Hub Logo" />
          </div>
        </div>

        {/* Right Section - Notifications & Profile */}
        <div className="AdminNavbar__right">
          {/* Notifications */}
          <div className="AdminNavbar__notifications">
            <button
              className="notification-btn"
              onClick={() => setShowNotifications(!showNotifications)}
              aria-label="Notifications"
            >
              <IoMdNotifications />
              {notificationCount > 0 && (
                <span className="notification-badge">{notificationCount}</span>
              )}
            </button>

            {showNotifications && (
              <div className="notifications-dropdown">
                <div className="notifications-header">
                  <h4>Notifications</h4>
                  <span className="notification-count">{notificationCount} new</span>
                </div>
                <div className="notifications-list">
                  <div className="notification-item">
                    <div className="notification-content">
                      <p>New content awaiting approval</p>
                      <span className="notification-time">2 minutes ago</span>
                    </div>
                  </div>
                  <div className="notification-item">
                    <div className="notification-content">
                      <p>New user registration</p>
                      <span className="notification-time">5 minutes ago</span>
                    </div>
                  </div>
                  <div className="notification-item">
                    <div className="notification-content">
                      <p>Payment received</p>
                      <span className="notification-time">10 minutes ago</span>
                    </div>
                  </div>
                </div>
                <div className="notifications-footer">
                  <button className="view-all-btn">View All Notifications</button>
                </div>
              </div>
            )}
          </div>

          {/* Admin Profile */}
          <div className="AdminNavbar__profile">
            <button
              className="profile-btn"
              onClick={() => setShowProfileDropdown(!showProfileDropdown)}
              aria-label="Admin Profile"
            >
              <div className="profile-avatar">
                {profile.profileImage ? (
                  <img src={profile.profileImage} alt="Admin" />
                ) : (
                  <FaUser />
                )}
              </div>
              <div className="profile-info">
                <span className="profile-name">
                  {profile.firstName} {profile.lastName}
                </span>
                <span className="profile-role">Administrator</span>
              </div>
            </button>

            {showProfileDropdown && (
              <div className="profile-dropdown">
                <div className="profile-dropdown-header">
                  <div className="profile-avatar large">
                    {profile.profileImage ? (
                      <img src={profile.profileImage} alt="Admin" />
                    ) : (
                      <FaUser />
                    )}
                  </div>
                  <div className="profile-details">
                    <h4>{profile.firstName} {profile.lastName}</h4>
                    <p>{profile.email}</p>
                  </div>
                </div>
                <div className="profile-dropdown-menu">
                  <button
                    className="dropdown-item"
                    onClick={handleProfileClick}
                  >
                    <FaUser className="dropdown-icon" />
                    View Profile
                  </button>
                  <button
                    className="dropdown-item"
                    onClick={handleSettingsClick}
                  >
                    <FaCog className="dropdown-icon" />
                    Settings
                  </button>
                  <hr className="dropdown-divider" />
                  <button
                    className="dropdown-item logout"
                    onClick={handleLogout}
                  >
                    <IoLogOut className="dropdown-icon" />
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Overlay for closing dropdowns */}
      {(showProfileDropdown || showNotifications) && (
        <div
          className="AdminNavbar__overlay"
          onClick={() => {
            setShowProfileDropdown(false);
            setShowNotifications(false);
          }}
        />
      )}
    </div>
  );
};

export default AdminNavbar;
