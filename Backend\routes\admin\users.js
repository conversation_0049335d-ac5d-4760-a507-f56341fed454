const express = require('express');
const { check } = require('express-validator');
const {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  bulkUpdateUsers,
  bulkDeleteUsers,
  toggleUserStatus,
  getUserActivityHistory,
  getUserStats,
  exportUsers,
  verifyUser,
  suspendUser,
  unsuspendUser
} = require('../../controllers/admin/users');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin user routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get all users with filtering, sorting, and pagination
router.get('/', getAllUsers);

// Get user statistics
router.get('/stats', getUserStats);

// Export users data
router.get('/export', exportUsers);

// Create new user
router.post('/', [
  check('firstName', 'First name is required').not().isEmpty(),
  check('lastName', 'Last name is required').not().isEmpty(),
  check('email', 'Please include a valid email').isEmail(),
  check('mobile', 'Mobile number is required').not().isEmpty(),
  check('role', 'Role is required').isIn(['buyer', 'seller', 'admin']),
], createUser);

// Bulk operations
router.post('/bulk-update', [
  check('userIds', 'User IDs array is required').isArray(),
  check('action', 'Action is required').not().isEmpty(),
], bulkUpdateUsers);

router.post('/bulk-delete', [
  check('userIds', 'User IDs array is required').isArray(),
], bulkDeleteUsers);

// Individual user operations
router.get('/:id', getUserById);
router.put('/:id', updateUser);
router.delete('/:id', deleteUser);

// User status management
router.put('/:id/toggle-status', toggleUserStatus);
router.put('/:id/verify', verifyUser);
router.put('/:id/suspend', suspendUser);
router.put('/:id/unsuspend', unsuspendUser);

// User activity history
router.get('/:id/activity', getUserActivityHistory);

module.exports = router;
