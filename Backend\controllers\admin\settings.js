const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');

// Mock settings model - In a real application, you would have a Settings model
// For now, we'll use a simple object to store settings
let platformSettings = {
  general: {
    siteName: 'XOSportsHub',
    siteDescription: 'Premium Sports Content Marketplace',
    contactEmail: '<EMAIL>',
    supportEmail: '<EMAIL>',
    timezone: 'UTC',
    language: 'en',
    currency: 'USD'
  },
  financial: {
    platformCommission: 15, // percentage
    sellerPayout: 85, // percentage
    minimumPayout: 50, // minimum amount for payout
    processingFee: 2.9, // percentage
    payoutSchedule: 'weekly', // daily, weekly, monthly
    taxRate: 0, // percentage
    refundPolicy: 30 // days
  },
  email: {
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpUser: '',
    smtpPassword: '',
    fromEmail: '<EMAIL>',
    fromName: 'XOSportsHub',
    enableNotifications: true
  },
  security: {
    sessionTimeout: 3600, // seconds
    passwordExpiry: 90, // days
    maxLoginAttempts: 5,
    lockoutDuration: 900, // seconds (15 minutes)
    twoFactorAuth: false,
    ipWhitelist: []
  },
  system: {
    maxFileSize: 100, // MB
    allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'mp4', 'mov', 'avi'],
    maintenanceMode: false,
    debugMode: false,
    cacheEnabled: true,
    backupFrequency: 'daily'
  }
};

// Settings history for versioning
let settingsHistory = [];

// @desc    Get all settings
// @route   GET /api/admin/settings
// @access  Private/Admin
exports.getAllSettings = async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      data: platformSettings
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update general settings
// @route   PUT /api/admin/settings
// @access  Private/Admin
exports.updateSettings = async (req, res, next) => {
  try {
    const { category, settings } = req.body;

    // Save current settings to history
    settingsHistory.push({
      timestamp: new Date(),
      updatedBy: req.user.id,
      previousSettings: JSON.parse(JSON.stringify(platformSettings)),
      changes: { category, settings }
    });

    // Update settings
    if (platformSettings[category]) {
      platformSettings[category] = { ...platformSettings[category], ...settings };
    } else {
      return next(new ErrorResponse('Invalid settings category', 400));
    }

    res.status(200).json({
      success: true,
      data: platformSettings,
      message: `${category} settings updated successfully`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get financial settings
// @route   GET /api/admin/settings/financial
// @access  Private/Admin
exports.getFinancialSettings = async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      data: platformSettings.financial
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update financial settings
// @route   PUT /api/admin/settings/financial
// @access  Private/Admin
exports.updateFinancialSettings = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    // Save current settings to history
    settingsHistory.push({
      timestamp: new Date(),
      updatedBy: req.user.id,
      previousSettings: JSON.parse(JSON.stringify(platformSettings.financial)),
      changes: req.body
    });

    // Update financial settings
    platformSettings.financial = { ...platformSettings.financial, ...req.body };

    res.status(200).json({
      success: true,
      data: platformSettings.financial,
      message: 'Financial settings updated successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get email settings
// @route   GET /api/admin/settings/email
// @access  Private/Admin
exports.getEmailSettings = async (req, res, next) => {
  try {
    // Don't expose sensitive information like passwords
    const emailSettings = { ...platformSettings.email };
    delete emailSettings.smtpPassword;

    res.status(200).json({
      success: true,
      data: emailSettings
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update email settings
// @route   PUT /api/admin/settings/email
// @access  Private/Admin
exports.updateEmailSettings = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    // Save current settings to history
    settingsHistory.push({
      timestamp: new Date(),
      updatedBy: req.user.id,
      previousSettings: JSON.parse(JSON.stringify(platformSettings.email)),
      changes: req.body
    });

    // Update email settings
    platformSettings.email = { ...platformSettings.email, ...req.body };

    // Don't expose sensitive information in response
    const responseSettings = { ...platformSettings.email };
    delete responseSettings.smtpPassword;

    res.status(200).json({
      success: true,
      data: responseSettings,
      message: 'Email settings updated successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get security settings
// @route   GET /api/admin/settings/security
// @access  Private/Admin
exports.getSecuritySettings = async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      data: platformSettings.security
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update security settings
// @route   PUT /api/admin/settings/security
// @access  Private/Admin
exports.updateSecuritySettings = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    // Save current settings to history
    settingsHistory.push({
      timestamp: new Date(),
      updatedBy: req.user.id,
      previousSettings: JSON.parse(JSON.stringify(platformSettings.security)),
      changes: req.body
    });

    // Update security settings
    platformSettings.security = { ...platformSettings.security, ...req.body };

    res.status(200).json({
      success: true,
      data: platformSettings.security,
      message: 'Security settings updated successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get system settings
// @route   GET /api/admin/settings/system
// @access  Private/Admin
exports.getSystemSettings = async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      data: platformSettings.system
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update system settings
// @route   PUT /api/admin/settings/system
// @access  Private/Admin
exports.updateSystemSettings = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    // Save current settings to history
    settingsHistory.push({
      timestamp: new Date(),
      updatedBy: req.user.id,
      previousSettings: JSON.parse(JSON.stringify(platformSettings.system)),
      changes: req.body
    });

    // Update system settings
    platformSettings.system = { ...platformSettings.system, ...req.body };

    res.status(200).json({
      success: true,
      data: platformSettings.system,
      message: 'System settings updated successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Reset settings to default
// @route   POST /api/admin/settings/reset
// @access  Private/Admin
exports.resetSettings = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { category } = req.body;

    // Save current settings to history before reset
    settingsHistory.push({
      timestamp: new Date(),
      updatedBy: req.user.id,
      previousSettings: JSON.parse(JSON.stringify(platformSettings)),
      changes: { action: 'reset', category }
    });

    // Default settings
    const defaultSettings = {
      general: {
        siteName: 'XOSportsHub',
        siteDescription: 'Premium Sports Content Marketplace',
        contactEmail: '<EMAIL>',
        supportEmail: '<EMAIL>',
        timezone: 'UTC',
        language: 'en',
        currency: 'USD'
      },
      financial: {
        platformCommission: 15,
        sellerPayout: 85,
        minimumPayout: 50,
        processingFee: 2.9,
        payoutSchedule: 'weekly',
        taxRate: 0,
        refundPolicy: 30
      },
      email: {
        smtpHost: 'smtp.gmail.com',
        smtpPort: 587,
        smtpUser: '',
        smtpPassword: '',
        fromEmail: '<EMAIL>',
        fromName: 'XOSportsHub',
        enableNotifications: true
      },
      security: {
        sessionTimeout: 3600,
        passwordExpiry: 90,
        maxLoginAttempts: 5,
        lockoutDuration: 900,
        twoFactorAuth: false,
        ipWhitelist: []
      },
      system: {
        maxFileSize: 100,
        allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'mp4', 'mov', 'avi'],
        maintenanceMode: false,
        debugMode: false,
        cacheEnabled: true,
        backupFrequency: 'daily'
      }
    };

    if (category === 'all') {
      platformSettings = JSON.parse(JSON.stringify(defaultSettings));
    } else if (defaultSettings[category]) {
      platformSettings[category] = JSON.parse(JSON.stringify(defaultSettings[category]));
    } else {
      return next(new ErrorResponse('Invalid settings category', 400));
    }

    res.status(200).json({
      success: true,
      data: platformSettings,
      message: `${category === 'all' ? 'All' : category} settings reset to default successfully`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export settings
// @route   GET /api/admin/settings/export
// @access  Private/Admin
exports.exportSettings = async (req, res, next) => {
  try {
    // Create export data with timestamp
    const exportData = {
      exportedAt: new Date(),
      exportedBy: req.user.id,
      settings: platformSettings,
      version: '1.0'
    };

    res.status(200).json({
      success: true,
      data: exportData
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Import settings
// @route   POST /api/admin/settings/import
// @access  Private/Admin
exports.importSettings = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { settings } = req.body;

    // Validate settings structure
    const requiredCategories = ['general', 'financial', 'email', 'security', 'system'];
    for (const category of requiredCategories) {
      if (!settings[category]) {
        return next(new ErrorResponse(`Missing required settings category: ${category}`, 400));
      }
    }

    // Save current settings to history before import
    settingsHistory.push({
      timestamp: new Date(),
      updatedBy: req.user.id,
      previousSettings: JSON.parse(JSON.stringify(platformSettings)),
      changes: { action: 'import', settings }
    });

    // Import settings
    platformSettings = JSON.parse(JSON.stringify(settings));

    res.status(200).json({
      success: true,
      data: platformSettings,
      message: 'Settings imported successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get settings history
// @route   GET /api/admin/settings/history
// @access  Private/Admin
exports.getSettingsHistory = async (req, res, next) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    // Simple pagination for history
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const paginatedHistory = settingsHistory
      .slice()
      .reverse() // Most recent first
      .slice(skip, skip + parseInt(limit));

    res.status(200).json({
      success: true,
      data: {
        history: paginatedHistory,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(settingsHistory.length / parseInt(limit)),
          total: settingsHistory.length,
          limit: parseInt(limit)
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Restore settings from history
// @route   POST /api/admin/settings/restore/:id
// @access  Private/Admin
exports.restoreSettings = async (req, res, next) => {
  try {
    const historyIndex = parseInt(req.params.id);

    if (historyIndex < 0 || historyIndex >= settingsHistory.length) {
      return next(new ErrorResponse('Invalid history entry', 404));
    }

    const historyEntry = settingsHistory[historyIndex];

    // Save current settings to history before restore
    settingsHistory.push({
      timestamp: new Date(),
      updatedBy: req.user.id,
      previousSettings: JSON.parse(JSON.stringify(platformSettings)),
      changes: { action: 'restore', historyIndex }
    });

    // Restore settings
    platformSettings = JSON.parse(JSON.stringify(historyEntry.previousSettings));

    res.status(200).json({
      success: true,
      data: platformSettings,
      message: 'Settings restored successfully'
    });
  } catch (err) {
    next(err);
  }
};
